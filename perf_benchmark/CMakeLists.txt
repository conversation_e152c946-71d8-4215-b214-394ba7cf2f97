cmake_minimum_required(VERSION 3.10)
project(PerfBenchmark CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find ZeroMQ (libzmq)
find_package(ZeroMQ REQUIRED)

# Find NNG
find_package(nng REQUIRED)

add_executable(latency_test latency_test.cc)

# Include directories
target_include_directories(latency_test PRIVATE
    /home/<USER>/cppzmq/include
    /home/<USER>/libzmq/include
    /home/<USER>/nng/include
)

# Link libraries
target_link_libraries(latency_test PRIVATE
    /home/<USER>/libzmq/lib64/libzmq.so
    /home/<USER>/nng/lib64/libnng.so
    pthread
)