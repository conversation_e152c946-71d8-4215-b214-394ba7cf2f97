# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /home/<USER>/cmake/bin/cmake

# The command to remove a file.
RM = /home/<USER>/cmake/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /trunk/hj/hybrid_kit_shm-enflame-0311/perf_benchmark

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /trunk/hj/hybrid_kit_shm-enflame-0311/perf_benchmark/build

# Include any dependencies generated for this target.
include CMakeFiles/latency_test.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/latency_test.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/latency_test.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/latency_test.dir/flags.make

CMakeFiles/latency_test.dir/codegen:
.PHONY : CMakeFiles/latency_test.dir/codegen

CMakeFiles/latency_test.dir/latency_test.cc.o: CMakeFiles/latency_test.dir/flags.make
CMakeFiles/latency_test.dir/latency_test.cc.o: /trunk/hj/hybrid_kit_shm-enflame-0311/perf_benchmark/latency_test.cc
CMakeFiles/latency_test.dir/latency_test.cc.o: CMakeFiles/latency_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/trunk/hj/hybrid_kit_shm-enflame-0311/perf_benchmark/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/latency_test.dir/latency_test.cc.o"
	/opt/rh/devtoolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/latency_test.dir/latency_test.cc.o -MF CMakeFiles/latency_test.dir/latency_test.cc.o.d -o CMakeFiles/latency_test.dir/latency_test.cc.o -c /trunk/hj/hybrid_kit_shm-enflame-0311/perf_benchmark/latency_test.cc

CMakeFiles/latency_test.dir/latency_test.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/latency_test.dir/latency_test.cc.i"
	/opt/rh/devtoolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /trunk/hj/hybrid_kit_shm-enflame-0311/perf_benchmark/latency_test.cc > CMakeFiles/latency_test.dir/latency_test.cc.i

CMakeFiles/latency_test.dir/latency_test.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/latency_test.dir/latency_test.cc.s"
	/opt/rh/devtoolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /trunk/hj/hybrid_kit_shm-enflame-0311/perf_benchmark/latency_test.cc -o CMakeFiles/latency_test.dir/latency_test.cc.s

# Object files for target latency_test
latency_test_OBJECTS = \
"CMakeFiles/latency_test.dir/latency_test.cc.o"

# External object files for target latency_test
latency_test_EXTERNAL_OBJECTS =

latency_test: CMakeFiles/latency_test.dir/latency_test.cc.o
latency_test: CMakeFiles/latency_test.dir/build.make
latency_test: /home/<USER>/libzmq/lib64/libzmq.so
latency_test: /home/<USER>/nng/lib64/libnng.so
latency_test: CMakeFiles/latency_test.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/trunk/hj/hybrid_kit_shm-enflame-0311/perf_benchmark/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable latency_test"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/latency_test.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/latency_test.dir/build: latency_test
.PHONY : CMakeFiles/latency_test.dir/build

CMakeFiles/latency_test.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/latency_test.dir/cmake_clean.cmake
.PHONY : CMakeFiles/latency_test.dir/clean

CMakeFiles/latency_test.dir/depend:
	cd /trunk/hj/hybrid_kit_shm-enflame-0311/perf_benchmark/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /trunk/hj/hybrid_kit_shm-enflame-0311/perf_benchmark /trunk/hj/hybrid_kit_shm-enflame-0311/perf_benchmark /trunk/hj/hybrid_kit_shm-enflame-0311/perf_benchmark/build /trunk/hj/hybrid_kit_shm-enflame-0311/perf_benchmark/build /trunk/hj/hybrid_kit_shm-enflame-0311/perf_benchmark/build/CMakeFiles/latency_test.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/latency_test.dir/depend

