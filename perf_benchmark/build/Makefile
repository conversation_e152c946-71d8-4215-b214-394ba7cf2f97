# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /home/<USER>/cmake/bin/cmake

# The command to remove a file.
RM = /home/<USER>/cmake/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /trunk/hj/hybrid_kit_shm-enflame-0311/perf_benchmark

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /trunk/hj/hybrid_kit_shm-enflame-0311/perf_benchmark/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/home/<USER>/cmake/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/home/<USER>/cmake/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /trunk/hj/hybrid_kit_shm-enflame-0311/perf_benchmark/build/CMakeFiles /trunk/hj/hybrid_kit_shm-enflame-0311/perf_benchmark/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /trunk/hj/hybrid_kit_shm-enflame-0311/perf_benchmark/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named latency_test

# Build rule for target.
latency_test: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 latency_test
.PHONY : latency_test

# fast build rule for target.
latency_test/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/latency_test.dir/build.make CMakeFiles/latency_test.dir/build
.PHONY : latency_test/fast

latency_test.o: latency_test.cc.o
.PHONY : latency_test.o

# target to build an object file
latency_test.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/latency_test.dir/build.make CMakeFiles/latency_test.dir/latency_test.cc.o
.PHONY : latency_test.cc.o

latency_test.i: latency_test.cc.i
.PHONY : latency_test.i

# target to preprocess a source file
latency_test.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/latency_test.dir/build.make CMakeFiles/latency_test.dir/latency_test.cc.i
.PHONY : latency_test.cc.i

latency_test.s: latency_test.cc.s
.PHONY : latency_test.s

# target to generate assembly for a file
latency_test.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/latency_test.dir/build.make CMakeFiles/latency_test.dir/latency_test.cc.s
.PHONY : latency_test.cc.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... latency_test"
	@echo "... latency_test.o"
	@echo "... latency_test.i"
	@echo "... latency_test.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

