#include <iostream>
#include <string>
#include <vector>
#include <chrono>
#include <stdexcept>
#include <iomanip>
#include <numeric>
#include <algorithm>

// ZMQ C++ wrapper
#include <zmq.hpp>

// NNG C library
#include <nng/nng.h>
#include <nng/protocol/reqrep0/req.h>
#include <nng/protocol/reqrep0/rep.h>
#include <nng/transport/tcp/tcp.h>
#include <nng/transport/ipc/ipc.h>

struct Args {
    std::string role;
    std::string lib;
    std::string url;
    size_t size = 64;
    int count = 100000;
};

void nng_fatal(const char *func, int rv) {
    if (rv != 0) {
        throw std::runtime_error(std::string(func) + ": " + nng_strerror(rv));
    }
}

void print_stats(const std::vector<double>& latencies, double total_duration_ns, int count, size_t size) {
    double total_duration_s = total_duration_ns / 1e9;
    double avg_latency_us = (total_duration_ns / count) / 1e3;
    double msgs_per_second = count / total_duration_s;
    double throughput_mbps = (static_cast<double>(count) * size * 2) / (total_duration_s * 1024 * 1024);

    std::vector<double> sorted_latencies = latencies;
    std::sort(sorted_latencies.begin(), sorted_latencies.end());

    double min_us = sorted_latencies.front() / 1e3;
    double max_us = sorted_latencies.back() / 1e3;
    double median_us = sorted_latencies[count / 2] / 1e3;
    double p99_us = sorted_latencies[static_cast<size_t>(count * 0.99)] / 1e3;
    double p999_us = sorted_latencies[static_cast<size_t>(count * 0.999)] / 1e3;

    std::cout << std::fixed << std::setprecision(2);
    std::cout << "----------------------------------------" << std::endl;
    std::cout << "Message Size: " << size << " bytes" << std::endl;
    std::cout << "Message Count: " << count << std::endl;
    std::cout << "Avg Latency: " << avg_latency_us << " us" << std::endl;
    std::cout << "Min Latency: " << min_us << " us" << std::endl;
    std::cout << "Max Latency: " << max_us << " us" << std::endl;
    std::cout << "Median Latency: " << median_us << " us" << std::endl;
    std::cout << "99th Percentile: " << p99_us << " us" << std::endl;
    std::cout << "99.9th Percentile: " << p999_us << " us" << std::endl;
    std::cout << "Messages/s: " << msgs_per_second << std::endl;
    std::cout << "Throughput: " << throughput_mbps << " MB/s" << std::endl;
    std::cout << "----------------------------------------" << std::endl;
}

void run_zmq_test(const Args& args) {
    std::cout << "--- Running ZMQ Test ---" << std::endl;
    std::cout << "Mode: " << args.role << ", Transport: " << args.url
              << ", MsgSize: " << args.size << ", Count: " << args.count << std::endl;

    zmq::context_t context(1);
    zmq::socket_t socket(context, (args.role == "client" ? ZMQ_REQ : ZMQ_REP));

    if (args.role == "client") {
        socket.connect(args.url);

        std::vector<double> latencies;
        latencies.reserve(args.count);
        std::vector<char> msg_data(args.size, 'a');

        // Warm-up
        zmq::message_t warmup_msg(msg_data.size());
        memcpy(warmup_msg.data(), msg_data.data(), msg_data.size());
        socket.send(warmup_msg, zmq::send_flags::none);
        zmq::message_t warmup_reply;
        auto recvd = socket.recv(warmup_reply);

        auto start_total = std::chrono::high_resolution_clock::now();
        for (int i = 0; i < args.count; ++i) {
            zmq::message_t request(msg_data.size());
            memcpy(request.data(), msg_data.data(), msg_data.size());

            auto start_lap = std::chrono::high_resolution_clock::now();
            socket.send(request, zmq::send_flags::none);
            
            zmq::message_t reply;
            auto recvd = socket.recv(reply);
            auto end_lap = std::chrono::high_resolution_clock::now();

            if (recvd.has_value()) { // Check if recv was successful
                latencies.push_back(std::chrono::duration_cast<std::chrono::nanoseconds>(end_lap - start_lap).count());
            }
        }
        auto end_total = std::chrono::high_resolution_clock::now();
        print_stats(latencies, std::chrono::duration_cast<std::chrono::nanoseconds>(end_total - start_total).count(), args.count, args.size);

    } else { // server
        socket.bind(args.url);
        while (true) {
            zmq::message_t request;
            auto recvd = socket.recv(request);
            if(recvd.has_value()){
                socket.send(request, zmq::send_flags::none);
            }
        }
    }
}

void run_nng_test(const Args& args) {
    std::cout << "--- Running NNG Test ---" << std::endl;
    std::cout << "Mode: " << args.role << ", Transport: " << args.url
              << ", MsgSize: " << args.size << ", Count: " << args.count << std::endl;

    nng_socket sock;
    int rv;

    if (args.role == "client") {
        nng_fatal("nng_req0_open", nng_req0_open(&sock));
        nng_fatal("nng_dial", nng_dial(sock, args.url.c_str(), NULL, 0));

        std::vector<double> latencies;
        latencies.reserve(args.count);
        std::vector<char> msg_data(args.size, 'a');

        // Warm-up
        nng_fatal("nng_send", nng_send(sock, msg_data.data(), msg_data.size(), 0));
        char* warmup_buf = NULL;
        size_t warmup_size;
        nng_fatal("nng_recv", nng_recv(sock, &warmup_buf, &warmup_size, NNG_FLAG_ALLOC));
        nng_free(warmup_buf, warmup_size);
        
        auto start_total = std::chrono::high_resolution_clock::now();
        for (int i = 0; i < args.count; i++) {
            auto start_lap = std::chrono::high_resolution_clock::now();
            
            nng_send(sock, msg_data.data(), msg_data.size(), 0);

            char *buf = NULL;
            size_t sz;
            nng_recv(sock, &buf, &sz, NNG_FLAG_ALLOC);
            
            auto end_lap = std::chrono::high_resolution_clock::now();
            nng_free(buf, sz);
            latencies.push_back(std::chrono::duration_cast<std::chrono::nanoseconds>(end_lap - start_lap).count());
        }
        auto end_total = std::chrono::high_resolution_clock::now();
        print_stats(latencies, std::chrono::duration_cast<std::chrono::nanoseconds>(end_total - start_total).count(), args.count, args.size);

    } else { // server
        nng_fatal("nng_rep0_open", nng_rep0_open(&sock));
        nng_socket_set_bool(sock, NNG_OPT_TCP_NODELAY, true);
        nng_fatal("nng_listen", nng_listen(sock, args.url.c_str(), NULL, 0));
        while (true) {
            char *buf = NULL;
            size_t sz;
            rv = nng_recv(sock, &buf, &sz, NNG_FLAG_ALLOC);
            if (rv == 0) {
                nng_send(sock, buf, sz, NNG_FLAG_ALLOC);
            }
        }
    }
    nng_close(sock);
}


int main(int argc, char** argv) {
    Args args;
    try {
        for (int i = 1; i < argc; ++i) {
            std::string arg = argv[i];
            if (arg == "--role" && i + 1 < argc) {
                args.role = argv[++i];
            } else if (arg == "--lib" && i + 1 < argc) {
                args.lib = argv[++i];
            } else if (arg == "--transport" && i + 1 < argc) {
                args.url = argv[++i];
            } else if (arg == "--size" && i + 1 < argc) {
                args.size = std::stoul(argv[++i]);
            } else if (arg == "--count" && i + 1 < argc) {
                args.count = std::stoi(argv[++i]);
            }
        }

        if (args.role.empty() || args.lib.empty() || args.url.empty()) {
            std::cerr << "Usage: " << argv[0] << " --role <client|server> --lib <zmq|nng> --transport <url> [--size <bytes>] [--count <num>]" << std::endl;
            return 1;
        }

        if (args.lib == "zmq") {
            run_zmq_test(args);
        } else if (args.lib == "nng") {
            run_nng_test(args);
        } else {
            std::cerr << "Invalid library specified. Use 'zmq' or 'nng'." << std::endl;
            return 1;
        }

    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}