#!/usr/bin/tcsh -x
setenv HYBRID_ROOT ${PWD}
echo "HYBRID_ROOT   ${HYBRID_ROOT}"

setenv HYBRID_GCC_VERSION 10.3

setenv LD_LIBRARY_PATH ${HYBRID_ROOT}/lib/Linux64_GCC-10.3.0_Release/:${HYBRID_ROOT}/src/thirdparty/install/lib/:${LD_LIBRARY_PATH}

setenv XTOR_ROOT /remote/products/isol/images/xtor/latest/Testing/package

# setenv XTOR_ROOT /remote/products/isol/images/xtor/2025.04/nightly/20250109_48531a68/Testing/package
setenv LD_LIBRARY_PATH ${XTOR_ROOT}/utils/lib:${LD_LIBRARY_PATH}
setenv LD_LIBRARY_PATH ${XTOR_ROOT}/lib/Linux64_GCC-${HYBRID_GCC_VERSION}:${LD_LIBRARY_PATH}
echo "XTOR_ROOT   ${XTOR_ROOT}"


setenv UV_ROOT /remote/products/emu/2023.09_dev/images/nightly/latest_release
echo "UV_ROOT   ${UV_ROOT}"

setenv UVS_HOME /remote/products/simu/handoff/UVS/2023.12.P2/20240221_10075782
setenv UVD_HOME /remote/products/simu/handoff/UVD/2023.12.P2/20240223_10076335
setenv XILINX_HLS /depot/3rd_tools/Vivado/v2022.2/Vitis_HLS/2022.2
setenv XILINX_VIVADO /depot/3rd_tools/Vivado/v2022.2/Vivado/2022.2
setenv PATH /depot/3rd_tools/Vivado/v2022.2/Model_Composer/2022.2/bin:$PATH
source ${XILINX_VIVADO}/settings64.csh

setenv GCC_PATH /depot/opensource/gcc/v10.3.0
setenv QUESTA_PATH /remote/gitlab/compute/sfta/depot/questa_sim/questasim


setenv HW_HOME ${UV_ROOT} 
setenv UMICOM_HOME ${HW_HOME}/platform/U2.2/Emulation/umicom
echo "HW_HOME   ${HW_HOME}"

setenv PATH ${UVD_HOME}/bin:${UVS_HOME}/bin:${GCC_PATH}/bin:${QUESTA_PATH}/bin:${HW_HOME}/bin:${XILINX_HLS}/bin:${XILINX_VIVADO}/bin:$PATH

setenv LD_LIBRARY_PATH ${GCC_PATH}/lib64:${QUESTA_PATH}/linux_x86_64:${UMICOM_HOME}/lib:$LD_LIBRARY_PATH


setenv RLM_LICENSE 5053@hjsh-lic01:5053@hjsh-ft01
setenv VERIFIC_LICENSE_FILE /opt/verific_eval/verific.license
setenv LM_LICENSE_FILE 1717@shrdlab12:1717@hjsh-lic01:1717@10.157.101.204
setenv UV_LICENSE 8273@hjsh-ft01 