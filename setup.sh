export XTOR_ROOT=/remote/products/isol/images/xtor/latest/Testing/package
export UV_ROOT=/remote/products/emu/2023.09_dev/images/nightly/latest_release
export HW_HOME=${UV_ROOT} 
export UMICOM_HOME=${HW_HOME}/platform/U2.2/Emulation/umicom
export HYBRID_ROOT=$(pwd)
echo "HYBRID_ROOT = ${HYBRID_ROOT}"
echo "XTOR_ROOT = ${XTOR_ROOT}"
echo "UV_ROOT = ${UV_ROOT}"

export HYBRID_GCC_VERSION=10.3
export LD_LIBRARY_PATH=${HYBRID_ROOT}/lib/Linux64_GCC-10.3.0_Debug/:${HYBRID_ROOT}/src/thirdparty/install/lib/:${LD_LIBRARY_PATH}

export LD_LIBRARY_PATH=${XTOR_ROOT}/utils/lib:${LD_LIBRARY_PATH}
export LD_LIBRARY_PATH=${XTOR_ROOT}/lib/Linux64_GCC-${HYBRID_GCC_VERSION}:${LD_LIBRARY_PATH}

export UVS_HOME=/remote/products/simu/handoff/UVS/2023.12.P2/20240221_10075782
export UVD_HOME=/remote/products/simu/handoff/UVD/2023.12.P2/20240223_10076335

export PYTHON_PATH=/depot/opensource/python/v3.8.12
export PYTHON_HOME=/remote/projects/xtor/tools/python_tool

export XILINX_HLS=/depot/3rd_tools/Vivado/v2022.2/Vitis_HLS/2022.2
export XILINX_VIVADO=/depot/3rd_tools/Vivado/v2022.2/Vivado/2022.2
export PATH=/depot/3rd_tools/Vivado/v2022.2/Model_Composer/2022.2/bin:$PATH
source ${XILINX_VIVADO}/settings64.sh



#export GCC_PATH=/remote/products/emu/2023.09_dev/images/release/weekly_release/latest/release/Testing/lib/gcc10.3
export GCC_PATH=/depot/opensource/gcc/v10.3.0

export CMAKE_PATH=/depot/opensource/cmake/v3.25.2

export QUESTA_PATH=/remote/gitlab/compute/sfta/depot/questa_sim/questasim

export HW_HOME=${UV_ROOT}
export UMICOM_HOME=${HW_HOME}/platform/U2.2/Emulation/umicom
echo "HW_HOME = ${HW_HOME}"

export PATH=${PYTHON_PATH}/bin:${UVD_HOME}/bin:${UVS_HOME}/bin:${GCC_PATH}/bin:${QUESTA_PATH}/bin:${HW_HOME}/bin:${XILINX_HLS}/bin:${XILINX_VIVADO}/bin:${CMAKE_PATH}/bin:$PATH
export LD_LIBRARY_PATH=${GCC_PATH}/lib64:${QUESTA_PATH}/linux_x86_64:${UMICOM_HOME}/lib:$LD_LIBRARY_PATH

#################################### LICENSE ###############################################
export RLM_LICENSE=5053@hjsh-lic01:5053@hjsh-ft01
export VERIFIC_LICENSE_FILE=/opt/verific_eval/verific.license
export LM_LICENSE_FILE=1717@shrdlab12:1717@hjsh-lic01:1717@10.157.101.204
export UV_LICENSE=8273@hjsh-ft01 

