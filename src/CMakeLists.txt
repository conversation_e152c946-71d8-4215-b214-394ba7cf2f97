cmake_minimum_required(VERSION 3.14)


if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

if(NOT CMAKE_INSTALL_PREFIX)
set(CMAKE_INSTALL_PREFIX ${CMAKE_CURRENT_SOURCE_DIR}/../lib 
    CACHE PATH "Install path" FORCE)
endif()

option(BUILD_SHARED_LIBS "Build shared libraries" ON)
option(USE_FAKE_XTOR "Use fake xtor to test" OFF)


project(hybrid_kit_shm)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED TRUE)

add_compile_definitions(XTOR_SC SC_CPLUSPLUS=201402L)


if(CMAKE_SIZEOF_VOID_P EQUAL 8)
    message(STATUS "64-bit system")
    set(SYSTEMBIT 64)
else()
    message(STATUS "32-bit system")
    set(SYSTEMBIT 32)
endif()

message(STATUS "systemc: ${CMAKE_SYSTEM_NAME}")
message(STATUS "gcc version: ${CMAKE_CXX_COMPILER_VERSION}")
message(STATUS "build type: ${CMAKE_BUILD_TYPE}")

set(LIB_VERSION "${CMAKE_SYSTEM_NAME}${SYSTEMBIT}_GCC-${CMAKE_CXX_COMPILER_VERSION}_${CMAKE_BUILD_TYPE}")

message(STATUS "lib version: ${LIB_VERSION}")

set(PROJECT_ROOT ${CMAKE_CURRENT_SOURCE_DIR})

set(CMAKE_PREFIX_PATH
    ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/install
    ${CMAKE_PREFIX_PATH}
)

find_package(cpp-ipc REQUIRED)
find_package(GTest REQUIRED)

enable_testing()


add_custom_target(build_utests)


add_subdirectory(sslogger)
add_subdirectory(common)
add_subdirectory(axi_common)
add_subdirectory(axi_master)
add_subdirectory(axi_slave)
add_subdirectory(sideband)
add_subdirectory(dispatcher)
