add_library(axi_common
    src/axi_utils.cc
)

if(USE_FAKE_XTOR)
    target_include_directories(axi_common
        PUBLIC
            ${PROJECT_ROOT}/thirdparty/fake_install/include
            ${CMAKE_CURRENT_SOURCE_DIR}/include
    )
else()
    target_include_directories(axi_common
        PUBLIC
            $ENV{XTOR_ROOT}/xtor_axi/c
            $ENV{XTOR_ROOT}/xtor_base/c
            $ENV{XTOR_ROOT}/xtor_sig/c
            ${CMAKE_CURRENT_SOURCE_DIR}/include
    )
endif()



install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
    DESTINATION ${CMAKE_INSTALL_PREFIX}
    FILES_MATCHING PATTERN "*.h"
    PATTERN "src" EXCLUDE
    PATTERN "tests" EXCLUDE
)

install(TARGETS axi_common
    LIBRARY DESTINATION ${CMAKE_INSTALL_PREFIX}/${LIB_VERSION}
    ARCHIVE DESTINATION ${CMAKE_INSTALL_PREFIX}/${LIB_VERSION}
)