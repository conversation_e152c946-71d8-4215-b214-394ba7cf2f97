#ifndef SSLN_HYBRID_AXI_FORMAT_H
#define SSLN_HYBRID_AXI_FORMAT_H

#include <cstdint>
#include "axi_transaction_item.h"
#include "quill/DirectFormatCodec.h"
#include "quill/Utility.h"


template <>
struct fmtquill::formatter<umi_axi::axi_transaction_item> {
    constexpr auto parse(format_parse_context& ctx) { return ctx.begin(); }
    auto format(umi_axi::axi_transaction_item const& item, format_context& ctx) const{
        
        const char* cmd_s = item.cmd == umi_axi::cmd_type_t::WRITE ? "write" : "read";
        const char* burst_s = item.burst == umi_axi::burst_type_t::INCR ? "INCR" : item.burst == umi_axi::burst_type_t::FIXED ?  "FIXED" : "WARP";
        auto out = fmtquill::format_to(ctx.out(), "AXI----------------------->>>\n");
        out = fmtquill::format_to(out, "id={}, cmd={}, addr={:#x}, burst_type={}, size={}, len={}, version={}\n",
        item.id,
        cmd_s,
        item.addr,
        burst_s,
        uint32_t(item.size),
        uint32_t(item.len),
        uint32_t(item.spec_ver)
        );
        out = fmtquill::format_to(out, "data={}\n", quill::utility::to_hex(item.data.data(), item.data.size()));
        out = fmtquill::format_to(out, "byte_enable={}\n", quill::utility::to_hex(item.be.data(), item.be.size()));
        out = fmtquill::format_to(out, "req_user={}\n", quill::utility::to_hex(item.req_user.data(), item.req_user.size()));
        out = fmtquill::format_to(out, "data_user={}\n", quill::utility::to_hex(item.data_user.data(), item.data_user.size()));
        out = fmtquill::format_to(out, "resp_user={}\n", quill::utility::to_hex(item.resp_user.data(), item.resp_user.size()));

        return out;
    }

};

// Codec for axi_transaction_item
template <>
struct quill::Codec<umi_axi::axi_transaction_item> : quill::DirectFormatCodec<umi_axi::axi_transaction_item> {};

#endif