#ifndef SSLN_HYBRID_AXI_UTILS_H_
#define SSLN_HYBRID_AXI_UTILS_H_

#include <cstdint>
#include <memory>
#include "axi_transaction_item.h"

namespace ssln {
namespace hybrid {
namespace axi {


// Metadata for split transactions
struct AxiTransactionMetadata {
    uint32_t axi_id;
    uint64_t address;           // Start address of this transaction
    uint32_t data_len;         // Length of data for this transaction
    uint32_t data_offset;      // Offset in original TLM payload data
    uint32_t streaming_width;  // TLM streaming width
    uint32_t beat_num;        // Number of AXI beats
    umi_axi::cmd_type_t cmd;  // READ or WRITE
    umi_axi::size_type_t size;// AXI size (burst size)
    umi_axi::burst_type_t burst;// AXI burst type
    std::shared_ptr<umi_axi::axi_transaction_item> axi_transaction; // AXI transaction details

};
namespace utils {

uint32_t GetNumberBytes(uint32_t axsize);

uint64_t GetAlignedAddress(uint64_t addr, uint32_t axsize);
uint64_t GetNextAlignedAddress(uint64_t addr, uint32_t axsize); 

uint32_t GetMaxBurstBytes(uint32_t axsize, umi_axi::spec_version_t spec_ver, uint32_t axlen_width=8, uint8_t axsize_width=0);

uint64_t GetNearest4KBoundary(uint64_t address);
uint32_t GetSizeOfDataTransaction(uint32_t axsize, uint32_t axlen);

uint64_t GetLowWrapBoundary(uint64_t axaddr, uint32_t axsize, uint32_t axlen);
uint64_t GetHighWrapBoundary(uint64_t axaddr, uint32_t axsize, uint32_t axlen);
uint32_t GetBurstIndexOfWrap(uint64_t axaddr, uint32_t axsize, uint32_t axlen);

uint8_t GetStartOffset(uint64_t base_address, uint32_t axsize); 
uint8_t GetEndOffset(uint64_t base_address, uint32_t length, uint32_t axsize);

uint64_t GetAlignedAddress2(uint64_t addr, uint32_t number_bytes);
uint64_t GetNextAlignedAddress2(uint64_t addr, uint32_t number_bytes); 
uint32_t GetMaxBurstBeatNum2(uint32_t number_bytes, umi_axi::spec_version_t spec_ver, uint32_t axlen_width, uint8_t axsize_width=0);
uint32_t GetMaxBurstBytes2(uint32_t number_bytes, umi_axi::spec_version_t spec_ver, uint32_t axlen_width=8, uint8_t axsize_width=0);
uint32_t GetSizeOfDataTransaction2(uint32_t number_bytes, uint32_t len);
uint64_t GetLowWrapBoundary2(uint64_t axaddr, uint32_t number_bytes, uint32_t len);
uint64_t GetHighWrapBoundary2(uint64_t axaddr, uint32_t number_bytes, uint32_t len);
uint32_t GetBurstIndexOfWrap2(uint64_t axaddr, uint32_t number_bytes, uint32_t len);
uint8_t GetStartOffset2(uint64_t address, uint32_t number_bytes); 
uint8_t GetEndOffset2(uint64_t address, uint32_t length, uint32_t number_bytes);
uint32_t GetWrapForwardLen2(uint64_t axaddr, uint32_t number_bytes, uint32_t len);
uint32_t GetWrapBackwardLen2(uint64_t axaddr, uint32_t number_bytes, uint32_t len);
uint32_t GetWrapForwardtBeatNumber2(uint32_t axaddr, uint32_t number_bytes, uint32_t len);
uint32_t GetWrapBackwardBeatNumber2(uint32_t axaddr, uint32_t number_bytes, uint32_t len);

uint32_t GetIncrBeatNum2(uint64_t address, uint32_t data_len, uint32_t data_width);
uint32_t GetFixedBeatNum2(uint32_t data_len, uint32_t streaming_width);

bool IsPowerOfTwo(uint32_t n);
uint8_t GetAxsize(uint32_t number_bytes);

} /* namespace ssln::hybrid::axi::utils */

} /* namespace ssln::hybrid::axi */
} /* namespace ssln::hybrid */
} /* namespace ssln */

#endif