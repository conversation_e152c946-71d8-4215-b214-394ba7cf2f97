#include "axi_utils.h"
#include <cmath>
#include <stdexcept>

namespace ssln::hybrid::axi::utils {

bool IsPowerOfTwo(uint32_t n) { return (n != 0) && ((n & (n - 1)) == 0); }

uint32_t GetNumberBytes(uint32_t axsize) {
  return 1UL<<axsize;
}

uint64_t GetAlignedAddress(uint64_t addr, uint32_t axsize) {
    uint32_t number_bytes = GetNumberBytes(axsize);
    return (addr/number_bytes) * number_bytes;
}

uint64_t GetAlignedAddress2(uint64_t addr, uint32_t number_bytes) {
    return (addr/number_bytes) * number_bytes;
}

uint64_t GetNextAlignedAddress(uint64_t addr, uint32_t axsize) {
    uint32_t number_bytes = GetNumberBytes(axsize);
    return GetAlignedAddress(addr, number_bytes) + number_bytes;
}

uint64_t GetNextAlignedAddress2(uint64_t addr, uint32_t number_bytes) {
    return GetAlignedAddress2(addr, number_bytes) + number_bytes;
}

// number_bytes in bytes
uint32_t GetMaxBurstBytes(uint32_t axsize, umi_axi::spec_version_t spec_ver, uint32_t axlen_width, uint8_t axsize_width) {
    const uint32_t MAX_AXI_TRANSACTION_SIZE = 4096; // 4KB
    uint32_t max_burst_len;
    uint32_t effective_data_width;
    uint32_t number_bytes = GetNumberBytes(axsize);

    // Determine max_burst_len based on spec version and axlen_width
    if (spec_ver == umi_axi::spec_version_t::AMBA3) {
        max_burst_len = std::min(16, 1<<axlen_width);
    } else { // AXI4
        max_burst_len = std::min(256, 1<<axlen_width);
    }

    // Determine effective_data_width
    if (axsize_width == 0) {
        effective_data_width = number_bytes ; 
    } else {
        effective_data_width = 1<<axsize_width;
    }

    // Calculate max burst bytes
    uint32_t max_burst_bytes = max_burst_len * effective_data_width;

    // Ensure max_burst_bytes doesn't exceed 4KB
    return std::min(max_burst_bytes, MAX_AXI_TRANSACTION_SIZE);
}

uint32_t GetMaxBurstBeatNum2(uint32_t number_bytes, umi_axi::spec_version_t spec_ver, uint32_t axlen_width, uint8_t axsize_width) {
    uint32_t max_burst_len; 
    // Determine max_burst_len based on spec version and axlen_width
    if (spec_ver == umi_axi::spec_version_t::AMBA3) {
        max_burst_len = std::min(16, 1<<axlen_width);
    } else { // AXI4
        max_burst_len = std::min(256, 1<<axlen_width);
    }
    uint32_t burst_len_4K = 4096 / number_bytes;
    max_burst_len = std::min(max_burst_len, burst_len_4K);
    return max_burst_len;
}

uint32_t GetMaxBurstBytes2(uint32_t number_bytes, umi_axi::spec_version_t spec_ver, uint32_t axlen_width, uint8_t axsize_width) {
    const uint32_t MAX_AXI_TRANSACTION_SIZE = 4096; // 4KB
    uint32_t max_burst_len;
    uint32_t effective_data_width;

    // Determine max_burst_len based on spec version and axlen_width
    if (spec_ver == umi_axi::spec_version_t::AMBA3) {
        max_burst_len = std::min(16, 1<<axlen_width);
    } else { // AXI4
        max_burst_len = std::min(256, 1<<axlen_width);
    }

    // Determine effective_data_width
    if (axsize_width == 0) {
        effective_data_width = number_bytes ; 
    } else {
        effective_data_width = 1<<axsize_width;
    }

    // Calculate max burst bytes
    uint32_t max_burst_bytes = max_burst_len * effective_data_width;

    // Ensure max_burst_bytes doesn't exceed 4KB
    return std::min(max_burst_bytes, MAX_AXI_TRANSACTION_SIZE);
}


uint8_t GetAxsize(uint32_t number_bytes) {
    uint8_t axsize = 0;
    while (number_bytes > 1) {
        number_bytes >>= 1;
        axsize++;
    }
    return axsize;
}

/*
* dataWidth: should be the power of 2
*/
uint8_t GetStartOffset(uint64_t address, uint32_t axsize) {
    uint32_t number_bytes = GetNumberBytes(axsize);
    return static_cast<uint8_t>(address & (number_bytes - 1));
}

uint8_t GetStartOffset2(uint64_t address, uint32_t number_bytes) {
    return static_cast<uint8_t>(address & (number_bytes - 1));
}

/*
* dataWidth: should be the power of 2
*/
uint8_t GetEndOffset(uint64_t base_address, uint32_t length, uint32_t axsize) {
    uint32_t number_bytes = GetNumberBytes(axsize);
    uint64_t endAddress = base_address + length - 1;
    //uint64_t alignedEndAddress = (endAddress  | (dataWidth - 1));
    //return static_cast<uint8_t>(alignedEndAddress - endAddress);
    return static_cast<uint8_t>(number_bytes - ((endAddress) & (number_bytes - 1)) - 1);
}

uint8_t GetEndOffset2(uint64_t base_address, uint32_t length, uint32_t number_bytes) {
    uint64_t endAddress = base_address + length - 1;
    return static_cast<uint8_t>(number_bytes - ((endAddress) & (number_bytes - 1)) - 1);

}

uint64_t GetNearest4KBoundary(uint64_t address) {
  uint64_t next_4k_boundary;
    static constexpr uint64_t BOUNDARY_4K = 0x1000; 
    static constexpr uint64_t MASK_4K = 0xfff;
    next_4k_boundary = (address + BOUNDARY_4K) & ~MASK_4K;
    return next_4k_boundary;
}

uint32_t GetSizeOfDataTransaction(uint32_t axsize, uint32_t axlen) {
  uint32_t number_bytes = GetNumberBytes(axsize);
  uint32_t burst_length = axlen+1;
  return number_bytes * burst_length;
}

uint32_t GetSizeOfDataTransaction2(uint32_t number_bytes, uint32_t len) {
  return number_bytes * len;
}

uint64_t GetLowWrapBoundary(uint64_t axaddr, uint32_t axsize, uint32_t axlen) {
  uint32_t dtsize = GetSizeOfDataTransaction(axsize, axlen);
  uint64_t wrap_boundary = (axaddr / dtsize) * (dtsize);
  return wrap_boundary;
}
uint64_t GetHighWrapBoundary(uint64_t axaddr, uint32_t axsize, uint32_t axlen) {
  uint64_t next_wrap_boundary;
  uint32_t dtsize = GetSizeOfDataTransaction(axsize, axlen);
  next_wrap_boundary = GetLowWrapBoundary(axaddr, axsize, axlen) + dtsize;
  return next_wrap_boundary;
}

uint32_t GetBurstIndexOfWrap(uint64_t axaddr, uint32_t axsize, uint32_t axlen) {
  uint32_t number_bytes = GetNumberBytes(axsize);
  uint32_t burst_length = axlen+1;
  uint64_t next_wrap_boundary = GetHighWrapBoundary(axaddr, axsize, axlen);
  uint32_t beat_number = (next_wrap_boundary - axaddr)/number_bytes;
  return beat_number+1;
}

uint64_t GetLowWrapBoundary2(uint64_t axaddr, uint32_t number_bytes, uint32_t len) {
  uint32_t dtsize = GetSizeOfDataTransaction2(number_bytes, len);
  uint64_t wrap_boundary = (axaddr / dtsize) * (dtsize);
  return wrap_boundary;
}
uint64_t GetHighWrapBoundary2(uint64_t axaddr, uint32_t number_bytes, uint32_t len) {
  uint64_t next_wrap_boundary;
  uint32_t dtsize = GetSizeOfDataTransaction2(number_bytes, len);
  next_wrap_boundary = GetLowWrapBoundary2(axaddr, number_bytes, len) + dtsize;
  return next_wrap_boundary;
}

uint32_t GetBurstIndexOfWrap2(uint64_t axaddr, uint32_t number_bytes, uint32_t len) {
  uint64_t next_wrap_boundary = GetHighWrapBoundary2(axaddr, number_bytes, len);
  uint32_t beat_number = (next_wrap_boundary - axaddr)/number_bytes;
  return beat_number+1;
}


uint32_t GetWrapForwardLen2(uint64_t axaddr, uint32_t number_bytes, uint32_t len) {
  uint64_t next_wrap_boundary = GetHighWrapBoundary2(axaddr, number_bytes, len);
  return static_cast<uint32_t >(next_wrap_boundary - axaddr);
}

uint32_t GetWrapBackwardLen2(uint64_t axaddr, uint32_t number_bytes, uint32_t len) {
  uint64_t low_wrap_boundary = GetLowWrapBoundary2(axaddr, number_bytes, len);
  return static_cast<uint32_t >(axaddr - low_wrap_boundary);
}

uint32_t GetWrapForwardtBeatNumber2(uint32_t axaddr, uint32_t number_bytes, uint32_t len) {
  uint32_t wrap_len = GetWrapForwardLen2(axaddr, number_bytes, len);
  return wrap_len/number_bytes;
}
uint32_t GetWrapBackwardBeatNumber2(uint32_t axaddr, uint32_t number_bytes, uint32_t len) {
  uint32_t wrap_len = GetWrapBackwardLen2(axaddr, number_bytes, len);
  return wrap_len/number_bytes;
}

uint32_t GetIncrBeatNum2(uint64_t address, uint32_t data_len, uint32_t data_width) {
    uint64_t start_offset = GetStartOffset2(address, data_width);
    uint64_t end_offset = GetEndOffset2(address, data_len, data_width);
    return (start_offset + data_len + end_offset) / data_width;
}

uint32_t GetFixedBeatNum2(uint32_t data_len, uint32_t streaming_width) {
    return (data_len + streaming_width - 1) / streaming_width;
}

} /* namespace ssln::hybrid::axi::utils */
