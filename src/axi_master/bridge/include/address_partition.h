#ifndef SSLN_HYBRID_ADDRESS_PARTITION_H_
#define SSLN_HYBRID_ADDRESS_PARTITION_H_

#include <vector>
#include <cstdint>
#include "transaction_manager.h"
#include "axi_utils.h"

namespace ssln {
namespace hybrid {
namespace axi {

enum class PartitionType {
    kIncr = 1,
    kFixed,
    kMultiIncr
};

struct AddressRange {
    uint64_t start;
    uint32_t len;
    uint32_t data_index;
    PartitionType part_type;
};

class AddressPartition {
public:
    AddressPartition(
        uint32_t data_width,
        umi_axi::spec_version_t spec_ver = umi_axi::spec_version_t::AMBA4,
        uint32_t axlen_width = 8,
        uint8_t axsize_width = 0):
        data_width_(data_width)
        , spec_ver_(spec_ver)
        , axlen_width_(axlen_width)
        , axsize_width_(axsize_width) {
        start_ = 0;
        len_ = 0;
        streaming_width_ = 0;
    }

    // Main partition function
    std::vector<AxiTransactionMetadata> Partition();
    std::vector<AddressRange> TlmPartition();

    void SetAddress(uint64_t address);
    void SetDataLength(uint32_t data_length);
    void SetStreamingWidth(uint32_t streaming_width);
    void SetAll(uint64_t address, uint32_t data_length, uint32_t streaming_width);
    
private:
    // Helper functions
    bool IncrPartition(const AddressRange& range, std::vector<AxiTransactionMetadata>& result);
    bool FixedPartition(const std::vector<AddressRange>& ranges, std::vector<AxiTransactionMetadata>& result);
    PartitionType GetPartitionType() const;

    // Calculate max burst parameters
    uint32_t GetMaxBurstLength() const;
    uint32_t GetMaxBurstBytes() const;

    // Member variables
    uint64_t start_;
    uint32_t len_;
    uint32_t streaming_width_;
    uint32_t data_width_;
    umi_axi::spec_version_t spec_ver_;
    uint32_t axlen_width_;
    uint8_t axsize_width_;

    // Constants
    static constexpr uint32_t BOUNDARY_4K = 0x1000;
    static constexpr uint32_t MASK_4K = 0xfff;
};

}  // namespace axi
}  // namespace hybrid
}  // namespace ssln

#endif  // SSLN_HYBRID_ADDRESS_PARTITION_H_ 
