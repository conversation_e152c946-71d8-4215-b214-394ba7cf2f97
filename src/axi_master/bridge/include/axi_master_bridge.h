#ifndef SSLN_HYBRID_AXI_MASTER_BRIDGE_H_
#define SSLN_HYBRID_AXI_MASTER_BRIDGE_H_

#include <atomic>
#include <memory>
#include <string>
#include <thread>
#include <unordered_map>
#include <vector>
#include <readerwriterqueue.h>
#include <systemc>
#include "axi_transaction_item.h"
#include "axi_master_proxy.h"
#include "tlm_payload.h"
#include "transaction_manager.h"
#include "axi_utils.h"
#include "async_event.h"
#include "ssln_profiling.h"
#include "dispatcher/include/dispatcher.h"
#include "dispatcher/include/i_endpoint.h"
namespace ssln {
namespace hybrid {
namespace axi {

// Forward declarations
class TransactionManager;
class ResponseMerger;
class AddressPartition;

class AxiMasterBridge: public sc_core::sc_module, public IEndpoint {
    
public:
    SC_HAS_PROCESS(AxiMasterBridge);
    explicit AxiMasterBridge(
        sc_core::sc_module_name name,
        Dispatcher* dispatcher,
        uint32_t comp_id,
        uint32_t dest_id,
        const std::string& bfm_hdl_path);

    ~AxiMasterBridge();

    // Disabled copy/move operations
    AxiMasterBridge(const AxiMasterBridge&) = delete;
    AxiMasterBridge& operator=(const AxiMasterBridge&) = delete;
    AxiMasterBridge(AxiMasterBridge&&) = delete;
    AxiMasterBridge& operator=(AxiMasterBridge&&) = delete;

    // Handle response from AXI
    void HandleResponse(void* response);


 // IEndpoint interface implementation
 bool handle_data(const void* data, size_t size) override;
 uint32_t get_component_id() const override;

private:
    // Process request from queue. Not directly using Cpp thread to process requests is because it requires waiting for the proxy to initialize and obtain parameters
    void ScProcessRequest();

    // Create AXI transaction from metadata
    std::shared_ptr<umi_axi::axi_transaction_item> CreateAxiTransaction(
        const AxiTransactionMetadata& metadata,
        const TlmPayload* payload,
        uint64_t axi_id);

    // Fill data and strobe for fixed burst
    void FillDataAndStrobeForFixedBurst(
        umi_axi::axi_transaction_item* transaction,
        const AxiTransactionMetadata& metadata,
        const TlmPayload* payload);

    // Fill data and strobe for incremental burst
    void FillDataAndStrobeForIncrBurst(
        umi_axi::axi_transaction_item* transaction,
        const AxiTransactionMetadata& metadata,
        const TlmPayload* payload);

    // Send response back to proxy
    void SendResponse(TlmPayload* resp_payload);

    // AXI configuration parameters
    umi_axi::spec_version_t spec_ver_;
    uint32_t data_width_;
    uint32_t axlen_width_;
    uint8_t axsize_width_;

    uint32_t req_user_width_;
    uint32_t data_w_user_width_;
    uint32_t resp_w_user_width_;
    uint32_t data_r_user_width_;

    // Helper function to initialize AXI configuration
    void InitAxiConfig();

    // Member variables
    std::shared_ptr<umi_axi::axi_master_proxy> axi_proxy_;
    std::unique_ptr<TransactionManager> transaction_manager_;
    std::unique_ptr<ResponseMerger> response_merger_;
	std::unique_ptr<AddressPartition> address_partition_;

    Dispatcher* dispatcher_;
    uint32_t comp_id_;
    uint32_t dest_id_;
    
    // Single producer single consumer thread safe queue for incoming requests
    moodycamel::ReaderWriterQueue<std::vector<char>>* recv_requests_;
    async_event recv_event_;

    SslnProfiling* profiling;
};












}  // namespace axi
}  // namespace hybrid
}  // namespace ssln

#endif  // SSLN_HYBRID_AXI_MASTER_BRIDGE_H_ 
