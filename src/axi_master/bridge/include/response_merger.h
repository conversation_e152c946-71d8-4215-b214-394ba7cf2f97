#ifndef SSLN_HYBRID_RESPONSE_MERGER_H_
#define SSLN_HYBRID_RESPONSE_MERGER_H_

#include "transaction_manager.h"
#include "tlm_payload.h"
#include "tlm.h"

namespace ssln {
namespace hybrid {
namespace axi {

class ResponseMerger {
public:
    explicit ResponseMerger(TransactionManager& manager)
        : transaction_manager_(manager) {}

    // Merge multiple AXI responses into single TLM response
    TlmPayload* MergeResponses(uint64_t transaction_id);

private:
    // Helper functions
    void MergeReadData(uint64_t transaction_id);
    tlm::tlm_response_status DetermineResponseStatus(uint64_t transaction_id);

    TransactionManager& transaction_manager_;
};

}  // namespace axi
}  // namespace hybrid
}  // namespace ssln

#endif  // SSLN_HYBRID_RESPONSE_MERGER_H_ 