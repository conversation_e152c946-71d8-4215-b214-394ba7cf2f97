#ifndef SSLN_HYBRID_TRANSACTION_MANAGER_H_
#define SSLN_HYBRID_TRANSACTION_MANAGER_H_

#include <atomic>
#include <memory>
#include <unordered_map>
#include <vector>
#include "axi_transaction_item.h"
#include "tlm_payload.h"
#include "axi_utils.h"
namespace ssln {
namespace hybrid {
namespace axi {

// Forward declarations
// struct AxiTransactionMetadata;

class TransactionManager {
public:
    // Transaction context structure
    struct TransactionContext {
        TlmPayload* payload;  // Owned TlmPayload with allocated space
        std::vector<AxiTransactionMetadata> splits;
        std::vector<bool> completed;
        std::vector<umi_axi::response_type_t> responses;
        std::vector<uint32_t> axi_ids;  // Store AXI IDs for each split
    };

    TransactionManager() = default;
    ~TransactionManager();

    // Register new transaction and its splits
    void RegisterTransaction(const TlmPayload* ipc_payload,
                           const std::vector<AxiTransactionMetadata>& splits);

    // Find transaction by AXI response ID
    std::pair<TlmPayload*, AxiTransactionMetadata*> FindTransaction(uint64_t axi_id);

    // Update transaction status and return if all completed
    bool UpdateTransaction(const umi_axi::axi_transaction_item* response);

    // Cleanup transaction and its mappings
    void CleanupTransaction(uint64_t payload_id);

    // Get payload for response merger
    TlmPayload* GetPayload(uint64_t payload_id) const;

    // Get transaction context
    const TransactionContext* GetContext(uint64_t payload_id) const {
        auto it = transactions_.find(payload_id);
        return it != transactions_.end() ? &it->second : nullptr;
    }

    uint32_t resp_w_user_width;
    
private:
    // Create new TlmPayload with copied metadata and allocated data space
    TlmPayload* CreateNewTlmPayload(const TlmPayload* ipc_payload);

    // Delete TlmPayload
    void DeleteTlmPayload(TlmPayload* payload);

    std::unordered_map<uint64_t, TransactionContext> transactions_;  // payload_id -> context
    std::unordered_map<uint32_t, uint64_t> axi_to_payload_map_;    // axi_id -> payload_id
    std::atomic<uint32_t> current_axi_id_{0};

    
};



}  // namespace axi
}  // namespace hybrid
}  // namespace ssln

#endif  // SSLN_HYBRID_TRANSACTION_MANAGER_H_ 