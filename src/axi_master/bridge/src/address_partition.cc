#include "address_partition.h"
#include "axi_utils.h"
#include <algorithm>
#include <stdexcept>

namespace ssln {
namespace hybrid {
namespace axi {

void AddressPartition::SetAddress(uint64_t address) {
    start_ = address;
}

void AddressPartition::SetDataLength(uint32_t data_length) {
    len_ = data_length;
}

void AddressPartition::SetStreamingWidth(uint32_t streaming_width) {
    streaming_width_ = streaming_width;
}

void AddressPartition::SetAll(uint64_t address, uint32_t data_length, uint32_t streaming_width) {
    start_ = address;
    len_ = data_length;
    streaming_width_ = streaming_width;
}

uint32_t AddressPartition::GetMaxBurstLength() const {
    return utils::GetMaxBurstBeatNum2(data_width_, spec_ver_, axlen_width_, axsize_width_);
}

uint32_t AddressPartition::GetMaxBurstBytes() const {
    return utils::GetMaxBurstBytes2(data_width_, spec_ver_, axlen_width_, axsize_width_);
}

PartitionType AddressPartition::GetPartitionType() const {
    if (streaming_width_ == 0 || streaming_width_ >= len_) {
        return PartitionType::kIncr;
    } else {
        if (utils::GetStartOffset2(start_, data_width_) + streaming_width_ <= data_width_) {
            return PartitionType::kFixed;
        } else {
            return PartitionType::kMultiIncr;
        }
    }
}

std::vector<AddressRange> AddressPartition::TlmPartition() {
    // Handle streaming width
    if (streaming_width_ == 0 || streaming_width_ > len_) {
        // Return one range for non-streaming transfers
        return {AddressRange{
            .start = start_,
            .len = len_,
            .data_index = 0,
            .part_type = PartitionType::kIncr
        }};
    } else {
        // Split into multiple ranges based on streaming width
        std::vector<AddressRange> ranges;
        uint64_t aligned_address = utils::GetAlignedAddress2(start_, data_width_);
        PartitionType part_type = GetPartitionType();
        uint32_t remaining_len = len_;
        uint32_t current_data_index = 0;

        while (remaining_len > 0) {
            uint32_t len = std::min(remaining_len, streaming_width_);
            ranges.emplace_back(AddressRange{
                .start = start_,
                .len = len,
                .data_index = current_data_index,
                .part_type = part_type
            });
            remaining_len -= len;
            current_data_index += len;
        }
        return ranges;
    }
}

bool AddressPartition::IncrPartition(
    const AddressRange& range,
    std::vector<AxiTransactionMetadata>& result) {
    
    // Initialize variables for splitting
    uint64_t current_address = range.start;
    uint32_t remaining_length = range.len;
    uint32_t current_data_index = range.data_index;

    while (remaining_length > 0) {
        // Calculate maximum burst length considering 4KB boundary
        uint64_t next_4k_boundary = (current_address + BOUNDARY_4K) & ~MASK_4K;
        uint64_t aligned_start = utils::GetAlignedAddress2(current_address, data_width_);
        uint32_t max_burst_bytes = GetMaxBurstBytes();
        uint64_t next_max_burst = aligned_start + max_burst_bytes;

        // Determine burst length
        uint32_t burst_length = std::min({
            remaining_length,
            static_cast<uint32_t>(next_4k_boundary - current_address),
            static_cast<uint32_t>(next_max_burst - current_address)
        });

        // Create metadata for this burst
        AxiTransactionMetadata metadata;
        metadata.address = current_address;
        metadata.data_len = burst_length;
        metadata.data_offset = current_data_index;
        metadata.size = static_cast<umi_axi::size_type_t>(utils::GetAxsize(data_width_));
        metadata.burst = umi_axi::burst_type_t::INCR;
        metadata.beat_num = utils::GetIncrBeatNum2(
            current_address, burst_length, data_width_);

        result.push_back(std::move(metadata));

        // Update for next iteration
        current_address += burst_length;
        remaining_length -= burst_length;
        current_data_index += burst_length;
    }

    return true;
}

bool AddressPartition::FixedPartition(
    const std::vector<AddressRange>& ranges,
    std::vector<AxiTransactionMetadata>& result) {
    
    uint32_t total_beats = ranges.size();
    uint32_t max_beats = GetMaxBurstLength();
    uint32_t current_index = 0;

    while (total_beats > 0) {
        uint32_t beats_this_burst = std::min(max_beats, total_beats);
        
        AxiTransactionMetadata metadata;
        metadata.address = ranges[current_index].start;
        metadata.data_len = beats_this_burst * streaming_width_;
        metadata.data_offset = current_index * streaming_width_;
        metadata.streaming_width = streaming_width_;
        metadata.size = static_cast<umi_axi::size_type_t>(utils::GetAxsize(data_width_));
        metadata.burst = umi_axi::burst_type_t::FIXED;
        metadata.beat_num = beats_this_burst;

        result.push_back(std::move(metadata));

        current_index += beats_this_burst;
        total_beats -= beats_this_burst;
    }

    return true;
}

std::vector<AxiTransactionMetadata> AddressPartition::Partition() {
    std::vector<AxiTransactionMetadata> result;
    
    // First get TLM partitions
    auto tlm_ranges = TlmPartition();
    PartitionType part_type = GetPartitionType();

    // Process based on partition type
    if (part_type == PartitionType::kFixed) {
        FixedPartition(tlm_ranges, result);
    } else if (part_type == PartitionType::kIncr) {
        if (tlm_ranges.size() != 1) {
            throw std::runtime_error("Invalid TLM ranges for INCR partition");
        }
        IncrPartition(tlm_ranges[0], result);
    } else if (part_type == PartitionType::kMultiIncr) {
        for (const auto& range : tlm_ranges) {
            IncrPartition(range, result);
        }
    }

    return result;
}

}  // namespace axi
}  // namespace hybrid
}  // namespace ssln 

