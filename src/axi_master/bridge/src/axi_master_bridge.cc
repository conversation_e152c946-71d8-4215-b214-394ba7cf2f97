#include "axi_master_bridge.h"

#include <cstring>
#include "transaction_manager.h"
#include "response_merger.h"
#include "address_partition.h"
#include "ssln/sslogger.h"
#include "axi_format.h"
#include "quill/std/Vector.h"
#include "dispatcher/generated/payload_generated.h"
using namespace sc_core;
namespace ssln {
namespace hybrid {
namespace axi {

void AxiMasterBridge::InitAxiConfig() {
    // TODO: get AXI configuration from proxy
    axi_proxy_->xtor_start_run();

    // Get AXI configuration from proxy
    data_width_ = (axi_proxy_->data_bus_width + 7) / 8;
    axlen_width_ = axi_proxy_->len_width;
    req_user_width_ = axi_proxy_->req_user_width / 8;
    data_w_user_width_ = axi_proxy_->data_w_user_width / 8;
    resp_w_user_width_ = axi_proxy_->resp_w_user_width / 8;
    data_r_user_width_ = axi_proxy_->data_r_user_width / 8;

    axsize_width_ = 0;

    // Get AXI spec version
    switch (axi_proxy_->axi_spec_type) {
        case 3:
            spec_ver_ = umi_axi::spec_version_t::AMBA3;
            break;
        case 4:
            spec_ver_ = umi_axi::spec_version_t::AMBA4;
            break;
        case 5:
            spec_ver_ = umi_axi::spec_version_t::AMBA5;
            break;
        default:
            spec_ver_ = umi_axi::spec_version_t::AMBA4;  // Default to AXI4
            break;
    }

    transaction_manager_->resp_w_user_width = resp_w_user_width_;

    SSLN_LOG_INFO(file_logger, "[{}], data_width_: {}, axlen_width_: {}, req_user_width_: {}, data_w_user_width_: {}, resp_w_user_width_: {}, data_r_user_width_: {}",
    this->name(),
    data_width_,
    axlen_width_,
    req_user_width_,
    data_w_user_width_,
    resp_w_user_width_,
    data_r_user_width_);

    address_partition_ = std::make_unique<AddressPartition>(
        data_width_,
        spec_ver_,
        axlen_width_,
        axsize_width_);
}

AxiMasterBridge::AxiMasterBridge(
    sc_core::sc_module_name name,
    Dispatcher* dispatcher,
    uint32_t comp_id,
    uint32_t dest_id,
    const std::string& bfm_hdl_path)
    : sc_core::sc_module(name),
      transaction_manager_(std::make_unique<TransactionManager>()),
      response_merger_(std::make_unique<ResponseMerger>(*transaction_manager_)),
      dispatcher_(dispatcher),
      comp_id_(comp_id),
      dest_id_(dest_id) {
    
    profiling = new SslnProfiling(this->name());
    axi_proxy_ = std::make_shared<umi_axi::axi_master_proxy>("axi_proxy", bfm_hdl_path);
    recv_requests_ = new moodycamel::ReaderWriterQueue<std::vector<char>>(15);
    axi_proxy_->setHandler("AFTER_RECV_TRANS", this, &AxiMasterBridge::HandleResponse);

    address_partition_ = nullptr;
    
    SC_THREAD(ScProcessRequest);
    
    // Register this component with the dispatcher
    dispatcher_->RegisterEndpoint(this);
}

AxiMasterBridge::AxiMasterBridge(
    sc_core::sc_module_name name,
    const std::string& channel_name,
    std::shared_ptr<umi_axi::axi_master_proxy> axi_proxy)
    : sc_core::sc_module(name)
    , axi_proxy_(std::move(axi_proxy))
    , transaction_manager_(std::make_unique<TransactionManager>())
    , response_merger_(std::make_unique<ResponseMerger>(*transaction_manager_))
    {
    
    ipc::channel::clear_storage((channel_name + "_resp").c_str());
    ipc::channel::clear_storage((channel_name + "_req").c_str());
    req_channel_ = new ipc::channel((channel_name + "_req").c_str(), ipc::receiver);
    resp_channel_ = new ipc::channel((channel_name + "_resp").c_str(), ipc::sender);
    
    // resp_channel_->wait_for_recv(1);
    profiling = new SslnProfiling(this->name());
    recv_requests = new moodycamel::ReaderWriterQueue<ipc::buffer*>(15);
    axi_proxy_->setHandler("AFTER_RECV_TRANS", this, &AxiMasterBridge::HandleResponse);

    address_partition_ = nullptr;

    
    SC_THREAD(ScProcessRequest);
    // Start request processing thread
    request_thread_ = std::make_unique<std::thread>(&AxiMasterBridge::ProcessRequest, this);
}


void AxiMasterBridge::ScProcessRequest(){

    // Initialize AXI configuration
    SSLN_LOG_INFO(file_logger, "[{}], InitAxiConfig start", this->name());
    InitAxiConfig();
    SSLN_LOG_INFO(file_logger, "[{}], InitAxiConfig end", this->name());
    axi_proxy_->wait_reset_deassert();
    SSLN_LOG_INFO(file_logger, "[{}], proxy wait_reset_deassert ok", this->name());
    while(true){
        if(recv_requests_->peek() == nullptr){
            SSLN_LOG_INFO(file_logger, "[{}], wait recv event start", this->name());
            wait(recv_event_);
            SSLN_LOG_INFO(file_logger, "[{}], wait recv event end", this->name());
        }

        std::vector<char> data;
        recv_requests_->try_dequeue(data);
        
        auto* payload = flatbuffers::GetRoot<ssln::hybrid::dispatch::Payload>(data.data());

        SSLN_LOG_INFO(file_logger, "[{}], process request id={}, cmd={}, addr={:#x}", this->name(), payload->id(), int(payload->command()), payload->address());

        // 2. Split transaction with current AXI configuration
        address_partition_->SetAddress(payload->address());
        address_partition_->SetDataLength(payload->data_length());
        address_partition_->SetStreamingWidth(payload->streaming_width());

        profiling->add_start_time("Partition_time");
        auto split_transactions = address_partition_->Partition();
        profiling->add_duration_time("Partition_time");
        
        // Convert tlm command to xtor axi command
        for(auto& item: split_transactions){
            item.cmd = payload->command == tlm::TLM_READ_COMMAND ? umi_axi::cmd_type_t::READ : umi_axi::cmd_type_t::WRITE;
        }

        // 3. Register with transaction manager
        profiling->add_start_time("RegisterTransaction");
        // TODO: This needs rework. The transaction manager expects a TlmPayload*, but we have a FlatBuffer.
        // This highlights the need for a conversion utility or to update the manager to understand FlatBuffers.
        // For now, this will cause a compile error. We will fix it later.
        // transaction_manager_->RegisterTransaction(payload, split_transactions);
        profiling->add_duration_time("RegisterTransaction");
        // 4. Send each split transaction to AXI
        const auto* context = transaction_manager_->GetContext(payload->id());
        if (!context) continue;

        SSLN_LOG_INFO(file_logger, "[{}], TlmPayload id={} split to axi_ids={}", this->name(), payload->id(), context->axi_ids);

        for (size_t i = 0; i < split_transactions.size(); ++i) {
            profiling->add_start_time("CreateAxiTransaction");
            // auto axi_trans = CreateAxiTransaction(
            //     split_transactions[i],
            //     payload,
            //     context->axi_ids[i]);
            profiling->add_duration_time("CreateAxiTransaction");

            SSLN_LOG_INFO(file_logger, "[{}], b_send_transaction start, axi id={}, cmd={}, addr={:#x}", this->name(), axi_trans->commu_tag.msg_id, int(axi_trans->cmd), axi_trans->addr);
            SSLN_LOG_DEBUG(file_logger, "[{}], AXI request: {}",this->name(), *axi_trans.get());
            profiling->add_start_time("b_send_transaction");
            SSLN_LOG_INFO(file_logger, "end time");
            axi_proxy_->b_send_transaction(*axi_trans.get());
            profiling->add_duration_time("b_send_transaction");
            SSLN_LOG_INFO(file_logger, "[{}], b_send_transaction end", this->name());

        }
        // delete data; // vector will go out of scope
    }
}

AxiMasterBridge::~AxiMasterBridge() {
    delete profiling;
    delete recv_requests_;
}

uint32_t AxiMasterBridge::GetComponentId() const {
    return comp_id_;
}

bool AxiMasterBridge::HandleData(const void* data, size_t size) {
    SSLN_LOG_INFO(file_logger, "[{}], handle_data received data size: {}", this->name(), size);
    
    // Copy the data into a vector to be owned by the queue
    std::vector<char> data_vec(static_cast<const char*>(data), static_cast<const char*>(data) + size);

    if (recv_requests_->try_enqueue(std::move(data_vec))) {
        recv_event_.notify(sc_core::SC_ZERO_TIME);
        return true;
    } else {
        SSLN_LOG_ERROR(file_logger, "[{}], Request queue full, data loss!!!", this->name());
        return false;
    }
}

void AxiMasterBridge::HandleResponse(void* response) {
    auto axi_resp = static_cast<umi_axi::axi_transaction_item*>(response);
    // 1. Find original TLM payload
    auto [payload, metadata] = transaction_manager_->FindTransaction(axi_resp->commu_tag.msg_id);
    if (!payload){
        SSLN_LOG_ERROR(file_logger, "[{}], transaction_manager can't FindTransaction id={}", this->name(), axi_resp->commu_tag.msg_id);
        return;
    } 
    

    SSLN_LOG_INFO(file_logger, "[{}], HandleResponse, axi_id={}, id={}, cmd={}, addr={:#x}", this->name(), axi_resp->commu_tag.msg_id, payload->id, int(axi_resp->cmd), axi_resp->addr);
    SSLN_LOG_DEBUG(file_logger, "[{}], AXI: {}", this->name(), *axi_resp);

    // 2. Update transaction status
    profiling->add_start_time("UpdateTransaction");
    bool all_completed = transaction_manager_->UpdateTransaction(axi_resp);
    profiling->add_duration_time("UpdateTransaction");
    // 3. If all split transactions completed, merge and send response
    if (all_completed) {
        auto resp_payload = response_merger_->MergeResponses(payload->id);
        if (resp_payload) {
            SSLN_LOG_INFO(file_logger, "[{}], SendResponse, id={}, cmd={}, addr={:#x}", this->name(), resp_payload->id, resp_payload->command, resp_payload->address);
            profiling->add_start_time("SendResponse");
            SendResponse(resp_payload);
            profiling->add_duration_time("SendResponse");
            // Cleanup transaction after sending response
            transaction_manager_->CleanupTransaction(payload->id);
        }
    }

}

std::shared_ptr<umi_axi::axi_transaction_item> AxiMasterBridge::CreateAxiTransaction(
    const AxiTransactionMetadata& metadata,
    const TlmPayload* payload,
    uint64_t axi_id) {
    auto transaction = std::make_shared<umi_axi::axi_transaction_item>();
    // Set basic transaction properties
    transaction->id = 0; 
    transaction->commu_tag.msg_id = axi_id;
    transaction->cmd = metadata.cmd;
    transaction->addr = metadata.address;
    transaction->len = metadata.beat_num - 1;  // AXI uses 0-based length
    transaction->size = metadata.size;
    transaction->burst = metadata.burst;
    
    // Handle data for write transactions
    if (transaction->cmd == umi_axi::cmd_type_t::WRITE) {
        if (metadata.burst == umi_axi::burst_type_t::FIXED) {
            FillDataAndStrobeForFixedBurst(transaction.get(), metadata, payload);
        } else if (metadata.burst == umi_axi::burst_type_t::INCR) {
            FillDataAndStrobeForIncrBurst(transaction.get(), metadata, payload);
        } else {
            throw std::runtime_error("Unsupported burst type");
        }
    }
    

    // handle user data
    if(payload->axuser_length > 0){

        uint8_t* user_req_ptr;
        if(payload->command == tlm::TLM_READ_COMMAND){
            user_req_ptr = payload->data;
        }
        else{
            user_req_ptr = payload->data + payload->data_length + payload->byte_enable_length;
        }
        
        transaction->req_user.insert(transaction->req_user.end(), user_req_ptr, user_req_ptr + payload->axuser_length);
        
        if(payload->command == 1){
            // uint32_t user_data_width = data_w_user_width_;
            // uint32_t user_data_offset = metadata.data_offset / data_width_* user_data_width;
            // uint32_t user_data_count = metadata.beat_num * user_data_width;
            // user_data_count = min(user_data_count, payload->xuser_length - user_data_offset);
            uint8_t* user_data_ptr = payload->data + payload->data_length + payload->byte_enable_length + payload->axuser_length;
            // if(user_data_offset < payload->xuser_length){
            //     transaction->data_user.insert(transaction->data_user.end(), user_data_ptr, user_data_ptr + user_data_count);
            // }
            transaction->data_user.insert(transaction->data_user.end(), user_data_ptr, user_data_ptr + payload->xuser_length);
        }
        
    }
    return transaction;
}

void AxiMasterBridge::FillDataAndStrobeForFixedBurst(
    umi_axi::axi_transaction_item* transaction,
    const AxiTransactionMetadata& metadata,
    const TlmPayload* payload) {
    
    uint8_t data_width = 1 << static_cast<uint32_t>(metadata.size);
    uint8_t start_offset = metadata.address % data_width;
    uint8_t end_offset = data_width - start_offset - metadata.streaming_width;
    uint32_t beat_num = metadata.beat_num;
    uint32_t total_len = beat_num * data_width;
    
    transaction->data.reserve(total_len);
    transaction->be.reserve(total_len);
    for (uint32_t i = 0; i < beat_num - 1; i++) {
        // Add start padding
        if (start_offset > 0) {
            transaction->data.insert(transaction->data.end(), start_offset, 0x00);
            transaction->be.insert(transaction->be.end(), start_offset, 0x00);
        }

        // Copy actual data
        const uint8_t* data_ptr = reinterpret_cast<const uint8_t*>(payload->data + metadata.data_offset + i * metadata.streaming_width);
        transaction->data.insert(transaction->data.end(),
                               data_ptr,
                               data_ptr + metadata.streaming_width);

        // Handle byte enables
        if (payload->byte_enable_length > 0) {
            // const uint8_t* be_ptr = reinterpret_cast<const uint8_t*>(payload->data + payload->data_length + metadata.data_offset + i * metadata.streaming_width);
            // transaction->be.insert(transaction->be.end(),
            //                        be_ptr,
            //                        be_ptr + metadata.streaming_width);
            const uint8_t* be_ptr = reinterpret_cast<const uint8_t*>(payload->data + payload->data_length);
            uint32_t now_data_offset = metadata.data_offset + i * metadata.streaming_width;
            uint32_t now_be_start = now_data_offset % payload->byte_enable_length;
            uint32_t now_be_end = now_be_start + metadata.streaming_width;
            if(now_be_end <= payload->byte_enable_length){ // fast copy
                transaction->be.insert(transaction->be.end(), be_ptr + now_be_start, be_ptr + now_be_end);
            }
            else{ // normal copy
                for(int j = 0; j < metadata.streaming_width; j++){
                    transaction->be.push_back(be_ptr[(now_data_offset + j) % payload->byte_enable_length]);
                }
            }
        } else {
            transaction->be.insert(transaction->be.end(),
                                   metadata.streaming_width,
                                   0xFF);
        }

        // Add end padding
        if (end_offset > 0) {
            transaction->data.insert(transaction->data.end(), end_offset, 0x00);
            transaction->be.insert(transaction->be.end(), end_offset, 0x00);
        }
    }


    // last beat
    uint32_t last_data_len = metadata.data_len % metadata.streaming_width;
    if(metadata.data_len % metadata.streaming_width == 0){
        last_data_len = metadata.streaming_width;
    }
    

    if (start_offset > 0) {
        transaction->data.insert(transaction->data.end(), start_offset, 0x00);
        transaction->be.insert(transaction->be.end(), start_offset, 0x00);
    }
    const uint8_t* data_ptr = reinterpret_cast<const uint8_t*>(payload->data + metadata.data_offset + (beat_num - 1) * metadata.streaming_width);
    transaction->data.insert(transaction->data.end(),
                            data_ptr,
                            data_ptr + last_data_len);
    
    if (payload->byte_enable_length > 0) {
        // const uint8_t* be_ptr = reinterpret_cast<const uint8_t*>(payload->data + payload->data_length + metadata.data_offset + (beat_num - 1) * metadata.streaming_width);
        // transaction->be.insert(transaction->be.end(),
        //                         be_ptr,
        //                         be_ptr + last_data_len);
        const uint8_t* be_ptr = reinterpret_cast<const uint8_t*>(payload->data + payload->data_length);
        uint32_t now_data_offset = metadata.data_offset + (beat_num - 1) * metadata.streaming_width;
        uint32_t now_be_start = now_data_offset % payload->byte_enable_length;
        uint32_t now_be_end = (now_be_start + metadata.streaming_width) % payload->byte_enable_length;
        if(now_be_end > now_be_start && now_be_end - now_be_start == metadata.streaming_width){ // fast copy
            transaction->be.insert(transaction->be.end(), be_ptr + now_be_start, be_ptr + now_be_end);
        }
        else{ // normal copy
            for(int j = 0; j < metadata.streaming_width; j++){
                transaction->be.push_back(be_ptr[(now_data_offset + j) % payload->byte_enable_length]);
            }
        }
    } else {
        transaction->be.insert(transaction->be.end(),
                                last_data_len,
                                0xFF);
    }

    // Add end padding
    // uint8_t last_end_offset = metadata.streaming_width - metadata.data_len % metadata.streaming_width + end_offset;
    uint8_t last_end_offset = data_width - start_offset - last_data_len;
    if (last_end_offset > 0) {
        transaction->data.insert(transaction->data.end(), last_end_offset, 0x00);
        transaction->be.insert(transaction->be.end(), last_end_offset, 0x00);
    }

    // std::cout << "FillDataAndStrobeForFixedBurst data_width: " << int(data_width) << " start_offset: " << int(start_offset) << " end_offset: " << int(end_offset) << " last_end_offset: " << int(last_end_offset) << " beat_num: " << beat_num << " total_len: " << total_len << " data vector size: " <<transaction->data.size() << std::endl;
}

void AxiMasterBridge::FillDataAndStrobeForIncrBurst(
    umi_axi::axi_transaction_item* transaction,
    const AxiTransactionMetadata& metadata,
    const TlmPayload* payload) {
    uint8_t data_width = 1 << static_cast<uint32_t>(metadata.size);
    uint8_t start_offset = metadata.address % data_width;
    uint8_t end_offset = (data_width - ((metadata.address + metadata.data_len) % data_width)) % data_width;

    // Add start padding
    if (start_offset > 0) {
        transaction->data.insert(transaction->data.end(), start_offset, 0x00);
        transaction->be.insert(transaction->be.end(), start_offset, 0x00);
    }

    // Copy actual data
    const uint8_t* data_ptr = reinterpret_cast<const uint8_t*>(payload->data + metadata.data_offset);
    transaction->data.insert(transaction->data.end(),
                           data_ptr,
                           data_ptr + metadata.data_len);

    // Handle byte enables
    if (payload->byte_enable_length > 0) {
        const uint8_t* be_ptr = reinterpret_cast<const uint8_t*>(payload->data + payload->data_length + metadata.data_offset);
        transaction->be.insert(transaction->be.end(),
                               be_ptr,
                               be_ptr + metadata.data_len);
    } else {
        transaction->be.insert(transaction->be.end(),
                               metadata.data_len,
                               0xFF);
    }

    // Add end padding
    if (end_offset > 0) {
        transaction->data.insert(transaction->data.end(), end_offset, 0x00);
        transaction->be.insert(transaction->be.end(), end_offset, 0x00);
    }
}

void AxiMasterBridge::SendResponse(TlmPayload* resp_payload) {
    flatbuffers::FlatBufferBuilder builder;

    // This function now needs to construct a FlatBuffer from the TlmPayload
    // which is created by the response_merger.
    
    // 1. Pack variable data
    std::vector<uint8_t> variable_data;
    if (resp_payload->command == 0) { // READ
        variable_data.insert(variable_data.end(), resp_payload->data, resp_payload->data + resp_payload->data_length);
    }
    // Assuming xuser data is always present in response
    uint8_t* xuser_ptr = resp_payload->data + resp_payload->data_length;
    variable_data.insert(variable_data.end(), xuser_ptr, xuser_ptr + resp_payload->xuser_length);

    auto fb_variable_data = builder.CreateVector(variable_data);

    // 2. Build the payload
    auto payload = ssln::hybrid::dispatch::CreatePayload(
        builder,
        dest_id_,
        resp_payload->id,
        resp_payload->command,
        resp_payload->address,
        resp_payload->response,
        resp_payload->streaming_width,
        fb_variable_data,
        resp_payload->data_length,
        resp_payload->byte_enable_length,
        resp_payload->axuser_length,
        resp_payload->xuser_length);
        
    builder.Finish(payload);

    // 3. Send the buffer
    uint8_t* buffer = builder.GetBufferPointer();
    size_t size = builder.GetSize();

    SSLN_LOG_INFO(file_logger, "[{}], Sending response to dest_id={}, size={}", this->name(), dest_id_, size);
    dispatcher_->Send(dest_id_, buffer, size);
}

}  // namespace axi
}  // namespace hybrid
}  // namespace ssln
