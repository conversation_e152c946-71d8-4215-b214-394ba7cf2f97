#include "response_merger.h"
#include "axi_utils.h"
#include <algorithm>
#include <cstring>

namespace ssln {
namespace hybrid {
namespace axi {

TlmPayload* ResponseMerger::MergeResponses(uint64_t transaction_id) {
    // Get payload through public interface
    TlmPayload* payload = transaction_manager_.GetPayload(transaction_id);
    if (!payload) return nullptr;
    
    // For read transactions, merge data directly into pre-allocated space
    if (payload->command == 0) {
        // MergeReadData(transaction_id);
    }
    
    // Set response status
    payload->response = DetermineResponseStatus(transaction_id);
    
    // Return payload (will be cleaned up by TransactionManager after IPC send)
    return payload;
}

void ResponseMerger::MergeReadData(uint64_t transaction_id) {
    auto* payload = transaction_manager_.GetPayload(transaction_id);
    const auto& context = transaction_manager_.GetContext(transaction_id);
    
    // Merge data directly into pre-allocated space in payload
    for (const auto& split : context->splits) {
        const auto& axi_trans = split.axi_transaction;
        
        // Copy data to correct offset in pre-allocated space
        std::memcpy(payload->data + split.data_offset,
                   axi_trans->data.data() ,
                   split.data_len);
        // std::memcpy(payload->data + split.data_offset, axi_trans->data.data() + split.address - axi_trans->addr, split.data_len);
    }
}

tlm::tlm_response_status ResponseMerger::DetermineResponseStatus(uint64_t transaction_id) {
    const auto& context = transaction_manager_.GetContext(transaction_id);
    
    // Check if any response indicates an error
    bool has_error = std::any_of(context->responses.begin(), context->responses.end(),
        [](const auto& resp) {
            return resp == umi_axi::response_type_t::SLVERR ||
                   resp == umi_axi::response_type_t::DECERR;
        });
        
    return has_error ? tlm::TLM_GENERIC_ERROR_RESPONSE : tlm::TLM_OK_RESPONSE;
}

}  // namespace axi
}  // namespace hybrid
}  // namespace ssln 