#include "transaction_manager.h"
#include "axi_utils.h"
#include <algorithm>
#include <cstring>
#include <cassert>
using namespace std;
namespace ssln {
namespace hybrid {
namespace axi {

TransactionManager::~TransactionManager() {
    // Cleanup any remaining transactions
    for (auto& [id, context] : transactions_) {
        DeleteTlmPayload(context.payload);
    }
    transactions_.clear();
    axi_to_payload_map_.clear();
}

void TransactionManager::RegisterTransaction(
    const TlmPayload* ipc_payload,
    const std::vector<AxiTransactionMetadata>& splits) {
    TransactionContext context;
    context.payload = CreateNewTlmPayload(ipc_payload);
    context.splits = splits;
    context.completed.resize(splits.size(), false);
    context.responses.resize(splits.size());
    
    // Store using payload id from proxy
    uint64_t payload_id = ipc_payload->id;
    transactions_[payload_id] = std::move(context);
    
    // Create mapping from AXI transaction IDs to payload ID
    for (size_t i = 0; i < splits.size(); ++i) {
        uint64_t axi_id = current_axi_id_.fetch_add(1, std::memory_order_relaxed);
        // uint64_t axi_id = 0;

        axi_to_payload_map_[axi_id] = payload_id;
        transactions_[payload_id].axi_ids.push_back(axi_id);
        transactions_[payload_id].splits[i].axi_id = axi_id;
    }
}

std::pair<TlmPayload*, AxiTransactionMetadata*> TransactionManager::FindTransaction(
    uint64_t axi_id) {
    auto it = axi_to_payload_map_.find(axi_id);
    if (it == axi_to_payload_map_.end()) {
        return {nullptr, nullptr};
    }
    
    uint64_t payload_id = it->second;
    auto trans_it = transactions_.find(payload_id);
    if (trans_it == transactions_.end()) {
        return {nullptr, nullptr};
    }
    
    // Find the corresponding split transaction
    auto& context = trans_it->second;
    auto axi_it = std::find(context.axi_ids.begin(), context.axi_ids.end(), axi_id);
    if (axi_it == context.axi_ids.end()) {
        return {nullptr, nullptr};
    }
    
    size_t split_index = axi_it - context.axi_ids.begin();
    return {context.payload, &context.splits[split_index]};
}

bool TransactionManager::UpdateTransaction(
    const umi_axi::axi_transaction_item* response) {
    auto it = axi_to_payload_map_.find(response->commu_tag.msg_id);
    if (it == axi_to_payload_map_.end()) {
        return false;
    }
    
    // std::cout << "UpdateTransaction, addr: " << response->addr << " len: " << int(response->len) << " size: " << int(response->size) << " burst: " << int(response->burst) << std::endl;

    uint32_t t_burst_len = (response->len + 1) * (1 << response->size);
    if(response->data.size() != t_burst_len){
        // cout << "data size error!!!!" << endl;
    }
    

    uint64_t payload_id = it->second;
    auto& context = transactions_[payload_id];
    
    // Find the split transaction index
    auto axi_it = std::find(context.axi_ids.begin(), context.axi_ids.end(), response->commu_tag.msg_id);
    if (axi_it == context.axi_ids.end()) {
        return false;
    }
    
    size_t split_index = axi_it - context.axi_ids.begin();
    context.completed[split_index] = true;
    // context.responses[split_index] = response->resp; // TODO
    
    // parser out valid data.
    const auto& split = context.splits[split_index];
    auto* payload = this->GetPayload(payload_id);
    if(payload->command == 0){
        if(response->burst == umi_axi::burst_type_t::INCR){
            uint32_t start_offset =  utils::GetStartOffset2(response->addr, response->data_width / 8);
            // cout << "split.data_offset: " << int(split.data_offset) << " start_offset: " << start_offset << " split.data_len: " << split.data_len << endl;
            memcpy(payload->data + split.data_offset, response->data.data() + start_offset, split.data_len);
            
        }
        else{
            uint64_t now_addr = split.address;
            uint32_t remain_data = split.data_len;
            for(int i = 0; i < response->len + 1; i++){
                uint32_t start_offset = utils::GetStartOffset2(now_addr, response->data_width / 8);
                assert(i * (response->data_width / 8) + start_offset + std::min(payload->streaming_width, remain_data) <= response->data.size());
                memcpy(payload->data + split.data_offset + i * payload->streaming_width, response->data.data() + i * (response->data_width / 8) + start_offset, std::min(payload->streaming_width, remain_data));
                remain_data -= payload->streaming_width;
            }
        }

    }
    // std::memcpy(payload->data + split.data_offset, response->data.data() + split.address - response->addr, split.data_len);

    // user data
    

    if(payload->command == 0 && payload->xuser_length > 0){
        // std::cout << "response->data_user.size: " << response->data_user.size() << std::endl;
        // for(int i = 0; i < response->data_user.size(); i++){
        //     std::cout << int(response->data_user[i]) << " ";
        // }
        // std::cout << std::endl;
        uint32_t user_data_offset = split.data_offset / (response->data_width / 8) * (response->data_r_user_width / 8);
        uint8_t* user_data_ptr = payload->data + payload->data_length + user_data_offset;
        // std::cout << "user_data_ptr- offset1: " << payload->data_length << " offset2: " <<  user_data_offset << std::endl;
        memcpy(user_data_ptr, response->data_user.data(), payload->xuser_length);
        
    }
    if(payload->command == 1 && payload->xuser_length > 0){
        uint8_t* user_resp_ptr = payload->data;
        memcpy(user_resp_ptr, response->resp_user.data(), payload->xuser_length);
    }


    // Check if all splits are completed
    return std::all_of(context.completed.begin(), context.completed.end(),
                      [](bool v) { return v; });
}

void TransactionManager::CleanupTransaction(uint64_t payload_id) {
    auto it = transactions_.find(payload_id);
    if (it != transactions_.end()) {
        // Remove all AXI ID mappings
        for (uint64_t axi_id : it->second.axi_ids) {
            axi_to_payload_map_.erase(axi_id);
        }
        
        // Delete payload and remove from transactions map
        DeleteTlmPayload(it->second.payload);
        
        transactions_.erase(it);

    }
}

TlmPayload* TransactionManager::GetPayload(uint64_t payload_id) const {
    auto it = transactions_.find(payload_id);
    return it != transactions_.end() ? it->second.payload : nullptr;
}

TlmPayload* TransactionManager::CreateNewTlmPayload(const TlmPayload* ipc_payload) {
    // Allocate new payload with or without data space
    size_t total_size = sizeof(TlmPayload);
    if(ipc_payload->command == 0){
        total_size += ipc_payload->data_length;
    }

    // user data
    if(ipc_payload->axuser_length > 0){
        if(ipc_payload->command == 0){
            total_size += ipc_payload->xuser_length;
        }
        else{
            total_size += this->resp_w_user_width;
        }
    }

    // std::cout << "CreateNewTlmPayload " << "id: " << ipc_payload->id << " command: " << int(ipc_payload->command) << " address: " << ipc_payload->address << " data length: " << ipc_payload->data_length << " streaming width: " << ipc_payload->streaming_width << " resp_w_user_width: " << this->resp_w_user_width << std::endl;

    // std::cout << "total size: " << total_size << " sizeof: " << sizeof(TlmPayload) << " data size: " << ipc_payload->data_length << std::endl;

    auto* new_payload = reinterpret_cast<TlmPayload*>(new unsigned char[total_size]);
    
    // Copy metadata including id
    new_payload->id = ipc_payload->id;
    new_payload->command = ipc_payload->command;
    new_payload->address = ipc_payload->address;
    new_payload->data_length = ipc_payload->data_length;
    new_payload->byte_enable_length = 0;
    new_payload->streaming_width = ipc_payload->streaming_width;
    new_payload->data = reinterpret_cast<unsigned char*>(new_payload) + sizeof(TlmPayload);

    if(ipc_payload->axuser_length > 0){
        if(ipc_payload->command == 0){
            new_payload->xuser_length = ipc_payload->xuser_length;
            new_payload->axuser_length = 0;
        }
        else{
            new_payload->xuser_length = this->resp_w_user_width;
            new_payload->axuser_length = 0;
        }
    }
    else{
        new_payload->axuser_length = 0;
        new_payload->xuser_length = 0;
    }
    // For write command, copy data
    // if (ipc_payload->command == 1) {
    //     std::memcpy(new_payload->data,
    //                ipc_payload->data,
    //                ipc_payload->data_length);
    // }
    
    return new_payload;
}

void TransactionManager::DeleteTlmPayload(TlmPayload* payload) {
    if (payload) {
        delete [] reinterpret_cast<uint8_t*>(payload);
    }
}

}  // namespace axi
}  // namespace hybrid
}  // namespace ssln
