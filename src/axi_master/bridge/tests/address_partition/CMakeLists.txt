
add_executable(address_partition_test
    src/address_partition_test.cc
    
)

#target_include_directories(address_partition_test
#   PRIVATE
#        ${PROJECT_ROOT}/thirdparty/fake_install/include
#)

target_link_directories(address_partition_test
    PRIVATE
        $ENV{UMICOM_HOME}/lib
        ${PROJECT_ROOT}/thirdparty/fake_install/lib
)



target_link_libraries(address_partition_test
    PRIVATE
        GTest::gtest_main
        fake_xtor
        axi_master_bridge
        systemc
)

include(GoogleTest)
gtest_discover_tests(address_partition_test)

add_dependencies(build_utests address_partition_test)