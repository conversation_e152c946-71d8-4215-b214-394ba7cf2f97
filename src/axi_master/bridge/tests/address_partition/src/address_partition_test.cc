#include <systemc>
#include <gtest/gtest.h>
#include "address_partition.h"
#include "axi_utils.h"

namespace ssln::hybrid::axi {

class AddressPartitionTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Common setup can be added here
    }

    void TearDown() override {
        // Common teardown can be added here
    }
};

TEST_F(AddressPartitionTest, SingleAligned) {
    AddressPartition partition(4, umi_axi::spec_version_t::AMBA4);
    partition.SetAll(0x1234, 0x400, 0);
    auto result = partition.Partition();
    ASSERT_EQ(result.size(), 1u);
    EXPECT_EQ(result[0].address, 0x1234u);
    EXPECT_EQ(result[0].data_len, 0x400u);
    EXPECT_EQ(result[0].burst, umi_axi::burst_type_t::INCR);
}

TEST_F(AddressPartitionTest, ZeroLength) {
    AddressPartition partition(4, umi_axi::spec_version_t::AMBA4);
    partition.SetAll(0x1235, 0, 0);
    auto result = partition.Partition();
    EXPECT_EQ(result.size(), 0u);
}

TEST_F(AddressPartitionTest, SingleUnaligned_front) {
    AddressPartition partition(8, umi_axi::spec_version_t::AMBA4);
    partition.SetAll(0x1235, 0x800, 0);
    auto result = partition.Partition();
    ASSERT_EQ(result.size(), 2u);
    EXPECT_EQ(result[0].address, 0x1235u);
    EXPECT_EQ(result[0].data_len, 0x7fbu);
    EXPECT_EQ(result[1].address, 0x1A30u);
    EXPECT_EQ(result[1].data_len, 0x5u);
}

TEST_F(AddressPartitionTest, SingleUnaligned_tail) {
    AddressPartition partition(8, umi_axi::spec_version_t::AMBA4);
    partition.SetAll(0x1ff9, 0x8, 0);
    auto result = partition.Partition();
    ASSERT_EQ(result.size(), 2u);
    EXPECT_EQ(result[0].address, 0x1ff9u);
    EXPECT_EQ(result[0].data_len, 0x7u);
    EXPECT_EQ(result[1].address, 0x2000u);
    EXPECT_EQ(result[1].data_len, 0x1u);
}

TEST_F(AddressPartitionTest, AlignedCrossing4K) {
    AddressPartition partition(8, umi_axi::spec_version_t::AMBA4);
    partition.SetAll(0x2008, 0x1000, 0);
    auto result = partition.Partition();
    ASSERT_EQ(result.size(), 3u);
    EXPECT_EQ(result[0].address, 0x2008u);
    EXPECT_EQ(result[0].data_len, 0x800u);
    EXPECT_EQ(result[1].address, 0x2808u);
    EXPECT_EQ(result[1].data_len, 0x7F8u);
    EXPECT_EQ(result[2].address, 0x3000u);
    EXPECT_EQ(result[2].data_len, 0x8u);
}

TEST_F(AddressPartitionTest, UnalignedCrossing4K) {
    AddressPartition partition(16, umi_axi::spec_version_t::AMBA4);
    partition.SetAll(0x1234567, 0x2000, 0);
    auto result = partition.Partition();
    ASSERT_EQ(result.size(), 3u);
    EXPECT_EQ(result[0].address, 0x1234567u);
    EXPECT_EQ(result[0].data_len, 0xA99u);
    EXPECT_EQ(result[1].address, 0x1235000u);
    EXPECT_EQ(result[1].data_len, 0x1000u);
    EXPECT_EQ(result[2].address, 0x1236000u);
    EXPECT_EQ(result[2].data_len, 0x567u);
}

TEST_F(AddressPartitionTest, UnalignedCrossing4K_2K) {
    AddressPartition partition(8, umi_axi::spec_version_t::AMBA4);
    partition.SetAll(0x1234567, 0x1000, 0);
    auto result = partition.Partition();
    ASSERT_EQ(result.size(), 3u);
    EXPECT_EQ(result[0].address, 0x1234567u);
    EXPECT_EQ(result[0].data_len, 0x7F9u);
    EXPECT_EQ(result[1].address, 0x1234D60u);
    EXPECT_EQ(result[1].data_len, 0x2A0u);
    EXPECT_EQ(result[2].address, 0x1235000u);
    EXPECT_EQ(result[2].data_len, 0x567u);
}

TEST_F(AddressPartitionTest, AXI3Specification) {
    AddressPartition partition(4, umi_axi::spec_version_t::AMBA3);
    partition.SetAll(0x1000, 0x1000, 0);
    auto result = partition.Partition();
    ASSERT_EQ(result.size(), 64u);
    for (const auto& metadata : result) {
        EXPECT_EQ(metadata.data_len, 64u);  // AXI3 max burst length is 16, so 16 * 4 bytes = 64
    }
}

/*
TEST_F(AddressPartitionTest, CustomerAxlenWidth) {
    AddressPartition partition(0x1000, 0x1000, 0, 4, umi_axi::spec_version_t::AMBA4, 6, 0);  // axdata_len_width = 6
    ASSERT_TRUE(partition.Partition());
    const auto& ranges = partition.GetRanges();
    for (const auto& range : ranges) {
        EXPECT_LE(range.data_len, 256u);  // 2^6 * 4 bytes = 256
    }
}
*/


TEST_F(AddressPartitionTest, StreamingWidth) {
    AddressPartition partition(4, umi_axi::spec_version_t::AMBA4);
    partition.SetAll(0x1000, 0x1000, 128);
    auto result = partition.Partition();
    for (const auto& metadata : result) {
        EXPECT_LE(metadata.data_len, 128u);
        EXPECT_EQ(metadata.burst, umi_axi::burst_type_t::INCR);
    }
}

TEST_F(AddressPartitionTest, StreamingWidthUnaligned) {
    AddressPartition partition(8, umi_axi::spec_version_t::AMBA4);
    partition.SetAll(0x1001, 34, 4);
    auto result = partition.Partition();
    EXPECT_EQ(result.size(), 1u);
    for (const auto& metadata : result) {
        EXPECT_EQ(metadata.data_len, 36u);
        EXPECT_EQ(metadata.burst, umi_axi::burst_type_t::FIXED);
    }
}

TEST_F(AddressPartitionTest, StreamingBigThan4K) {
    AddressPartition partition(4, umi_axi::spec_version_t::AMBA4);
    partition.SetAll(0x1000, 0x1000, 0x500);
    auto result = partition.Partition();
    EXPECT_EQ(result.size(), 7u);
    for (int i = 0; i < 3; i++) {
        EXPECT_EQ(result.at(i*2).address, 0x1000u);
        EXPECT_EQ(result.at(i*2).data_len, 0x400u);
        EXPECT_EQ(result.at(i*2).burst, umi_axi::burst_type_t::INCR);
        EXPECT_EQ(result.at(i*2+1).address, 0x1400u);
        EXPECT_EQ(result.at(i*2+1).data_len, 0x100u);
        EXPECT_EQ(result.at(i*2+1).burst, umi_axi::burst_type_t::INCR);
    }
}

TEST_F(AddressPartitionTest, MultiIncrBurst) {
    AddressPartition partition(4, umi_axi::spec_version_t::AMBA4);
    partition.SetAll(0x1000, 256, 64);
    auto result = partition.Partition();
    EXPECT_EQ(result.size(), 4u);
    for (const auto& metadata : result) {
        EXPECT_LE(metadata.data_len, 64u);
        EXPECT_EQ(metadata.burst, umi_axi::burst_type_t::INCR);
    }
}

TEST_F(AddressPartitionTest, MultiFixedBurst) {
    AddressPartition partition(4, umi_axi::spec_version_t::AMBA4);
    partition.SetAll(0x1000, 4096, 4);
    auto result = partition.Partition();
    EXPECT_EQ(result.size(), 4u);
    for (const auto& metadata : result) {
        EXPECT_EQ(metadata.data_len, 1024u);
        EXPECT_EQ(metadata.address, 0x1000u);
        EXPECT_EQ(metadata.burst, umi_axi::burst_type_t::FIXED);
    }
}

TEST_F(AddressPartitionTest, DataOffsetTracking) {
    AddressPartition partition(4, umi_axi::spec_version_t::AMBA4);
    partition.SetAll(0x1000, 256, 0);
    auto result = partition.Partition();
    uint32_t expected_offset = 0;
    for (const auto& metadata : result) {
        EXPECT_EQ(metadata.data_offset, expected_offset);
        expected_offset += metadata.data_len;
    }
}

TEST_F(AddressPartitionTest, BeatNumberCalculation) {
    AddressPartition partition(4, umi_axi::spec_version_t::AMBA4);
    partition.SetAll(0x1000, 256, 0);
    auto result = partition.Partition();
    for (const auto& metadata : result) {
        uint32_t expected_beats = (metadata.data_len + 3) / 4;  // Ceiling division by data_width
        EXPECT_EQ(metadata.beat_num, expected_beats);
    }
}


TEST_F(AddressPartitionTest, TlmPartitionNoStreaming) {
    AddressPartition partition(4, umi_axi::spec_version_t::AMBA4);
    partition.SetAll(0x1000, 0x100, 0);
    auto ranges = partition.TlmPartition();
    ASSERT_EQ(ranges.size(), 1u);
    EXPECT_EQ(ranges[0].start, 0x1000u);
    EXPECT_EQ(ranges[0].len, 0x100u);
    EXPECT_EQ(ranges[0].data_index, 0u);
    EXPECT_EQ(ranges[0].part_type, ssln::hybrid::axi::PartitionType::kIncr);
}

TEST_F(AddressPartitionTest, TlmPartitionWithStreaming) {
    AddressPartition partition(4, umi_axi::spec_version_t::AMBA4);
    partition.SetAll(0x1000, 0x100, 0x40);
    auto ranges = partition.TlmPartition();
    ASSERT_EQ(ranges.size(), 4u);
    for (size_t i = 0; i < ranges.size(); ++i) {
        EXPECT_EQ(ranges[i].start, 0x1000u);
        EXPECT_EQ(ranges[i].len, 0x40u);
        EXPECT_EQ(ranges[i].data_index, i * 0x40u);
        EXPECT_EQ(ranges[i].part_type, ssln::hybrid::axi::PartitionType::kMultiIncr);
    }
}

TEST_F(AddressPartitionTest, TlmPartitionWithStreamingLargerThanLen) {
    AddressPartition partition(4, umi_axi::spec_version_t::AMBA4);
    partition.SetAll(0x1000, 0x100, 0x200);
    auto ranges = partition.TlmPartition();
    ASSERT_EQ(ranges.size(), 1u);
    EXPECT_EQ(ranges[0].start, 0x1000u);
    EXPECT_EQ(ranges[0].len, 0x100u);
    EXPECT_EQ(ranges[0].data_index, 0u);
    EXPECT_EQ(ranges[0].part_type, ssln::hybrid::axi::PartitionType::kIncr);
}

TEST_F(AddressPartitionTest, TlmPartitionWithUnevenDivision) {
    AddressPartition partition(4, umi_axi::spec_version_t::AMBA4);
    partition.SetAll(0x1000, 0x110, 0x40);
    auto ranges = partition.TlmPartition();
    ASSERT_EQ(ranges.size(), 5u);
    for (size_t i = 0; i < 4; ++i) {
        EXPECT_EQ(ranges[i].start, 0x1000u);
        EXPECT_EQ(ranges[i].len, 0x40u);
        EXPECT_EQ(ranges[i].data_index, i * 0x40u);
        EXPECT_EQ(ranges[i].part_type, ssln::hybrid::axi::PartitionType::kMultiIncr);
    }
    EXPECT_EQ(ranges[4].start, 0x1000u);
    EXPECT_EQ(ranges[4].len, 0x10u);
    EXPECT_EQ(ranges[4].data_index, 0x100u);
    EXPECT_EQ(ranges[4].part_type, ssln::hybrid::axi::PartitionType::kMultiIncr);
}

TEST_F(AddressPartitionTest, TlmPartitionWithUnevenDivisionBigStream) {
    AddressPartition partition(4, umi_axi::spec_version_t::AMBA4);
    partition.SetAll(0x1000, 0x4000, 0x1200);
    auto ranges = partition.TlmPartition();
    ASSERT_EQ(ranges.size(), 4u);
    for (size_t i = 0; i < 3; ++i) {
        EXPECT_EQ(ranges[i].start, 0x1000u);
        EXPECT_EQ(ranges[i].len, 0x1200u);
        EXPECT_EQ(ranges[i].data_index, i * 0x1200u);
        EXPECT_EQ(ranges[i].part_type, ssln::hybrid::axi::PartitionType::kMultiIncr);
    }
    EXPECT_EQ(ranges[3].start, 0x1000u);
    EXPECT_EQ(ranges[3].len, 0xa00u);
    EXPECT_EQ(ranges[3].data_index, 0x3600u);
    EXPECT_EQ(ranges[3].part_type, ssln::hybrid::axi::PartitionType::kMultiIncr);
}

TEST_F(AddressPartitionTest, TlmPartitionWithUnevenDivisionUnalignedAddress) {
    AddressPartition partition(4, umi_axi::spec_version_t::AMBA4);
    partition.SetAll(0x1003, 0x110, 0x40);
    auto ranges = partition.TlmPartition();
    ASSERT_EQ(ranges.size(), 5u);
    for (size_t i = 0; i < 4; ++i) {
        EXPECT_EQ(ranges[i].start, 0x1003u);
        EXPECT_EQ(ranges[i].len, 0x40u);
        EXPECT_EQ(ranges[i].data_index, i * 0x40u);
        EXPECT_EQ(ranges[i].part_type, ssln::hybrid::axi::PartitionType::kMultiIncr);
    }
    EXPECT_EQ(ranges[4].start, 0x1003u);
    EXPECT_EQ(ranges[4].len, 0x10u);
    EXPECT_EQ(ranges[4].data_index, 0x100u);
    EXPECT_EQ(ranges[4].part_type, ssln::hybrid::axi::PartitionType::kMultiIncr);
}

}  // namespace ssln::hybrid::axi

int sc_main(int argc, char **argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
} 
