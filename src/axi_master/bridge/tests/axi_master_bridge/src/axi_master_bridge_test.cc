#include <gtest/gtest.h>
#include "axi_master_bridge.h"
#include "axi_master_proxy.h"
#include "axi_transaction_item.h"
#include <thread>
#include <systemc>
#include <tlm>
#include "ssln/sslogger.h"
using namespace std;
using namespace umi_axi;
using namespace ssln::hybrid::axi;
using namespace ssln::hybrid;

const int sleep_time = 200;

class ScStopModule: public sc_core::sc_module{
public:
    SC_HAS_PROCESS(ScStopModule);
    ScStopModule(sc_core::sc_module_name name): sc_core::sc_module(name){

        SC_THREAD(StopThread);
    }

    void StopThread(){
        while(true){
            wait(stop_event);
            sc_core::sc_stop();
        }
    }

    async_event stop_event;
};


class AxiMasterBridgeTest: public testing::Test{
public:
    void SetUp() override{
        std::cout << "create" << endl;
        string channel_name = "test_channel123";
        axi_proxy = make_shared<axi_master_proxy>(256, spec_version_t::AMBA4);

        axi_master_bridge = new AxiMasterBridge("axi_master_bridge", channel_name, axi_proxy);

        req_channel_ = new ipc::channel((channel_name + "_req").c_str(), ipc::sender);
        resp_channel_ = new ipc::channel((channel_name + "_resp").c_str(), ipc::receiver);

        sc_stop_module = new ScStopModule("stopmodule");
        first = false;
        sim_thread = new thread(
            [](){
                sc_core::sc_start();
            }
        );
    }

    void TearDown() override{
        axi_proxy->b_send_lists.clear();
        sc_stop_module->stop_event.notify();
        if(sim_thread->joinable()){
            sim_thread->join();
        }
        delete sim_thread;
        delete axi_master_bridge;
        delete sc_stop_module;
        req_channel_->disconnect();
        resp_channel_->disconnect();
        delete req_channel_;
        delete resp_channel_;
        // delete sc_core::sc_curr_simcontext;
        sc_core::sc_curr_simcontext = new sc_core::sc_simcontext();
        sc_core::sc_default_global_context = nullptr;
        // std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    }

    void call_afterrecv(void * resp){
        cout << "call_afterrecv s" << endl;
        axi_master_bridge->HandleResponse(resp);
        cout << "call_afterrecv e" << endl;
    }

    AxiMasterBridge* axi_master_bridge;
    shared_ptr<axi_master_proxy> axi_proxy;

    ScStopModule* sc_stop_module;
    ipc::channel* req_channel_;
    ipc::channel* resp_channel_;
    bool first{true};
    bool last{false};
    thread* sim_thread;
};


TEST_F(AxiMasterBridgeTest, R_INCR){

    uint32_t total_size = sizeof(TlmPayload);
    unsigned char* payload_ptr = new unsigned char[total_size];

    TlmPayload* payload = reinterpret_cast<TlmPayload*>(payload_ptr);
    payload->id = 0;
    payload->command = 0;
    payload->address = 0x0;
    payload->data_length = 0x100;
    payload->byte_enable_length = 0x0;
    payload->axuser_length = 0x0;
    payload->xuser_length = 0x0;
    payload->streaming_width = 0x100;
    payload->response = 0x0;
    payload->data = nullptr;

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    req_channel_->send(payload, total_size);

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));


    EXPECT_EQ(axi_proxy->b_send_lists.size(), 1);
    axi_transaction_item& item = axi_proxy->b_send_lists[0];
    EXPECT_EQ(item.addr, 0);
    //EXPECT_EQ(item.id, 0);
    EXPECT_EQ(item.len, 7);
    EXPECT_EQ(item.size, size_type_t::BYTE_32);
    EXPECT_EQ(item.burst, burst_type_t::INCR);
    EXPECT_EQ(item.cmd, cmd_type_t::READ);


    axi_transaction_item resp = item;
    resp.data = vector<unsigned char>(0x100, 0);
    resp.data_width = bus_width_t::BIT_256;
    for(int i = 0; i < 0x100; i++){
        resp.data[i] = i % 256;
    }
    for(int i = 0; i < resp.len + 1; i ++){
        resp.resp.push_back(response_type_t::OKAY);
    }

    call_afterrecv(&resp);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    auto recv_data = resp_channel_->recv();
    
    EXPECT_EQ(recv_data.empty(), false);
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    resp_payload->data = reinterpret_cast<uint8_t*>(resp_payload) + sizeof(TlmPayload);
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload) + payload->data_length);
    
    for(int i = 0; i < 0x100; i ++){
        EXPECT_EQ(resp_payload->data[i], i%256);
    }

    delete [] payload_ptr;
}


TEST_F(AxiMasterBridgeTest, R_INCR_MULTI){

    uint32_t total_size = sizeof(TlmPayload);
    unsigned char* payload_ptr = new unsigned char[total_size];

    TlmPayload* payload = reinterpret_cast<TlmPayload*>(payload_ptr);
    payload->id = 0;
    payload->command = 0;
    payload->address = 0x0;
    payload->data_length = 0x3000;
    payload->byte_enable_length = 0x0;
    payload->axuser_length = 0x0;
    payload->xuser_length = 0x0;
    payload->streaming_width = 0x3000;
    payload->response = 0x0;
    payload->data = nullptr;

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    req_channel_->send(payload, total_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    EXPECT_EQ(axi_proxy->b_send_lists.size(), 3);
    // check burst
    
    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item& item = axi_proxy->b_send_lists[i];
        EXPECT_EQ(item.addr, i * 0x1000);
        //EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, 127);
        EXPECT_EQ(item.size, size_type_t::BYTE_32);
        EXPECT_EQ(item.burst, burst_type_t::INCR);
        EXPECT_EQ(item.cmd, cmd_type_t::READ);
    }

    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item resp = axi_proxy->b_send_lists[i];
        resp.data = vector<unsigned char>(0x1000, 0);
        resp.data_width = bus_width_t::BIT_256;
        for(int j = 0; j < 0x1000; j++){
            resp.data[j] = j % 256;
        }
        for(int j = 0; j < resp.len + 1; j ++){
            resp.resp.push_back(response_type_t::OKAY);
        }
        call_afterrecv(&resp);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    }

    auto recv_data = resp_channel_->recv();
    
    EXPECT_EQ(recv_data.empty(), false);
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    resp_payload->data = reinterpret_cast<uint8_t*>(resp_payload) + sizeof(TlmPayload);
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload) + payload->data_length);
    
    for(int i = 0; i < 0x3000; i ++){
        EXPECT_EQ(resp_payload->data[i], i%256);
    }

    delete [] payload_ptr;

}


TEST_F(AxiMasterBridgeTest, R_FIXED){

    uint32_t total_size = sizeof(TlmPayload);
    unsigned char* payload_ptr = new unsigned char[total_size];

    TlmPayload* payload = reinterpret_cast<TlmPayload*>(payload_ptr);
    payload->id = 0;
    payload->command = 0;
    payload->address = 0x0;
    payload->data_length = 0x100;
    payload->byte_enable_length = 0x0;
    payload->axuser_length = 0x0;
    payload->xuser_length = 0x0;
    payload->streaming_width = 32;
    payload->response = 0x0;
    payload->data = nullptr;

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    req_channel_->send(payload, total_size);

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    EXPECT_EQ(axi_proxy->b_send_lists.size(), 1);
    // check burst
    
    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item& item = axi_proxy->b_send_lists[i];
        EXPECT_EQ(item.addr, 0);
        //EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, 7);
        EXPECT_EQ(item.size, size_type_t::BYTE_32);
        EXPECT_EQ(item.burst, burst_type_t::FIXED);
        EXPECT_EQ(item.cmd, cmd_type_t::READ);
    }

    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item resp = axi_proxy->b_send_lists[i];
        resp.data = vector<unsigned char>(0x100, 0);
        resp.data_width = bus_width_t::BIT_256;
        for(int j = 0; j < 0x100; j++){
            resp.data[j] = j % 256;
        }
        for(int j = 0; j < resp.len + 1; j ++){
            resp.resp.push_back(response_type_t::OKAY);
        }
        call_afterrecv(&resp);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    }

    auto recv_data = resp_channel_->recv();
    
    EXPECT_EQ(recv_data.empty(), false);
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    resp_payload->data = reinterpret_cast<uint8_t*>(resp_payload) + sizeof(TlmPayload);
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload) + payload->data_length);
    
    for(int i = 0; i < 0x100; i ++){
        EXPECT_EQ(resp_payload->data[i], i%256);
    }

    delete [] payload_ptr;

}


TEST_F(AxiMasterBridgeTest, R_FIXED_MULTI){

    uint32_t total_size = sizeof(TlmPayload);
    unsigned char* payload_ptr = new unsigned char[total_size];

    TlmPayload* payload = reinterpret_cast<TlmPayload*>(payload_ptr);
    payload->id = 0;
    payload->command = 0;
    payload->address = 0x0;
    payload->data_length = 0x4000;
    payload->byte_enable_length = 0x0;
    payload->axuser_length = 0x0;
    payload->xuser_length = 0x0;
    payload->streaming_width = 32;
    payload->response = 0x0;
    payload->data = nullptr;

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    req_channel_->send(payload, total_size);

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    ASSERT_EQ(axi_proxy->b_send_lists.size(), 4);
    // check burst
    
    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item& item = axi_proxy->b_send_lists[i];
        EXPECT_EQ(item.addr, 0);
        //EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, 127);
        EXPECT_EQ(item.size, size_type_t::BYTE_32);
        EXPECT_EQ(item.burst, burst_type_t::FIXED);
        EXPECT_EQ(item.cmd, cmd_type_t::READ);
    }

    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item resp = axi_proxy->b_send_lists[i];
        resp.data = vector<unsigned char>(0x1000, 0);
        resp.data_width = bus_width_t::BIT_256;
        for(int j = 0; j < 0x1000; j++){
            resp.data[j] = j % 256;
        }
        for(int j = 0; j < resp.len + 1; j ++){
            resp.resp.push_back(response_type_t::OKAY);
        }
        call_afterrecv(&resp);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    }

    std::cout  << "wait recv" << std::endl;
    auto recv_data = resp_channel_->recv();
    
    EXPECT_EQ(recv_data.empty(), false);
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    resp_payload->data = reinterpret_cast<uint8_t*>(resp_payload) + sizeof(TlmPayload);
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload) + payload->data_length);
    
    for(int i = 0; i < 0x4000; i ++){
        EXPECT_EQ(resp_payload->data[i], i%256);
    }

    delete [] payload_ptr;

}



TEST_F(AxiMasterBridgeTest, R_INCR_UNALIGN){

    uint32_t total_size = sizeof(TlmPayload);
    unsigned char* payload_ptr = new unsigned char[total_size];

    TlmPayload* payload = reinterpret_cast<TlmPayload*>(payload_ptr);
    payload->id = 0;
    payload->command = 0;
    payload->address = 0x10;
    payload->data_length = 0x2020;
    payload->byte_enable_length = 0x0;
    payload->axuser_length = 0x0;
    payload->xuser_length = 0x0;
    payload->streaming_width = 0x2020;
    payload->response = 0x0;
    payload->data = nullptr;

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    req_channel_->send(payload, total_size);

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    ASSERT_EQ(axi_proxy->b_send_lists.size(), 3);
    // check burst
    
    uint8_t t_len[3] = {127, 127, 1};
    uint64_t t_addr[3] = {0x10, 0x1000, 0x2000};
    uint64_t t_align_addr[3] = {0x0, 0x1000, 0x2000};
    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item& item = axi_proxy->b_send_lists[i];
        EXPECT_EQ(item.addr, t_addr[i]);
        //EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, t_len[i]);
        EXPECT_EQ(item.size, size_type_t::BYTE_32);
        EXPECT_EQ(item.burst, burst_type_t::INCR);
        EXPECT_EQ(item.cmd, cmd_type_t::READ);
    }

    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item resp = axi_proxy->b_send_lists[i];
        resp.data_width = bus_width_t::BIT_256;
        uint32_t burst_len = (t_len[i] + 1)*32;
        resp.data = vector<unsigned char>(burst_len, 0);
        for(int j = 0; j < burst_len; j++){
            resp.data[j] = (t_align_addr[i] + j) % 256;
        }
        for(int j = 0; j < resp.len + 1; j ++){
            resp.resp.push_back(response_type_t::OKAY);
        }
        call_afterrecv(&resp);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    }
    auto recv_data = resp_channel_->recv();
    
    EXPECT_EQ(recv_data.empty(), false);
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    resp_payload->data = reinterpret_cast<uint8_t*>(resp_payload) + sizeof(TlmPayload);
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload) + payload->data_length);
    
    for(int i = 0; i < 0x2020; i ++){
        EXPECT_EQ(resp_payload->data[i], (i + 0x10)%256);
    }

    delete [] payload_ptr;

}


TEST_F(AxiMasterBridgeTest, R_FIXED_UNALIGN){

    uint32_t total_size = sizeof(TlmPayload);
    unsigned char* payload_ptr = new unsigned char[total_size];

    TlmPayload* payload = reinterpret_cast<TlmPayload*>(payload_ptr);
    payload->id = 0;
    payload->command = 0;
    payload->address = 0x10;
    payload->data_length = 0x810;
    payload->byte_enable_length = 0x0;
    payload->axuser_length = 0x0;
    payload->xuser_length = 0x0;
    payload->streaming_width = 0x8;
    payload->response = 0x0;
    payload->data = nullptr;

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    req_channel_->send(payload, total_size);

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    ASSERT_EQ(axi_proxy->b_send_lists.size(), 3);
    // check burst
    uint8_t t_len[3] = {127, 127, 1};
    
    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item& item = axi_proxy->b_send_lists[i];
        EXPECT_EQ(item.addr, 0x10);
        //EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, t_len[i]);
        EXPECT_EQ(item.size, size_type_t::BYTE_32);
        EXPECT_EQ(item.burst, burst_type_t::FIXED);
        EXPECT_EQ(item.cmd, cmd_type_t::READ);
    }

    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item resp = axi_proxy->b_send_lists[i];
        resp.data_width = bus_width_t::BIT_256;
        uint32_t t_burst_len = (resp.len + 1) * (1 << resp.size);
        resp.data = vector<unsigned char>(t_burst_len, 0);

        for(int j = 0; j < resp.len + 1; j++){
            for(int k = 0 ; k < (1 << resp.size); k++){
                resp.data[j*(1 << resp.size) + k] = (k % 256);
            }
        }
        for(int j = 0; j < resp.len + 1; j ++){
            resp.resp.push_back(response_type_t::OKAY);
        }
        call_afterrecv(&resp);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    }

    auto recv_data = resp_channel_->recv();
    
    EXPECT_EQ(recv_data.empty(), false);
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    resp_payload->data = reinterpret_cast<uint8_t*>(resp_payload) + sizeof(TlmPayload);
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload) + payload->data_length);
    
    for(int i = 0; i < 0x810; i+=0x8){
        for(int j = 0; j < 0x8; j++){
            ASSERT_EQ(resp_payload->data[i+j], (0x10 + j)%256);
        }
    }

    delete [] payload_ptr;

}




TEST_F(AxiMasterBridgeTest, R_INCR_4K){

    uint32_t total_size = sizeof(TlmPayload);
    unsigned char* payload_ptr = new unsigned char[total_size];

    TlmPayload* payload = reinterpret_cast<TlmPayload*>(payload_ptr);
    payload->id = 0;
    payload->command = 0;
    payload->address = 0xff0; // 4080
    payload->data_length = 0x20; // 4080 + 32 = 4112
    payload->byte_enable_length = 0x0;
    payload->axuser_length = 0x0;
    payload->xuser_length = 0x0;
    payload->streaming_width = 0x20;
    payload->response = 0x0;
    payload->data = nullptr;

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    req_channel_->send(payload, total_size);

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));


    EXPECT_EQ(axi_proxy->b_send_lists.size(), 2);
    uint64_t t_addr[2] = {0xff0, 0x1000};
    uint64_t t_align_addr[2] = {0xfe0, 0x1000};
    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item& item = axi_proxy->b_send_lists[i];
        EXPECT_EQ(item.addr, t_addr[i]);
        //EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, 0);
        EXPECT_EQ(item.size, size_type_t::BYTE_32);
        EXPECT_EQ(item.burst, burst_type_t::INCR);
        EXPECT_EQ(item.cmd, cmd_type_t::READ);
    }
    

    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item resp = axi_proxy->b_send_lists[i];
        resp.data = vector<unsigned char>(0x20, 0);
        resp.data_width = bus_width_t::BIT_256;
        for(int j = 0; j < 0x20; j++){
            resp.data[j] = (j + t_align_addr[i]) % 256;
        }
        for(int j = 0; j < resp.len + 1; j ++){
            resp.resp.push_back(response_type_t::OKAY);
        }

        call_afterrecv(&resp);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    }
    

    auto recv_data = resp_channel_->recv();
    
    EXPECT_EQ(recv_data.empty(), false);
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    resp_payload->data = reinterpret_cast<uint8_t*>(resp_payload) + sizeof(TlmPayload);
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload) + payload->data_length);
    
    for(int i = 0; i < 0x20; i ++){
        EXPECT_EQ(resp_payload->data[i], (i + 0xff0)%256);
    }

    delete [] payload_ptr;
}


TEST_F(AxiMasterBridgeTest, R_FIXED_4K){

    uint32_t total_size = sizeof(TlmPayload);
    unsigned char* payload_ptr = new unsigned char[total_size];

    TlmPayload* payload = reinterpret_cast<TlmPayload*>(payload_ptr);
    payload->id = 0;
    payload->command = 0;
    payload->address = 0xff0;
    payload->data_length = 0x40;
    payload->byte_enable_length = 0x0;
    payload->axuser_length = 0x0;
    payload->xuser_length = 0x0;
    payload->streaming_width = 0x20;
    payload->response = 0x0;
    payload->data = nullptr;

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    req_channel_->send(payload, total_size);

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    ASSERT_EQ(axi_proxy->b_send_lists.size(), 4);
    // check burst
    uint32_t t_addr[4] = {0xff0, 0x1000, 0xff0, 0x1000};
    uint32_t t_align_addr[4] = {0xfe0, 0x1000, 0xfe0, 0x1000};
    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item& item = axi_proxy->b_send_lists[i];
        EXPECT_EQ(item.addr, t_addr[i]);
        //EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, 0);
        EXPECT_EQ(item.size, size_type_t::BYTE_32);
        EXPECT_EQ(item.burst, burst_type_t::INCR);
        EXPECT_EQ(item.cmd, cmd_type_t::READ);
    }

    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item resp = axi_proxy->b_send_lists[i];
        resp.data_width = bus_width_t::BIT_256;
        uint32_t t_burst_len = (resp.len + 1) * (1 << resp.size);
        resp.data = vector<unsigned char>(t_burst_len, 0);

        for(int j = 0; j < resp.len + 1; j++){
            for(int k = 0 ; k < (1 << resp.size); k++){
                resp.data[j*(1 << resp.size) + k] = ((k + t_align_addr[i]) % 256);
            }
        }
        for(int j = 0; j < resp.len + 1; j ++){
            resp.resp.push_back(response_type_t::OKAY);
        }
        call_afterrecv(&resp);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    }

    auto recv_data = resp_channel_->recv();
    
    EXPECT_EQ(recv_data.empty(), false);
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    resp_payload->data = reinterpret_cast<uint8_t*>(resp_payload) + sizeof(TlmPayload);
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload) + payload->data_length);
    
    for(int i = 0; i < 0x40; i+=0x20){
        for(int j = 0; j < 0x20; j++){
            EXPECT_EQ(resp_payload->data[i+j], (0xff0 + j)%256);
        }
    }

    delete [] payload_ptr;

}



TEST_F(AxiMasterBridgeTest, W_INCR){

    uint32_t data_size = 0x100;
    uint32_t total_size = sizeof(TlmPayload) + data_size;
    unsigned char* payload_ptr = new unsigned char[total_size];

    TlmPayload* payload = reinterpret_cast<TlmPayload*>(payload_ptr);
    payload->id = 0;
    payload->command = 1;
    payload->address = 0x0;
    payload->data_length = 0x100;
    payload->byte_enable_length = 0x0;
    payload->axuser_length = 0x0;
    payload->xuser_length = 0x0;
    payload->streaming_width = 0x100;
    payload->response = 0x0;
    payload->data = payload_ptr + sizeof(TlmPayload);
    for(int i = 0; i < payload->data_length; i += payload->streaming_width){
        for(int j = 0; j < payload->streaming_width; j++){
            if(i + j >= data_size) break;
            payload->data[i + j] = (payload->address + j) % 256;
        }
    }

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    req_channel_->send(payload, total_size);

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));


    EXPECT_EQ(axi_proxy->b_send_lists.size(), 1);
    axi_transaction_item& item = axi_proxy->b_send_lists[0];
    EXPECT_EQ(item.addr, 0);
    //EXPECT_EQ(item.id, 0);
    EXPECT_EQ(item.len, 7);
    EXPECT_EQ(item.size, size_type_t::BYTE_32);
    EXPECT_EQ(item.burst, burst_type_t::INCR);
    EXPECT_EQ(item.data.size(), data_size);
    EXPECT_EQ(item.be.size(), data_size);
    EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
    uint32_t burst_len = (item.len + 1) * (1 << item.size);
    for(int i = 0; i < item.data.size(); i++){
        EXPECT_EQ(item.data[i], (item.addr + i) % 256);
    }
    for(int i = 0; i < item.be.size(); i++){
        EXPECT_EQ(item.be[i], 0xff);
    }

    axi_transaction_item resp = item;
    resp.data.clear();
    for(int i = 0; i < 1; i ++){
        resp.resp.push_back(response_type_t::OKAY);
    }

    call_afterrecv(&resp);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    auto recv_data = resp_channel_->recv();
    
    EXPECT_EQ(recv_data.empty(), false);
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload));
    EXPECT_EQ(resp_payload->response, tlm::TLM_OK_RESPONSE);


    delete [] payload_ptr;
}



TEST_F(AxiMasterBridgeTest, W_INCR_MULTI){
    uint32_t data_size = 0x3000;
    uint32_t total_size = sizeof(TlmPayload) + data_size;
    unsigned char* payload_ptr = new unsigned char[total_size];

    TlmPayload* payload = reinterpret_cast<TlmPayload*>(payload_ptr);
    payload->id = 0;
    payload->command = 1;
    payload->address = 0x0;
    payload->data_length = data_size;
    payload->byte_enable_length = 0x0;
    payload->axuser_length = 0x0;
    payload->xuser_length = 0x0;
    payload->streaming_width = data_size;
    payload->response = 0x0;
    payload->data = payload_ptr + sizeof(TlmPayload);


    for(int i = 0; i < payload->data_length; i += payload->streaming_width){
        for(int j = 0; j < payload->streaming_width; j++){
            if(i + j >= data_size) break;
            payload->data[i + j] = (payload->address + j) % 256;
        }
    }

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    req_channel_->send(payload, total_size);

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    EXPECT_EQ(axi_proxy->b_send_lists.size(), 3);
    // check burst
    
    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item& item = axi_proxy->b_send_lists[i];
        EXPECT_EQ(item.addr, i * 0x1000);
        //EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, 127);
        EXPECT_EQ(item.size, size_type_t::BYTE_32);
        EXPECT_EQ(item.burst, burst_type_t::INCR);
        EXPECT_EQ(item.data.size(), 0x1000);
        EXPECT_EQ(item.be.size(), 0x1000);
        EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
        for(int j = 0; j < 0x1000; j++){
            EXPECT_EQ(item.data[j], (item.addr + j) % 256);
        }
        for(int j = 0; j < 0x1000; j++){
            EXPECT_EQ(item.be[j], 0xff);
        }
    }

    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item resp = axi_proxy->b_send_lists[i];
        resp.data.clear();
        resp.data_width = bus_width_t::BIT_256;
        
        for(int j = 0; j < 1; j ++){
            resp.resp.push_back(response_type_t::OKAY);
        }
        call_afterrecv(&resp);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    }

    auto recv_data = resp_channel_->recv();
    
    EXPECT_EQ(recv_data.empty(), false);
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    resp_payload->data = reinterpret_cast<uint8_t*>(resp_payload) + sizeof(TlmPayload);
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload));
    EXPECT_EQ(resp_payload->response, tlm::TLM_OK_RESPONSE);

    delete [] payload_ptr;

}



TEST_F(AxiMasterBridgeTest, W_FIXED){
    uint32_t data_size = 0x100;
    uint32_t total_size = sizeof(TlmPayload) + data_size;
    unsigned char* payload_ptr = new unsigned char[total_size];

    TlmPayload* payload = reinterpret_cast<TlmPayload*>(payload_ptr);
    payload->id = 0;
    payload->command = 1;
    payload->address = 0x0;
    payload->data_length = data_size;
    payload->byte_enable_length = 0x0;
    payload->axuser_length = 0x0;
    payload->xuser_length = 0x0;
    payload->streaming_width = 32;
    payload->response = 0x0;
    payload->data = payload_ptr + sizeof(TlmPayload);

    for(int i = 0; i < payload->data_length; i += payload->streaming_width){
        for(int j = 0; j < payload->streaming_width; j++){
            if(i + j >= data_size) break;
            payload->data[i + j] = (payload->address + j) % 256;
        }
    }

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    req_channel_->send(payload, total_size);

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    EXPECT_EQ(axi_proxy->b_send_lists.size(), 1);
    // check burst
    
    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item& item = axi_proxy->b_send_lists[i];
        EXPECT_EQ(item.addr, 0);
        //EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, 7);
        EXPECT_EQ(item.size, size_type_t::BYTE_32);
        EXPECT_EQ(item.burst, burst_type_t::FIXED);
        EXPECT_EQ(item.data.size(), 0x100);
        EXPECT_EQ(item.be.size(), 0x100);
        EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
        for(int j = 0; j < item.len + 1; j++){
            for(int k = 0; k < (1 << item.size); k++){
                EXPECT_EQ(item.data[j * 32 + k], (item.addr + k) % 256);
                EXPECT_EQ(item.be[j * 32 + k], 0xff);
            }
        }
    }

    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item resp = axi_proxy->b_send_lists[i];
        resp.data.clear();
        resp.data_width = bus_width_t::BIT_256;
        for(int j = 0; j < 1; j ++){
            resp.resp.push_back(response_type_t::OKAY);
        }
        call_afterrecv(&resp);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    }

    auto recv_data = resp_channel_->recv();
    
    EXPECT_EQ(recv_data.empty(), false);
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    resp_payload->data = reinterpret_cast<uint8_t*>(resp_payload) + sizeof(TlmPayload);
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload));
    EXPECT_EQ(resp_payload->response, tlm::TLM_OK_RESPONSE);
    

    delete [] payload_ptr;

}


TEST_F(AxiMasterBridgeTest, W_FIXED_MULTI){
    uint32_t data_size = 0x4000;
    uint32_t total_size = sizeof(TlmPayload) + data_size;
    unsigned char* payload_ptr = new unsigned char[total_size];

    TlmPayload* payload = reinterpret_cast<TlmPayload*>(payload_ptr);
    payload->id = 0;
    payload->command = 1;
    payload->address = 0x0;
    payload->data_length = data_size;
    payload->byte_enable_length = 0x0;
    payload->axuser_length = 0x0;
    payload->xuser_length = 0x0;
    payload->streaming_width = 32;
    payload->response = 0x0;
    payload->data = payload_ptr + sizeof(TlmPayload);

    for(int i = 0; i < payload->data_length; i += payload->streaming_width){
        for(int j = 0; j < payload->streaming_width; j++){
            if(i + j >= data_size) break;
            payload->data[i + j] = (payload->address + j) % 256;
        }
    }

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    req_channel_->send(payload, total_size);

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    ASSERT_EQ(axi_proxy->b_send_lists.size(), 4);
    // check burst
    
    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item& item = axi_proxy->b_send_lists[i];
        EXPECT_EQ(item.addr, 0);
        //EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, 127);
        EXPECT_EQ(item.size, size_type_t::BYTE_32);
        EXPECT_EQ(item.burst, burst_type_t::FIXED);
        EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
        EXPECT_EQ(item.data.size(), 0x1000);
        for(int j = 0; j < item.len + 1; j++){
            for(int k = 0; k < (1 << item.size); k++){
                EXPECT_EQ(item.data[j * 32 + k], (item.addr + k) % 256);
                EXPECT_EQ(item.be[j * 32 + k], 0xff);
            }
        }
    }

    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item resp = axi_proxy->b_send_lists[i];
        resp.data.clear();
        resp.data_width = bus_width_t::BIT_256;
        for(int j = 0; j < 1; j ++){
            resp.resp.push_back(response_type_t::OKAY);
        }
        call_afterrecv(&resp);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    }

    auto recv_data = resp_channel_->recv();
    
    EXPECT_EQ(recv_data.empty(), false);
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    resp_payload->data = reinterpret_cast<uint8_t*>(resp_payload) + sizeof(TlmPayload);
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload));
    EXPECT_EQ(resp_payload->response, tlm::TLM_OK_RESPONSE);

    delete [] payload_ptr;

}



TEST_F(AxiMasterBridgeTest, W_INCR_UNALIGN){
    uint32_t data_size = 0x2020;
    uint32_t total_size = sizeof(TlmPayload) + data_size;
    unsigned char* payload_ptr = new unsigned char[total_size];

    TlmPayload* payload = reinterpret_cast<TlmPayload*>(payload_ptr);
    payload->id = 0;
    payload->command = 1;
    payload->address = 0x10;
    payload->data_length = data_size;
    payload->byte_enable_length = 0x0;
    payload->axuser_length = 0x0;
    payload->xuser_length = 0x0;
    payload->streaming_width = data_size;
    payload->response = 0x0;
    payload->data = payload_ptr + sizeof(TlmPayload);

    for(int i = 0; i < payload->data_length; i += payload->streaming_width){
        for(int j = 0; j < payload->streaming_width; j++){
            if(i + j >= data_size) break;
            payload->data[i + j] = (payload->address + j) % 256;
        }
    }

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    req_channel_->send(payload, total_size);

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    ASSERT_EQ(axi_proxy->b_send_lists.size(), 3);
    // check burst
    
    uint8_t t_len[3] = {127, 127, 1};
    uint64_t t_addr[3] = {0x10, 0x1000, 0x2000};
    uint64_t t_align_addr[3] = {0x0, 0x1000, 0x2000};
    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item& item = axi_proxy->b_send_lists[i];
        EXPECT_EQ(item.addr, t_addr[i]);
        //EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, t_len[i]);
        EXPECT_EQ(item.size, size_type_t::BYTE_32);
        EXPECT_EQ(item.burst, burst_type_t::INCR);
        EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
        EXPECT_EQ(item.data.size(), (t_len[i] + 1) * 32);
        uint32_t burst_len = (t_len[i] + 1) * 32;
        for(int j = 0; j < t_len[i] + 1; j++){
            for(int k = 0; k < (1 << item.size); k++){
                if(t_align_addr[i] + j * 32 + k < t_addr[i]) continue;
                if(t_align_addr[i] + j * 32 + k >= data_size + payload->address) continue;
                EXPECT_EQ(item.data[j * 32 + k], (t_align_addr[i] + j * 32 + k) % 256);
            }
        }

        for(int j = 0; j < t_len[i] + 1; j++){
            for(int k = 0; k < (1 << item.size); k++){
                if(t_align_addr[i] + j * 32 + k < t_addr[i]){
                    EXPECT_EQ(item.be[j * 32 + k], 0x00);
                }
                else if(t_align_addr[i] + j * 32 + k >= data_size + payload->address){
                    EXPECT_EQ(item.be[j * 32 + k], 0x00);
                }
                else{
                    EXPECT_EQ(item.be[j * 32 + k], 0xff);
                }
            }
        }

    }

    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item resp = axi_proxy->b_send_lists[i];
        resp.data_width = bus_width_t::BIT_256;
        resp.data.clear();
        for(int j = 0; j < 1; j ++){
            resp.resp.push_back(response_type_t::OKAY);
        }
        call_afterrecv(&resp);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    }
    auto recv_data = resp_channel_->recv();
    
    EXPECT_EQ(recv_data.empty(), false);
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    resp_payload->data = reinterpret_cast<uint8_t*>(resp_payload) + sizeof(TlmPayload);
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload));
    EXPECT_EQ(resp_payload->response, tlm::TLM_OK_RESPONSE);

    delete [] payload_ptr;

}



TEST_F(AxiMasterBridgeTest, W_FIXED_UNALIGN){
    uint32_t data_size = 0x810;
    uint32_t total_size = sizeof(TlmPayload) + data_size;
    unsigned char* payload_ptr = new unsigned char[total_size];

    TlmPayload* payload = reinterpret_cast<TlmPayload*>(payload_ptr);
    payload->id = 0;
    payload->command = 1;
    payload->address = 0x10;
    payload->data_length = 0x810;
    payload->byte_enable_length = 0x0;
    payload->axuser_length = 0x0;
    payload->xuser_length = 0x0;
    payload->streaming_width = 0x8;
    payload->response = 0x0;
    payload->data = payload_ptr + sizeof(TlmPayload);

    for(int i = 0; i < payload->data_length; i += payload->streaming_width){
        for(int j = 0; j < payload->streaming_width; j++){
            if(i  + j >= data_size) break;
            payload->data[i + j] = (payload->address + j) % 256;
        }
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    req_channel_->send(payload, total_size);

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    ASSERT_EQ(axi_proxy->b_send_lists.size(), 3);
    // check burst
    uint8_t t_len[3] = {127, 127, 1};
    
    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item& item = axi_proxy->b_send_lists[i];
        EXPECT_EQ(item.addr, 0x10);
        //EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, t_len[i]);
        EXPECT_EQ(item.size, size_type_t::BYTE_32);
        EXPECT_EQ(item.burst, burst_type_t::FIXED);
        EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
        EXPECT_EQ(item.data.size(), (t_len[i] + 1) * 32);
        EXPECT_EQ(item.be.size(), (t_len[i] + 1) * 32);
        for(int j = 0; j < item.len + 1; j++){
            for(int k = 0; k < (1 << item.size); k++){
                if(k < 0x10) continue;
                if(k >= 0x18) continue;
                EXPECT_EQ(item.data[j * 32 + k] , k % 256);
            }
        }
        for(int j = 0; j < item.len + 1; j++){
            for(int k = 0; k < (1 << item.size); k++){
                if(k < 0x10){
                    ASSERT_EQ(item.be[j * 32 + k] , 0x00);
                }
                else if(k >= 0x18){  
                    ASSERT_EQ(item.be[j * 32 + k] , 0x00);
                }
                else{
                    ASSERT_EQ(item.be[j * 32 + k] , 0xff);
                }
            }
        }
    }

    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item resp = axi_proxy->b_send_lists[i];
        resp.data_width = bus_width_t::BIT_256;
        resp.data.clear();

        for(int j = 0; j < 1; j ++){
            resp.resp.push_back(response_type_t::OKAY);
        }
        call_afterrecv(&resp);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    }

    auto recv_data = resp_channel_->recv();
    
    EXPECT_EQ(recv_data.empty(), false);
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    resp_payload->data = reinterpret_cast<uint8_t*>(resp_payload) + sizeof(TlmPayload);
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload));
    EXPECT_EQ(resp_payload->response, tlm::TLM_OK_RESPONSE);
    

    delete [] payload_ptr;

}



TEST_F(AxiMasterBridgeTest, W_INCR_4K){
    uint32_t data_size = 0x20;
    uint32_t total_size = sizeof(TlmPayload) + data_size;
    unsigned char* payload_ptr = new unsigned char[total_size];

    TlmPayload* payload = reinterpret_cast<TlmPayload*>(payload_ptr);
    payload->id = 0;
    payload->command = 1;
    payload->address = 0xff0; // 4080
    payload->data_length = 0x20; // 4080 + 32 = 4112
    payload->byte_enable_length = 0x0;
    payload->axuser_length = 0x0;
    payload->xuser_length = 0x0;
    payload->streaming_width = 0x20;
    payload->response = 0x0;
    payload->data = payload_ptr + sizeof(TlmPayload);

    for(int i = 0; i < payload->data_length; i += payload->streaming_width){
        for(int j = 0; j < payload->streaming_width; j++){
            if(i  + j >= data_size) break;
            payload->data[i + j] = (payload->address + j) % 256;
        }
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    req_channel_->send(payload, total_size);

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));


    EXPECT_EQ(axi_proxy->b_send_lists.size(), 2);
    uint64_t t_addr[2] = {0xff0, 0x1000};
    uint64_t t_align_addr[2] = {0xfe0, 0x1000};
    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item& item = axi_proxy->b_send_lists[i];
        EXPECT_EQ(item.addr, t_addr[i]);
        //EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, 0);
        EXPECT_EQ(item.size, size_type_t::BYTE_32);
        EXPECT_EQ(item.burst, burst_type_t::INCR);
        EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
        EXPECT_EQ(item.data.size(), 32);
        for(int j = 0; j < 32; j++){
            if(t_align_addr[i] + j < t_addr[i]) continue;
            if(t_align_addr[i] + j >= payload->address + payload->data_length) continue; 
            EXPECT_EQ(item.data[j], (t_align_addr[i] + j) % 256);
        }
        for(int j = 0; j < 32; j++){
            if(t_align_addr[i] + j < t_addr[i]){
                EXPECT_EQ(item.be[j], 0x00);
            }
            else if(t_align_addr[i] + j >= payload->address + payload->data_length){
                EXPECT_EQ(item.be[j], 0x00);
            }
            else{
                EXPECT_EQ(item.be[j], 0xff);
            }
        }
    }
    

    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item resp = axi_proxy->b_send_lists[i];
        resp.data.clear();
        resp.data_width = bus_width_t::BIT_256;
        for(int j = 0; j < 1; j ++){
            resp.resp.push_back(response_type_t::OKAY);
        }

        call_afterrecv(&resp);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    }
    

    auto recv_data = resp_channel_->recv();
    
    EXPECT_EQ(recv_data.empty(), false);
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    resp_payload->data = reinterpret_cast<uint8_t*>(resp_payload) + sizeof(TlmPayload);
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload));
    EXPECT_EQ(resp_payload->response, tlm::TLM_OK_RESPONSE);

    delete [] payload_ptr;
}



TEST_F(AxiMasterBridgeTest, W_FIXED_4K){
    uint32_t data_size = 0x40;
    uint32_t total_size = sizeof(TlmPayload) + data_size;
    unsigned char* payload_ptr = new unsigned char[total_size];

    TlmPayload* payload = reinterpret_cast<TlmPayload*>(payload_ptr);
    payload->id = 0;
    payload->command = 1;
    payload->address = 0xff0;
    payload->data_length = 0x40;
    payload->byte_enable_length = 0x0;
    payload->axuser_length = 0x0;
    payload->xuser_length = 0x0;
    payload->streaming_width = 0x20;
    payload->response = 0x0;
    payload->data = payload_ptr + sizeof(TlmPayload);

    for(int i = 0; i < payload->data_length; i += payload->streaming_width){
        for(int j = 0; j < payload->streaming_width; j++){
            if(i  + j >= data_size) break;
            payload->data[i + j] = (payload->address + j) % 256;
        }
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    req_channel_->send(payload, total_size);

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    ASSERT_EQ(axi_proxy->b_send_lists.size(), 4);
    // check burst
    uint32_t t_addr[4] = {0xff0, 0x1000, 0xff0, 0x1000};
    uint32_t t_align_addr[4] = {0xfe0, 0x1000, 0xfe0, 0x1000};
    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item& item = axi_proxy->b_send_lists[i];
        EXPECT_EQ(item.addr, t_addr[i]);
        //EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, 0);
        EXPECT_EQ(item.size, size_type_t::BYTE_32);
        EXPECT_EQ(item.burst, burst_type_t::INCR);
        EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
        EXPECT_EQ(item.data.size(), 32);
        for(int j = 0; j < 32; j++){
            if(t_align_addr[i] + j < t_addr[i]) continue;
            if(t_align_addr[i] + j >= t_addr[i] + 0x10) continue;
            EXPECT_EQ(item.data[j], (t_align_addr[i] + j) % 256);
        }
        for(int j = 0; j < 32; j++){
            if(t_align_addr[i] + j < t_addr[i]){
                EXPECT_EQ(item.be[j], 0x00);
            }
            else if(t_align_addr[i] + j >= t_addr[i] + 0x10){
                EXPECT_EQ(item.be[j], 0x00);
            }
            else{
                EXPECT_EQ(item.be[j], 0xff);
            }
        }
    }

    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item resp = axi_proxy->b_send_lists[i];
        resp.data_width = bus_width_t::BIT_256;
        uint32_t t_burst_len = (resp.len + 1) * (1 << resp.size);
        resp.data.clear();
        for(int j = 0; j < 1; j ++){
            resp.resp.push_back(response_type_t::OKAY);
        }
        call_afterrecv(&resp);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    }

    auto recv_data = resp_channel_->recv();
    
    EXPECT_EQ(recv_data.empty(), false);
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    resp_payload->data = reinterpret_cast<uint8_t*>(resp_payload) + sizeof(TlmPayload);
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload));
    EXPECT_EQ(resp_payload->response, tlm::TLM_OK_RESPONSE);

    delete [] payload_ptr;

}


TEST_F(AxiMasterBridgeTest, W_BE_INCR){

    uint32_t data_size = 0x100;
    uint32_t be_size = 0x100;
    uint32_t total_size = sizeof(TlmPayload) + data_size + be_size;
    unsigned char* payload_ptr = new unsigned char[total_size];

    TlmPayload* payload = reinterpret_cast<TlmPayload*>(payload_ptr);
    payload->id = 0;
    payload->command = 1;
    payload->address = 0x0;
    payload->data_length = 0x100;
    payload->byte_enable_length = 0x100;
    payload->axuser_length = 0x0;
    payload->xuser_length = 0x0;
    payload->streaming_width = 0x100;
    payload->response = 0x0;
    payload->data = payload_ptr + sizeof(TlmPayload);
    for(int i = 0; i < payload->data_length; i += payload->streaming_width){
        uint8_t* data_ptr = payload->data + i;
        for(int j = 0; j < payload->streaming_width; j++){
            if(i + j >= data_size) break;
            data_ptr[i + j] = (payload->address + j) % 256;
        }
        uint8_t* be_ptr = payload->data + payload->data_length + i;
        for(int j = 0; j < payload->streaming_width; j++){
            if(i + j >= be_size) break;
            be_ptr[i + j] = (i + j) % 2 ? 0xff : 0x0;
        }
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    req_channel_->send(payload, total_size);

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));


    EXPECT_EQ(axi_proxy->b_send_lists.size(), 1);
    axi_transaction_item& item = axi_proxy->b_send_lists[0];
    EXPECT_EQ(item.addr, 0);
    //EXPECT_EQ(item.id, 0);
    EXPECT_EQ(item.len, 7);
    EXPECT_EQ(item.size, size_type_t::BYTE_32);
    EXPECT_EQ(item.burst, burst_type_t::INCR);
    EXPECT_EQ(item.data.size(), data_size);
    EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
    EXPECT_EQ(item.be.size(), be_size);
    uint32_t burst_len = (item.len + 1) * (1 << item.size);
    for(int i = 0; i < item.data.size(); i++){
        EXPECT_EQ(item.data[i], (item.addr + i) % 256);
    }
    for(int i = 0; i < item.be.size(); i++){
        EXPECT_EQ(item.be[i], i % 2 ? 0xff : 0x0);
    }

    axi_transaction_item resp = item;
    resp.data.clear();
    for(int i = 0; i < 1; i ++){
        resp.resp.push_back(response_type_t::OKAY);
    }

    call_afterrecv(&resp);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    auto recv_data = resp_channel_->recv();
    
    EXPECT_EQ(recv_data.empty(), false);
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload));
    EXPECT_EQ(resp_payload->response, tlm::TLM_OK_RESPONSE);


    delete [] payload_ptr;
}



TEST_F(AxiMasterBridgeTest, W_BE_FIXED){
    uint32_t data_size = 0x100;
    uint32_t be_size = 32;
    uint32_t total_size = sizeof(TlmPayload) + data_size +  be_size;
    unsigned char* payload_ptr = new unsigned char[total_size];

    TlmPayload* payload = reinterpret_cast<TlmPayload*>(payload_ptr);
    payload->id = 0;
    payload->command = 1;
    payload->address = 0x0;
    payload->data_length = data_size;
    payload->byte_enable_length = be_size;
    payload->axuser_length = 0x0;
    payload->xuser_length = 0x0;
    payload->streaming_width = 32;
    payload->response = 0x0;
    payload->data = payload_ptr + sizeof(TlmPayload);

    for(int i = 0; i < payload->data_length; i += payload->streaming_width){
        uint8_t* data_ptr = payload->data + i;
        for(int j = 0; j < payload->streaming_width; j++){
            if(i + j >= data_size) break;
            data_ptr[j] = (payload->address + j) % 256;
        }
    }
    uint8_t* be_ptr = payload->data + payload->data_length;
    for(int j = 0; j < payload->streaming_width; j++){
        if(j >= be_size) break;
        be_ptr[j] = j % 2 ? 0xff : 0x0;
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    req_channel_->send(payload, total_size);

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    EXPECT_EQ(axi_proxy->b_send_lists.size(), 1);
    // check burst
    
    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item& item = axi_proxy->b_send_lists[i];
        EXPECT_EQ(item.addr, 0);
        //EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, 7);
        EXPECT_EQ(item.size, size_type_t::BYTE_32);
        EXPECT_EQ(item.burst, burst_type_t::FIXED);
        EXPECT_EQ(item.data.size(), 0x100);
        EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
        EXPECT_EQ(item.be.size(), 0x100);
        // cout << "!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!" << endl;
        for(int j = 0; j < item.len + 1; j++){
            for(int k = 0; k < (1 << item.size); k++){
                EXPECT_EQ(item.data[j * 32 + k], (item.addr + k) % 256);
                // cout << int(item.data[j * 32 + k]) << " ";
            }
            // cout << endl;
        }
        // cout << "be size: " << item.be.size() << endl;
        // cout << "+++++++++++++++++++++++++++++++++++" << endl;
        for(int j = 0; j < item.len + 1; j++){
            for(int k = 0; k < (1 << item.size); k++){
                EXPECT_EQ(item.be[j * 32 + k], (item.addr + k) % 2 ? 0xff : 0x0);
                // cout << int(item.be[j * 32 + k]) << " ";
            }
            // cout << endl;
        }
        // cout << "------------------------------------" << endl;

    }
    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item resp = axi_proxy->b_send_lists[i];
        resp.data.clear();
        resp.data_width = bus_width_t::BIT_256;
        for(int j = 0; j < 1; j ++){
            resp.resp.push_back(response_type_t::OKAY);
        }
        call_afterrecv(&resp);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    }

    auto recv_data = resp_channel_->recv();
    
    EXPECT_EQ(recv_data.empty(), false);
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    resp_payload->data = reinterpret_cast<uint8_t*>(resp_payload) + sizeof(TlmPayload);
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload));
    EXPECT_EQ(resp_payload->response, tlm::TLM_OK_RESPONSE);
    

    delete [] payload_ptr;

}


int sc_main(int argc, char* argv[]) {
    // delete ssln::file_logger;
    // ssln::file_logger->flush_log();
    return 0;
}