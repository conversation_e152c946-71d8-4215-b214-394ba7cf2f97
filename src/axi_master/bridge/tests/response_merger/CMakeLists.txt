add_executable(response_merger_test
    src/response_merger_test.cc
)


target_link_directories(response_merger_test
    PRIVATE
        ${PROJECT_ROOT}/thirdparty/fake_install/lib
        $ENV{UMICOM_HOME}/lib
)

target_link_libraries(response_merger_test
    PRIVATE
        GTest::gtest_main
        axi_master_bridge
        fake_xtor
        systemc
)

include(GoogleTest)
gtest_discover_tests(response_merger_test)

add_dependencies(build_utests response_merger_test)