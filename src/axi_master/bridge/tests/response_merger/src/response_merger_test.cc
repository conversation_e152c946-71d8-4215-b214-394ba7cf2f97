#include <systemc.h>
#include <gtest/gtest.h>
#include "transaction_manager.h"
#include "address_partition.h"
#include "response_merger.h"


using namespace std;
using namespace ssln::hybrid::axi;
using namespace umi_axi;

class ResponseMergerTest: public testing::Test{
public:
    void SetUp() override{
        trans_manager = new TransactionManager();
        resp_merger = new ResponseMerger(*trans_manager);
    }

    void TearDown() override{
        delete trans_manager;
        delete resp_merger;
    }

    TransactionManager* trans_manager;
    ResponseMerger* resp_merger;

};

TEST_F(ResponseMergerTest, MergeResponsesFunction){
    unsigned char* payload_ptr = new unsigned char [sizeof(ssln::hybrid::TlmPayload)];

    ssln::hybrid::TlmPayload* payload = reinterpret_cast<ssln::hybrid::TlmPayload*>(payload_ptr);
    payload->id = 0;
    payload->command = 0;
    payload->address = 0x0;
    payload->data_length = 0x404;
    payload->byte_enable_length = 0x0;
    payload->axuser_length = 0x0;
    payload->xuser_length = 0x0;
    payload->streaming_width = 0x4;
    payload->response = 0x0;
    payload->data = nullptr;

    AddressPartition partitioner(
        0x20, // data width(bytes)
        umi_axi::spec_version_t::AMBA4, // sepc
        8, // axlen width(bits)
        3 // axsize width(bits)
    );

    partitioner.SetAll(payload->address, payload->data_length, payload->streaming_width);

    auto split_transactions = partitioner.Partition();
    EXPECT_EQ(split_transactions.size(), 3);
    trans_manager->RegisterTransaction(payload, split_transactions);
    umi_axi::axi_transaction_item* resp = new umi_axi::axi_transaction_item();
    resp->commu_tag.msg_id = 0;
    resp->data = vector<unsigned char>(4096, 0);
    resp->data_width = umi_axi::bus_width_t::BIT_256;
    resp->addr = 0;
    resp->len = 127;
    resp->cmd = cmd_type_t::READ;
    resp->burst = burst_type_t::FIXED;
    resp->size = size_type_t::BYTE_32;
    for(int i = 0; i < 4096; i++){
        resp->data[i] = i % 256;
    }
    bool complete = trans_manager->UpdateTransaction(resp);
    EXPECT_EQ(complete, false);
    resp->commu_tag.msg_id = 1;
    resp->data = vector<unsigned char>(4096, 0);
    for(int i = 0; i < 4096; i++){
        resp->data[i] = i % 256;
    }
    complete = trans_manager->UpdateTransaction(resp);
    EXPECT_EQ(complete, false);
    resp->commu_tag.msg_id = 2;
    resp->data = vector<unsigned char>(32, 0);
    resp->len = 0;
    for(int i = 0; i < 32; i++){
        resp->data[i] = i % 256;
    }
    complete = trans_manager->UpdateTransaction(resp);
    EXPECT_EQ(complete, true);
    auto resp_payload = resp_merger->MergeResponses(0);
    for(int i = 0; i < 0x20; i += 4){
        int t = i % 0x200;
        t /= 4;
        t *= 32;
        for(int j = 0; j < 4; j++){
            EXPECT_EQ(int(resp_payload->data[i + j]), (t + j) % 256);
        }
    }
}



int sc_main(int argc, char* argv[]) {
    return 0;
}