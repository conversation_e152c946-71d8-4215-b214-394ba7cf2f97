add_executable(transaction_manager_test
    src/transaction_manager_test.cc
)


target_link_directories(transaction_manager_test
    PRIVATE
        ${PROJECT_ROOT}/thirdparty/fake_install/lib
        $ENV{UMICOM_HOME}/lib
)

target_link_libraries(transaction_manager_test
    PRIVATE
        GTest::gtest_main
        axi_master_bridge
        fake_xtor
        systemc
)

include(GoogleTest)
gtest_discover_tests(transaction_manager_test)

add_dependencies(build_utests transaction_manager_test)