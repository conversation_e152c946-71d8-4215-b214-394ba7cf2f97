#include <systemc.h>
#include <gtest/gtest.h>

#include "transaction_manager.h"
#include "address_partition.h"

using namespace std;
using namespace ssln::hybrid::axi;
class TransactionManagerTest: public testing::Test{
public:
    void SetUp() override{
        trans_manager = new TransactionManager();
    }

    void TearDown() override{
        delete trans_manager;
    }

    TransactionManager* trans_manager;

};

TEST_F(TransactionManagerTest, RegisterTransactionFunction){
    
    unsigned char* data = new unsigned char [0x4];

    ssln::hybrid::TlmPayload* payload = new ssln::hybrid::TlmPayload();
    payload->id = 0;
    payload->command = 0;
    payload->address = 0x0;
    payload->data_length = 0x4;
    payload->byte_enable_length = 0x0;
    payload->axuser_length = 0x0;
    payload->xuser_length = 0x0;
    payload->streaming_width = 0x4;
    payload->response = 0x0;
    payload->data = data;


    AddressPartition partitioner(
        0x20, // data width(bytes)
        umi_axi::spec_version_t::AMBA4, // sepc
        8, // axlen width(bits)
        3 // axsize width(bits)
    );

    partitioner.SetAll(payload->address, payload->data_length, payload->streaming_width);
    auto split_transactions = partitioner.Partition();
    trans_manager->RegisterTransaction(payload, split_transactions);

}

TEST_F(TransactionManagerTest, FindTransactionFunction){
    unsigned char* data = new unsigned char [0x404];

    ssln::hybrid::TlmPayload* payload = new ssln::hybrid::TlmPayload();
    payload->id = 0;
    payload->command = 0;
    payload->address = 0x0;
    payload->data_length = 0x404;
    payload->byte_enable_length = 0x0;
    payload->axuser_length = 0x0;
    payload->xuser_length = 0x0;
    payload->streaming_width = 0x4;
    payload->response = 0x0;
    payload->data = data;

    AddressPartition partitioner(
        0x20, // data width(bytes)
        umi_axi::spec_version_t::AMBA4, // sepc
        8, // axlen width(bits)
        3 // axsize width(bits)
    );

    partitioner.SetAll(payload->address, payload->data_length, payload->streaming_width);
    auto split_transactions = partitioner.Partition();
    EXPECT_EQ(split_transactions.size(), 3);
    trans_manager->RegisterTransaction(payload, split_transactions);
    auto it = trans_manager->FindTransaction(1);
    EXPECT_EQ(it.second->axi_id, 1);
    EXPECT_EQ(it.second->address, 0);
    EXPECT_EQ(it.second->data_len, 0x200);
}

TEST_F(TransactionManagerTest, UpdateTransactionFunction){
    unsigned char* data = new unsigned char [0x404];

    ssln::hybrid::TlmPayload* payload = new ssln::hybrid::TlmPayload();
    payload->id = 0;
    payload->command = 0;
    payload->address = 0x0;
    payload->data_length = 0x404;
    payload->byte_enable_length = 0x0;
    payload->axuser_length = 0x0;
    payload->xuser_length = 0x0;
    payload->streaming_width = 0x4;
    payload->response = 0x0;
    payload->data = data;

    AddressPartition partitioner(
        0x20, // data width(bytes)
        umi_axi::spec_version_t::AMBA4, // sepc
        8, // axlen width(bits)
        3 // axsize width(bits)
    );

    partitioner.SetAll(payload->address, payload->data_length, payload->streaming_width);
    auto split_transactions = partitioner.Partition();
    EXPECT_EQ(split_transactions.size(), 3);
    trans_manager->RegisterTransaction(payload, split_transactions);
    umi_axi::axi_transaction_item* resp = new umi_axi::axi_transaction_item();
    resp->commu_tag.msg_id = 0;
    resp->data = vector<unsigned char>(4096, 0);
    resp->data_width = umi_axi::bus_width_t::BIT_32;
    resp->addr = 0;
    resp->len = 127;
    bool complete = trans_manager->UpdateTransaction(resp);
    EXPECT_EQ(complete, false);
    resp->commu_tag.msg_id = 1;
    complete = trans_manager->UpdateTransaction(resp);
    EXPECT_EQ(complete, false);
    resp->commu_tag.msg_id = 2;
    resp->data = vector<unsigned char>(32, 0);
    resp->len = 0;
    complete = trans_manager->UpdateTransaction(resp);
    EXPECT_EQ(complete, true);
}

TEST_F(TransactionManagerTest, CleanupTransactionFunction){
    unsigned char* data = new unsigned char [0x404];

    ssln::hybrid::TlmPayload* payload = new ssln::hybrid::TlmPayload();
    payload->id = 0;
    payload->command = 0;
    payload->address = 0x0;
    payload->data_length = 0x404;
    payload->byte_enable_length = 0x0;
    payload->axuser_length = 0x0;
    payload->xuser_length = 0x0;
    payload->streaming_width = 0x4;
    payload->response = 0x0;
    payload->data = data;

    AddressPartition partitioner(
        0x20, // data width(bytes)
        umi_axi::spec_version_t::AMBA4, // sepc
        8, // axlen width(bits)
        3 // axsize width(bits)
    );

    partitioner.SetAll(payload->address, payload->data_length, payload->streaming_width);
    auto split_transactions = partitioner.Partition();
    EXPECT_EQ(split_transactions.size(), 3);
    trans_manager->RegisterTransaction(payload, split_transactions);
    trans_manager->CleanupTransaction(0);
    auto gp = trans_manager->GetPayload(0);
    EXPECT_EQ(gp, nullptr);
    auto trans = trans_manager->FindTransaction(1);
    EXPECT_EQ(trans.second, nullptr);
}

TEST_F(TransactionManagerTest, GetPayloadFunction){
    unsigned char* data = new unsigned char [0x404];

    ssln::hybrid::TlmPayload* payload = new ssln::hybrid::TlmPayload();
    payload->id = 0;
    payload->command = 0;
    payload->address = 0x0;
    payload->data_length = 0x404;
    payload->byte_enable_length = 0x0;
    payload->axuser_length = 0x0;
    payload->xuser_length = 0x0;
    payload->streaming_width = 0x4;
    payload->response = 0x0;
    payload->data = data;

    AddressPartition partitioner(
        0x20, // data width(bytes)
        umi_axi::spec_version_t::AMBA4, // sepc
        8, // axlen width(bits)
        3 // axsize width(bits)
    );

    partitioner.SetAll(payload->address, payload->data_length, payload->streaming_width);
    auto split_transactions = partitioner.Partition();
    EXPECT_EQ(split_transactions.size(), 3);
    trans_manager->RegisterTransaction(payload, split_transactions);
    auto gp = trans_manager->GetPayload(0);
    EXPECT_NE(gp, payload); // deep copy
}

TEST_F(TransactionManagerTest, GetContextFunction){
    unsigned char* data = new unsigned char [0x404];

    ssln::hybrid::TlmPayload* payload = new ssln::hybrid::TlmPayload();
    payload->id = 0;
    payload->command = 0;
    payload->address = 0x0;
    payload->data_length = 0x404;
    payload->byte_enable_length = 0x0;
    payload->axuser_length = 0x0;
    payload->xuser_length = 0x0;
    payload->streaming_width = 0x4;
    payload->response = 0x0;
    payload->data = data;

    AddressPartition partitioner(
        0x20, // data width(bytes)
        umi_axi::spec_version_t::AMBA4, // sepc
        8, // axlen width(bits)
        3 // axsize width(bits)
    );

    partitioner.SetAll(payload->address, payload->data_length, payload->streaming_width);
    auto split_transactions = partitioner.Partition();
    EXPECT_EQ(split_transactions.size(), 3);
    trans_manager->RegisterTransaction(payload, split_transactions);

    auto context = trans_manager->GetContext(0);
    for(auto v: context->completed){
        EXPECT_FALSE(v);
    }
    for(int i = 0; i < 3; i++){
        EXPECT_EQ(context->axi_ids[i], i);
    }
}


int sc_main(int argc, char* argv[]) {
    return 0;
}