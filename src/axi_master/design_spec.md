# AXI Master Design Specification

## IPC Design

### IPC Implementation Choice

Based on comprehensive performance testing comparing different IPC mechanisms, we have chosen to use cpp-ipc as our primary IPC implementation for the following reasons:

1. **Lower Latency**: Performance tests show that cpp-ipc consistently provides lower latency compared to boost::interprocess::message_queue.

2. **Smart Memory Management**:
   - For small messages: Uses heap memory allocation
   - For large messages: Automatically uses shared memory pointers
   - This hybrid approach reduces unnecessary data copying, especially beneficial for large AXI transactions

3. **Zero-Copy Support**: 
   - When handling large payloads (like AXI burst transfers), cpp-ipc can return direct shared memory pointers
   - Eliminates need for extra memory copies between processes
   - Particularly efficient for DMA and burst transfers

4. **Flexible Message Sizes**:
   - Supports variable-length data efficiently
   - No need to pre-define maximum message size
   - Automatically handles different payload sizes (64B to 4KB+)

### Channel Design

The IPC system will use two channels:
1. Request Channel: For sending AXI transaction requests
2. Response Channel: For receiving transaction responses

Each channel will be implemented using cpp-ipc's channel mechanism with the following features:
- Lock-free queue for high performance
- Zero-copy data transfer for large payloads
- Automatic memory management

### Message Format

Transaction messages will contain:
```cpp
struct TlmPayload {
    uint64_t id;                // Payload ID
    uint8_t command;           // Command type
    uint64_t address;           // Target address
    uint32_t data_length;       // Length of data
    uint32_t byte_enable_length;// Length of byte enable
    uint32_t axuser_length;     // Length of axuser
    uint32_t xuser_length;     // Length of xuser
    uint32_t streaming_width;   // streaming width
    uint8_t response;
    char data[];               // Variable length data followed by byte enable
};
```

Proxy:
- For READ command, data is null when sent. data is valid in response.
- For WRITE command, data is valid when sent. data is null in response.

Bridge:
- For READ command, data is null when received. data is valid in response.
- For WRITE command, data is valid when received. data is null in response.

### Performance Characteristics

Based on performance testing:
- Latency: Consistently lower than message queue implementation
- Support for variable data sizes (64B - 4KB) without performance degradation
- Efficient handling of both small and large transfers
- Minimal CPU overhead due to zero-copy design

### Error Handling

The IPC implementation will include:
1. Robust error detection and reporting
2. Automatic cleanup of shared resources
3. Graceful handling of process disconnection
4. Recovery mechanisms for communication failures

## Memory Management

### Shared Memory Strategy

- Use cpp-ipc's built-in memory management for optimal performance
- Leverage zero-copy capabilities for large transfers
- Automatic memory pool management for variable-size allocations

### Buffer Management

- Dynamic buffer sizing based on transaction requirements
- Efficient memory reuse through pooling
- Zero-copy transfers when possible
- Smart pointer management for safe memory handling

## Implementation Guidelines

1. Use cpp-ipc's channel API for all IPC communications
2. Implement zero-copy transfers for large payloads
3. Utilize shared memory pointers for data transfers where possible
4. Maintain separate request and response channels
5. Implement proper cleanup and resource management
6. Add comprehensive error handling and recovery mechanisms

## Testing Requirements

1. Performance testing with various payload sizes (64B to 4KB)
2. Stress testing with high-frequency transactions
3. Error handling and recovery testing
4. Memory leak testing
5. Process crash recovery testing

## CPP-IPC Usage Guidelines

### Basic Channel Setup

```cpp
// Producer side
ipc::channel req_channel{channel_name, ipc::sender};
ipc::channel resp_channel{channel_name + "_resp", ipc::receiver};

// Consumer side
ipc::channel req_channel{channel_name, ipc::receiver};
ipc::channel resp_channel{channel_name + "_resp", ipc::sender};
```

### Channel Synchronization

1. **Connection Establishment**:
```cpp
// Wait for consumer/producer to connect
req_channel.wait_for_recv(1);  // Wait for receiver
resp_channel.wait_for_recv(1); // Wait for response channel
```

### Data Transfer

1. **Sending Data**:
```cpp
// Send with size
bool success = channel.send(buffer.data(), buffer_size);

// Receive data
auto recv_data = channel.recv();  // Returns ipc::buff_t
if (!recv_data.empty()) {
    // Process data
    auto* payload = reinterpret_cast<const TlmPayload*>(recv_data.data());
}
```

### Important Considerations

1. **Memory Management**:
   - For small messages (<16KB): Data is allocated on heap
   - For large messages (>16KB): Data is automatically allocated in shared memory
   - Received data pointer is valid until next receive operation

2. **Buffer Handling**:
   - No need to pre-allocate fixed size buffers
   - Received buffer (ipc::buff_t) automatically manages memory
   - Zero-copy is automatically used for large messages

3. **Error Handling**:
   - Check send() return value for success
   - Check received buffer size before accessing
   - Handle disconnect scenarios gracefully

4. **Resource Cleanup**:
```cpp
// Cleanup channels
channel.disconnect();  // Automatically cleans up resources

// Remove shared memory (if needed)
ipc::shm::remove(channel_name);
```

### Best Practices

1. **Channel Naming**:
   - Use unique names for each channel pair
   - Add suffixes for request/response channels
   - Example: "axi_master_0_req", "axi_master_0_resp"

2. **Performance Optimization**:
   - Minimize message sizes for small transfers
   - Use zero-copy for large transfers (happens automatically)
   - Batch small messages when possible

3. **Error Recovery**:
   - Implement reconnection logic for lost connections
   - Handle process crashes gracefully
   - Clean up shared resources on exit

4. **Thread Safety**:
   - Each channel should be accessed by single thread
   - Use separate channels for different threads
   - Avoid sharing channel objects between threads

### Example Usage Pattern

```cpp
// Producer implementation
class Producer {
public:
    void run() {
        ipc::channel req_channel{"axi_master", ipc::sender};
        ipc::channel resp_channel{"axi_master_resp", ipc::receiver};
        
        // Wait for consumer
        req_channel.wait_for_recv(1);
        resp_channel.wait_for_recv(1);
        
        // Send data
        TlmPayload payload{/*...*/};
        if (!req_channel.send(&payload, sizeof(payload) + payload.data_length + payload.byte_enable_length + payload.axuser_length + payload.xuser_length)) {
            // Handle send error
        }
        
        // Receive response
        auto resp = resp_channel.recv();
        if (!resp.empty()) {
            // Process response
        }
    }
};

// Consumer implementation
class Consumer {
public:
    void run() {
        ipc::channel req_channel{"axi_master", ipc::receiver};
        ipc::channel resp_channel{"axi_master_resp", ipc::sender};
        
        while (running_) {
            // data is valid in this scope only
            auto data = req_channel.recv();
            if (data.empty()) continue;
            
            auto* payload = reinterpret_cast<const TlmPayload*>(data.data());
            // Process payload...
            
            // Send response
            int response = 1;
            resp_channel.send(&response, sizeof(response));
        }
    }
private:
    std::atomic<bool> running_{true};
};
```

### Common Pitfalls to Avoid

1. **Resource Leaks**:
   - Always call disconnect() when done
   - Clean up shared memory if no longer needed
   - Don't forget to remove channels on abnormal exit

2. **Buffer Management**:
   - Don't store received buffer pointers
   - Process received data before next receive
   - Don't assume fixed message sizes

3. **Error Handling**:
   - Don't ignore send() return values
   - Check received buffer size
   - Handle disconnection scenarios

4. **Performance**:
   - Don't copy large messages unnecessarily
   - Don't create/destroy channels frequently
   - Don't share channels between threads

---

## Proxy Implementation

### Overview

The AXI Master Proxy acts as a bridge between SystemC/TLM and the AXI Master Bridge process. It implements the TLM interface and converts TLM transactions to IPC messages.

### Class Structure

```cpp
#include "async_event.h"

class AxiMasterTlmProxy : 
    public sc_core::sc_module,
    public tlm::tlm_fw_transport_if<>
{
public:
    tlm::tlm_target_socket<> targ_sock;  // TLM target socket

private:
    static constexpr size_t MAX_PENDING_TRANSACTIONS = 1;
    
    // Transaction context for tracking pending transactions
    struct TransactionContext {
        tlm::tlm_generic_payload* payload;
        bool response_received;
        async_event *response_event;
    };
    
    std::array<TransactionContext, MAX_PENDING_TRANSACTIONS> transactions_;
    std::atomic<uint64_t> payload_id_{0};
    
    // IPC channels
    ipc::channel req_channel_;   // For sending requests
    ipc::channel resp_channel_;  // For receiving responses
    
    // SystemC event for response handling
    async_event response_event_;
    
};
```

### Key Components

1. **Transaction Management**:
   - Uses payload ID for tracking transactions
   - b_transport is blocking
   - only 1 pending transaction is supported
   - use queue for future nb_transport support

2. **IPC Communication**:
   - Request channel for sending TLM transactions
   - Response channel for receiving transaction responses
   - Zero-copy data transfer for large payloads

3. **SystemC Integration**:
   - CPP Thread for handling responses
   - Async_Event for synchronization from CPP Thread to SC Thread

### Implementation Details

#### 1. Transaction Processing

```cpp
// SystemC thread
void b_transport(tlm::tlm_generic_payload& payload, sc_core::sc_time& delay) {
    uint64_t current_id = payload_id_.fetch_add(1, std::memory_order_relaxed);
    uint32_t index = current_id % MAX_PENDING_TRANSACTIONS;
    
    // Setup transaction context
    auto& context = transactions_[index];
    context.payload = &payload;
    context.response_received = false;
    
    // Calculate total message size
    size_t msg_size = sizeof(TlmPayload) + 
                     payload.get_data_length() + 
                     payload.get_byte_enable_length();
    
    // Prepare IPC payload
    std::vector<char> buffer(msg_size);
    auto* tlm_payload = reinterpret_cast<TlmPayload*>(buffer.data());
    
    // Fill payload fields
    tlm_payload->command = static_cast<uint8_t>(payload.get_command());
    tlm_payload->address = payload.get_address();
    tlm_payload->data_length = payload.get_data_length();
    tlm_payload->byte_enable_length = payload.get_byte_enable_length();
    tlm_payload->streaming_width = payload.get_streaming_width();
    
    // Copy data for write commands
    if (payload.get_command() == tlm::TLM_WRITE_COMMAND) {
        std::memcpy(tlm_payload->data, 
                   payload.get_data_ptr(), 
                   payload.get_data_length());
        std::memcpy(tlm_payload->data + payload.get_data_length(),
                   payload.get_byte_enable_ptr(),
                   payload.get_byte_enable_length());
    }
    
    // Send request
    if (!req_channel_.send(buffer.data(), msg_size)) {
        payload.set_response_status(tlm::TLM_GENERIC_ERROR_RESPONSE);
        return;
    }
    
    // Wait for response
    if (!context.response_received) {
        wait(context.response_event);
    }
    
    // Clear context
    context.payload = nullptr;
}
```

#### 2. Response Handling

```cpp
// cpp thread
void handle_response() {
    while (true) {
        auto resp_data = resp_channel_.recv();
        if (resp_data.empty()) continue;
        
        auto* resp_payload = reinterpret_cast<const TlmPayload*>(resp_data.data());
        uint32_t index = resp_payload->id % MAX_PENDING_TRANSACTIONS;
        auto& context = transactions_[index];
        
        if (context.payload) {
            // Update original payload
            context.payload->set_response_status(
                static_cast<tlm::tlm_response_status>(resp_payload->response));
            
            // Copy read data if applicable
            if (context.payload->get_command() == tlm::TLM_READ_COMMAND) {
                std::memcpy(context.payload->get_data_ptr(),
                          resp_payload->data,
                          resp_payload->data_length);
            }
            
            // Notify waiting thread
            context.response_received = true;
            context.response_event.notify();
        }
    }
}
```

### Error Handling

1. **Transaction Errors**:
   - Timeout handling for unresponsive bridge
   - Error status propagation to TLM
   - Resource exhaustion detection

2. **IPC Errors**:
   - Channel disconnection handling
   - Send/receive failure recovery
   - Memory allocation failures

### Performance Considerations

1. **Zero-Copy Optimization**:
   - Direct shared memory access for large payloads
   - Minimal copying for small transactions
   - Efficient memory management

2. **Concurrency**:
   - Lock-free queue for response handling
   - Atomic operation for payload ID generation
   - Event-based synchronization

3. **Resource Management**:
   - Transaction context pooling (1 for b_transport)
   - Buffer reuse
   - Efficient cleanup


---

## Bridge Implementation

### Overview

The AXI Master Bridge is responsible for:
1. Receiving TLM transactions from the proxy via IPC
2. Splitting transactions into AXI-compliant transfers
3. Managing AXI transactions
4. Merging responses back into TLM payloads
5. Sending responses back to the proxy

### Data Flow

#### 1. Request Path (Proxy -> Bridge -> AXI)

```cpp
class AxiMasterBridge {
public:
    void ProcessRequest() {
    // this process is running in a cpp thread
        while (running_) {
            // 1. Receive TLM payload from IPC
            auto data = req_channel_.recv();
            if (data.empty()) continue;
            
            auto* payload = reinterpret_cast<TlmPayload*>(data.data());
            
            // 2. Split transaction
            auto split_transactions = splitter_->SplitTransaction(payload);
            
            // 3. Register with transaction manager
            transaction_manager_->RegisterTransaction(payload, split_transactions);
            
            // 4. Send each split transaction to AXI
            for (const auto& metadata : split_transactions) {
                auto axi_trans = CreateAxiTransaction(metadata, payload);
                axi_proxy_->b_send_transaction(axi_trans);
            }
        }
    }
};
```

1. **IPC Reception**:
   - Receive TLM payload from proxy via IPC
   - Payload contains command, address, data, and byte enables
   - Data is in shared memory for zero-copy access
   - Data is valid only in the recv scope

2. **Transaction Splitting**:
   - Analyze transaction requirements
   - Split based on AXI constraints:
     * 4KB address boundary
     * Maximum burst length
     * Data width alignment
   - Create metadata for each split transaction for response merging

3. **Transaction Registration**:
   - Register split transactions, and copy original payload (original payload will be deconstructed out of the recv scope)
   - Maintain mapping between TLM and AXI transactions
   - Track completion status

4. **AXI Transaction Creation**:
   - Got the AXI-specific parameters thru AXI Master Proxy at the beginning of AxiMasterBridge Construction
   - Convert TLM metadata to AXI transaction format
   - Handle data and byte enable copying
   - Set up AXI-specific parameters

#### 2. Response Path (AXI -> Bridge -> Proxy)

response function is one callback from AXI Master Proxy, which is in systemc thread.

```cpp
class AxiMasterBridge {
private:
    void HandleResponse(void* response) {
        auto axi_resp = static_cast<umi_axi::axi_transaction_item*>(response);
        
        // 1. Find original TLM payload
        auto [payload, metadata] = transaction_manager_->FindTransaction(axi_resp->id);
        if (!payload) return;
        
        // 2. Update transaction status
        bool all_completed = transaction_manager_->UpdateTransaction(axi_resp, metadata);
        
        // 3. If all split transactions completed, merge and send response
        if (all_completed) {
            auto resp_payload = response_merger_->MergeResponses(axi_resp->id);
            if (resp_payload) {
                SendResponse(resp_payload);
                // Cleanup transaction after sending response
                transaction_manager_->CleanupTransaction(axi_resp->id);
            }
        }
    }
};
```

1. **Response Reception**:
   - Receive AXI response via callback
   - Response contains:
    * Payload ID
     * Response status
     * Read data (for read transactions)

2. **Transaction Tracking**:
   - Find original TLM payload using axi transaction ID (this is one incremented number)
   - Update completion status
   - Store response data for read transactions

3. **Response Merging**:
   - When all split transactions complete:
     * Merge read data in correct order
     * Combine response statuses
     * Create final TLM response

4. **Response Transmission**:
   - Send merged response back to proxy via IPC
   - Include all necessary data and status

### Key Components

#### 1. Transaction Manager
```cpp
class TransactionManager {
public:
    // Register new transaction and its splits
    void RegisterTransaction(const TlmPayload* ipc_payload, 
                           const std::vector<AxiTransactionMetadata>& splits) {
        TransactionContext context;
        context.payload = CreateNewTlmPayload(ipc_payload);
        context.splits = splits;
        context.completed.resize(splits.size(), false);
        context.responses.resize(splits.size());
        
        // Store using payload id from proxy
        uint64_t payload_id = ipc_payload->id;
        transactions_[payload_id] = std::move(context);
        
        // Create mapping from AXI transaction IDs to payload ID
        for (size_t i = 0; i < splits.size(); ++i) {
            uint64_t axi_id = current_axi_id_.fetch_add(1, std::memory_order_relaxed);
            axi_to_payload_map_[axi_id] = payload_id;
            context.axi_ids.push_back(axi_id);
        }
    }
    
    // Find transaction by AXI response ID
    std::pair<TlmPayload*, AxiTransactionMetadata*> FindTransaction(uint64_t axi_id) {
        auto it = axi_to_payload_map_.find(axi_id);
        if (it == axi_to_payload_map_.end()) {
            return {nullptr, nullptr};
        }
        
        uint64_t payload_id = it->second;
        auto trans_it = transactions_.find(payload_id);
        if (trans_it == transactions_.end()) {
            return {nullptr, nullptr};
        }
        
        // Find the corresponding split transaction
        auto& context = trans_it->second;
        auto axi_it = std::find(context.axi_ids.begin(), context.axi_ids.end(), axi_id);
        if (axi_it == context.axi_ids.end()) {
            return {nullptr, nullptr};
        }
        
        size_t split_index = axi_it - context.axi_ids.begin();
        return {context.payload, &context.splits[split_index]};
    }
    
    // Update transaction status and return if all completed
    bool UpdateTransaction(const umi_axi::axi_transaction_item* response) {
        auto it = axi_to_payload_map_.find(response->id);
        if (it == axi_to_payload_map_.end()) {
            return false;
        }
        
        uint64_t payload_id = it->second;
        auto& context = transactions_[payload_id];
        
        // Find the split transaction index
        auto axi_it = std::find(context.axi_ids.begin(), context.axi_ids.end(), response->id);
        if (axi_it == context.axi_ids.end()) {
            return false;
        }
        
        size_t split_index = axi_it - context.axi_ids.begin();
        context.completed[split_index] = true;
        context.responses[split_index] = response->resp;
        
        // Check if all splits are completed
        return std::all_of(context.completed.begin(), context.completed.end(),
                          [](bool v) { return v; });
    }

    // Cleanup transaction and its mappings
    void CleanupTransaction(uint64_t payload_id) {
        auto it = transactions_.find(payload_id);
        if (it != transactions_.end()) {
            // Remove all AXI ID mappings
            for (uint64_t axi_id : it->second.axi_ids) {
                axi_to_payload_map_.erase(axi_id);
            }
            
            // Delete payload and remove from transactions map
            DeleteTlmPayload(it->second.payload);
            transactions_.erase(it);
        }
    }

private:
    struct TransactionContext {
        TlmPayload* payload;  // Owned TlmPayload with allocated space
        std::vector<AxiTransactionMetadata> splits;
        std::vector<bool> completed;
        std::vector<umi_axi::response_type_t> responses;
        std::vector<uint64_t> axi_ids;  // Store AXI IDs for each split
    };

    std::unordered_map<uint64_t, TransactionContext> transactions_;  // payload_id -> context
    std::unordered_map<uint64_t, uint64_t> axi_to_payload_map_;    // axi_id -> payload_id
    std::atomic<uint64_t> current_axi_id_{0};
};
```

#### 2. Response Merger
```cpp
class ResponseMerger {
public:
    // Merge multiple AXI responses into single TLM response
    TlmPayload* MergeResponses(uint64_t transaction_id) {
        // Get payload through public interface
        TlmPayload* payload = transaction_manager_.GetPayload(transaction_id);
        if (!payload) return nullptr;
        
        // For read transactions, merge data directly into pre-allocated space
        if (payload->command == TLM_READ_COMMAND) {
            MergeReadData(transaction_id);
        }
        
        // Set response status
        payload->response_status = DetermineResponseStatus(transaction_id);
        
        // Return payload (will be cleaned up by TransactionManager after IPC send)
        return payload;
    }
};
```

### Error Handling

1. **Transaction Level**:
   - Invalid transaction parameters
   - Splitting failures
   - Resource allocation failures

2. **AXI Protocol Level**:
   - SLVERR/DECERR responses
   - Timeout conditions
   - Protocol violations

3. **IPC Level**:
   - Communication failures
   - Channel disconnection
   - Memory access errors

### Performance Optimizations

1. **Memory Management**:
   - Zero-copy data transfer via shared memory
   - Efficient buffer management
   - Smart pointer usage for resource cleanup

2. **Transaction Processing**:
   - Parallel transaction handling
   - Efficient response merging
   - Optimized data copying

3. **Resource Utilization**:
   - Transaction pooling
   - Buffer reuse
   - Efficient metadata tracking

### AXI Transaction Creation

```cpp
class AxiMasterBridge {
private:
    std::shared_ptr<AxiTransactionItem> CreateAxiTransaction(
        const AxiTransactionMetadata& metadata, 
        const TlmPayload* payload,
        uint64_t axi_id) {  // Add axi_id parameter
        auto transaction = std::make_shared<AxiTransactionItem>();
        
        // Use the AXI ID for this split transaction
        transaction->id = axi_id;  // Not using payload->id
        transaction->cmd = metadata.cmd;
        transaction->addr = metadata.address;
        transaction->len = metadata.beat_num - 1;
        transaction->size = metadata.size;
        transaction->burst = metadata.burst;
        
        // Handle data for write transactions
        if (metadata.cmd == umi_axi::cmd_type_t::WRITE) {
            // Calculate total data size
            size_t total_size = metadata.beat_num * 
                              (1 << static_cast<uint32_t>(metadata.size));
            
            // Prepare data and strobe
            transaction->data.resize(total_size);
            transaction->strb.resize(total_size);
            
            // Copy data from the correct offset
            std::memcpy(transaction->data.data(),
                       payload->data + metadata.data_offset,
                       metadata.data_len);
            
            // Generate strobe based on byte enables if available
            if (payload->byte_enable_length > 0) {
                GenerateStrobe(transaction->strb,
                             payload->data + payload->data_length + metadata.data_offset,
                             metadata.data_len);
            } else {
                // If no byte enables, all bytes are valid
                std::fill(transaction->strb.begin(),
                         transaction->strb.end(),
                         0xFF);
            }
        }
        
        return transaction;
    }
    
    void ProcessRequest() {
        while (running_) {
            auto data = req_channel_.recv();
            if (data.empty()) continue;
            
            auto* payload = reinterpret_cast<TlmPayload*>(data.data());
            auto split_transactions = splitter_->SplitTransaction(payload);
            
            // Register with transaction manager and get AXI IDs
            transaction_manager_->RegisterTransaction(payload, split_transactions);
            
            // Get AXI IDs for each split transaction
            const auto& context = transaction_manager_->GetContext(payload->id);
            
            // Send each split transaction with its AXI ID
            for (size_t i = 0; i < split_transactions.size(); ++i) {
                auto axi_trans = CreateAxiTransaction(
                    split_transactions[i],
                    payload,
                    context.axi_ids[i]);
                axi_proxy_->b_send_transaction(axi_trans);
            }
        }
    }
};

This design ensures that:
1. The same ID is used throughout the entire transaction lifecycle
2. Proxy can track its requests using the ID it assigned
3. Bridge can correlate AXI responses with original TLM transactions
4. No need for additional ID management in the bridge
```

