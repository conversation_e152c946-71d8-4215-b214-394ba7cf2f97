# AXI Master Package Functionality Specification

## 1. Overview
The AXI Master Package provides comprehensive support for AXI4 protocol conversion between SystemC TLM and Transactor domains, which include 

- AXI Master Proxy (TLM Target)
- AXI Master Bridge 

```mermaid
flowchart
    Initiator[TLM Intiator] --|b_transport|--> Proxy[AXI Master Proxy]
    Proxy --|SHM|--> Bridge[AXI Master Bridge]
    Bridge --|axi_transaction_items|--> xtor[AXI Xtor]
```

Proxy gets tlm_generic_payload from initiator, converts it to TlmPayload and send it to Bridge.
Bridge convert TlmPayload to axi_transaction_items, then send to xtor.
X<PERSON> send the axi_transaction_items to Hardware and get response. 
<PERSON> gets the responses, merges them into one TlmPayload, and send it to Proxy.
Proxy convert TlmPayload to tlm_generic_payload, then b_transport returns to initiator.

In user system, Package can be instantiated multiple times. In this case, Proxy and Bridge will be instantiated multiple times.
- Each Proxy-Bridge pair operates independently
- No ordering guarantees between different Proxy-Bridge pairs
- Each pair maintains its own communication channel for better performance


### 1.1 SystemC TLM

- TLM b_transport

```cpp
enum tlm_command {
    TLM_READ_COMMAND,
    TLM_WRITE_COMMAND,
    TLM_IGNORE_COMMAND
};

enum tlm_response_status {
    TLM_OK_RESPONSE = 1,
    TLM_INCOMPLETE_RESPONSE = 0,
    TLM_GENERIC_ERROR_RESPONSE = -1,
    TLM_ADDRESS_ERROR_RESPONSE = -2,
    TLM_COMMAND_ERROR_RESPONSE = -3,
    TLM_BURST_ERROR_RESPONSE = -4,
    TLM_BYTE_ENABLE_ERROR_RESPONSE = -5
};

enum tlm_gp_option {
    TLM_MIN_PAYLOAD,
    TLM_FULL_PAYLOAD,
    TLM_FULL_PAYLOAD_ACCEPTED
};

#define TLM_BYTE_DISABLED 0x0
#define TLM_BYTE_ENABLED 0xff

class SC_API tlm_generic_payload {
private:
    sc_dt::uint64        m_address;
    tlm_command          m_command;
    unsigned char*       m_data;
    unsigned int         m_length;
    tlm_response_status  m_response_status;
    bool                 m_dmi;
    unsigned char*       m_byte_enable;
    unsigned int         m_byte_enable_length;
    unsigned int         m_streaming_width;
    tlm_gp_option        m_gp_option;
	...
}

```

### 1.2 Transactor

- void nb_send_transaction(axi_transaction_item& trans); 
- void b_send_transaction(axi_transaction_item& trans); 
- void register_after_recv_trans_cb(void (* const func)(void *));  // response callback

```cpp
class axi_transaction_item : public xtor_base_transaction_item {
public:
	typedef xtor_base_transaction_item super;
	//aw || ar channel 
	cmd_type_t						cmd; // read or write
	uint64_t						addr; 
	uint32_t						id;
	burst_type_t					burst;
	size_type_t						size;
	uint8_t							len;
	lock_type_t						lock;
	uint8_t							prot;
	uint8_t							cache;
	uint8_t							qos;
	uint8_t							region;
	vector<uint8_t>					req_user;
	domain_t						domain;
	uint32_t						snoop;
	barrier_t						bar;
									
	//data channel 
	vector<unsigned char>			data;
	vector<unsigned char>			be; // bit_enable
	vector<uint8_t>                data_user;
	vector<uint8_t>                 last;

	//resp field
	vector<response_type_t>			resp; //bresp and rresp
	vector<uint8_t>                resp_user;

	//width field
	uint32_t						addr_width;   //V2
	uint32_t						len_width;    //V2
	uint32_t						lock_width;   //V2 
	bus_width_t						data_width;
	uint32_t						id_width;

	uint32_t                        req_user_width;
	uint32_t                        resp_w_user_width;
	uint32_t                        data_w_user_width;
	uint32_t                        data_r_user_width;

	bool							enable_delay;
	uint32_t						delay_width;
	uint32_t						cmd_valid_delay;  //awvlid_delay,arvalid_delay
	vector<uint32_t>				data_valid_delay; //wvalid delay

	spec_version_t                  spec_ver;

	bool                            is_finished;
	bool                            wr_sop; 
	bool                            ar_sop; 
}
```



## 2. Supported Features

### 2.1 Protocol Support
- Full AXI4 protocol compliance
- TLM Blocking Mode operation

### 2.2 Burst Operations
#### 2.2.1 Burst Types
- WRAP burst support
- FIXED burst support
- INCR burst support

#### 2.2.2 Address Alignment
- Aligned address transactions
- Unaligned address transactions

### 2.3 Advanced Features
#### 2.3.1 Large Transaction Support
- Transactions larger than 4KB
- Automatic transaction splitting for 4KB boundary crossing
- Proper handling of split transaction ordering

#### 2.3.2 User Data Support
- support for AXI user signals
- User data preservation during protocol conversion

## 3. Master Bridge Specifications
### 3.1 Features
- Initiates AXI transactions from TLM side
- Handles all burst types (FIXED/INCR)
- Supports unaligned transfers
- Manages 4KB boundary crossing
- Preserves transaction ordering

### 3.2 Transaction Flow
1. TLM transaction initiation
2. Protocol conversion to AXI
3. Burst decomposition if needed
4. Address alignment handling
5. Data transfer execution
6. Response aggregation
7. TLM response generation

## 4. Implementation Details
### 4.1 Burst Support
- WRAP burst: Full circular wrapping support
- FIXED burst: Same address repeated access
- INCR burst: Incremental address access
- Proper burst termination handling

### 4.2 Address Handling
- Alignment checking and adjustment
- Narrow transfer decomposition
- Address boundary checking
- 4KB boundary crossing management

### 4.3 Data Width Conversion
- Byte lane management

### 4.4 Protocol Conversion and Transaction Management
#### 4.4.1 TLM to AXI Conversion
- Dedicated converter class for TLM payload to AXI transaction items conversion
- Support for splitting one TLM payload into multiple AXI transaction items
- Handling of different burst types during conversion
- Address alignment and boundary checks during conversion

#### 4.4.2 Transaction Management
- Transaction manager to track relationships between TLM payloads and AXI transaction items
- Correlation mechanism:
  - Each TLM payload is assigned a unique incremental ID
  - Each AXI transaction item has its own unique incremental ID
  - Transaction manager maintains mappings between TLM payload IDs and their corresponding AXI transaction item IDs
- Response aggregation from multiple AXI transaction items
- Reconstruction of TLM payload response from AXI responses
- Maintenance of transaction ordering and consistency

### 4.5 User Signal Handling
- user signal support

## 5. Limitations and Constraints
### 5.1 Current Limitations
- TLM Blocking Mode only

### 5.2 Performance Considerations
- Memory boundary handling overhead

## 6. Future Enhancements
### 6.1 Planned Features
- Enhanced performance profiling

### 6.2 Potential Improvements
- Xtor Non-blocking mode support
- TLM non-blocking mode support
- Network support (Proxy and Bridge communicates thru network)

## 7. development guide
- use google style guide to write the code
- use cppcheck to check the code
- use google test to write the test case