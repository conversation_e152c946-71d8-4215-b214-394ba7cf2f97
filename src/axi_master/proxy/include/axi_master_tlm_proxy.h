#ifndef SSLN_HYBRID_AXI_MASTER_TLM_PROXY_H_
#define SSLN_HYBRID_AXI_MASTER_TLM_PROXY_H_

#include <array>
#include <atomic>
#include <cstdint>
#include <memory>
#include <string>
#include <thread>
#include <systemc>
#include <tlm>
#include "async_event.h"
#include "tlm_payload.h"
#include "ssln_profiling.h"
#include "dispatcher/include/dispatcher.h"
#include "dispatcher/include/i_endpoint.h"
namespace ssln {
namespace hybrid {
namespace axi {

// Forward declarations
class AxiMasterTlmProxy;

/**
 *@uv_class_name AxiMasterTlmProxy
 */
class AxiMasterTlmProxy : public sc_core::sc_module,
                         public tlm::tlm_fw_transport_if<>,
                         public IEndpoint {
 public:
  tlm::tlm_target_socket<32> targ_sock;  // TLM target socket

  // Constructor
  explicit AxiMasterTlmProxy(sc_core::sc_module_name name,
                            Dispatcher* dispatcher,
                            uint32_t comp_id,
                            uint32_t dest_id);

  // Destructor
  ~AxiMasterTlmProxy() override;

  // Disabled copy/move operations
  AxiMasterTlmProxy(const AxiMasterTlmProxy&) = delete;
  AxiMasterTlmProxy& operator=(const AxiMasterTlmProxy&) = delete;
  AxiMasterTlmProxy(AxiMasterTlmProxy&&) = delete;
  AxiMasterTlmProxy& operator=(AxiMasterTlmProxy&&) = delete;

  // TLM-2.0 interface implementation
  void b_transport(tlm::tlm_generic_payload& trans,
                  sc_core::sc_time& delay) override;

  bool get_direct_mem_ptr(tlm::tlm_generic_payload& trans,
                         tlm::tlm_dmi& dmi_data) override;

  unsigned int transport_dbg(tlm::tlm_generic_payload& trans) override;

  tlm::tlm_sync_enum nb_transport_fw(tlm::tlm_generic_payload& trans,
                                    tlm::tlm_phase& phase,
                                    sc_core::sc_time& delay) override;

 // IEndpoint interface implementation
 bool HandleData(const void* data, size_t size) override;
 uint32_t GetComponentId() const override;

private:
  static constexpr size_t kMaxPendingTransactions = 8;

  // Transaction context for tracking pending transactions
  struct TransactionContext {
    tlm::tlm_generic_payload* payload{nullptr};
    bool response_received{false};
    async_event response_event;
  };


 // Helper functions
 bool SendRequest(const tlm::tlm_generic_payload& trans, uint32_t index);
 void ProcessResponse(const TlmPayload* resp_payload, uint32_t index);

 // Member variables
 std::array<TransactionContext, kMaxPendingTransactions> transactions_;
 std::atomic<uint64_t> payload_id_{0};
 Dispatcher* dispatcher_;
 uint32_t comp_id_;
 uint32_t dest_id_;
 SslnProfiling* profiling;
};

}  // namespace axi
}  // namespace hybrid
}  // namespace ssln

#endif  // SSLN_HYBRID_AXI_MASTER_TLM_PROXY_H_ 