#ifndef _UV_AXI_MASTER_PROXY_WARPPER_H_
#define _UV_AXI_MASTER_PROXY_WARPPER_H_

#include "axi_master_tlm_proxy.h"

/**
 * @brief uv_axi_master_proxy_wrapper ,
 * @uv_class_name uv_axi_master_proxy_wrapper
 * @uv_model_name uv_axi_master_proxy_wrapper
 * @uv_lib_path_third ${HYBRID_ROOT}/lib/Linux64_GCC-10.3.0_Release/,${HYBRID_ROOT}/src/thirdparty/install/lib/
 * @uv_lib_third ipc,sslogger 
 * @uv_include_path_third ${HYBRID_ROOT}/lib/axi_master/proxy/include,${HYBRID_ROOT}/lib/common/include,${HYBRID_ROOT}/lib/sslogger/include,${HYBRID_ROOT}/src/thirdparty/install/include
 */

template <const char* CHANNEL_NAME="axi_master_channel">
class uv_axi_master_proxy_wrapper: public ssln::hybrid::axi::AxiMasterTlmProxy{
public:
    uv_axi_master_proxy_wrapper(sc_core::sc_module_name name): AxiMasterTlmProxy(name, CHANNEL_NAME){

    }
    ~uv_axi_master_proxy_wrapper(){

    }
};


#endif