#include "axi_master_tlm_proxy.h"
#include "ssln/sslogger.h"
#include <cstring>
#include <thread>
#include "dispatcher/generated/payload_generated.h"

using namespace std;
namespace ssln {
namespace hybrid {
namespace axi {


AxiMasterTlmProxy::AxiMasterTlmProxy(sc_core::sc_module_name name,
                                     Dispatcher* dispatcher,
                                     uint32_t comp_id,
                                     uint32_t dest_id)
    : sc_core::sc_module(name),
      targ_sock("targ_sock"),
      dispatcher_(dispatcher),
      comp_id_(comp_id),
      dest_id_(dest_id) {
    targ_sock.bind(*this);
    profiling = new SslnProfiling(this->name());

    // Register this component with the dispatcher
    dispatcher_->register_endpoint(this);
}

AxiMasterTlmProxy::~AxiMasterTlmProxy() {
    delete profiling;
}

void AxiMasterTlmProxy::b_transport(tlm::tlm_generic_payload& trans,
                                   sc_core::sc_time& delay) {
    SSLN_LOG_INFO(file_logger, "b_transport start time");
    uint64_t current_id = payload_id_.fetch_add(1, std::memory_order_relaxed);
    uint32_t index = current_id % kMaxPendingTransactions;
    // Setup transaction context
    auto& context = transactions_[index];
    context.payload = &trans;
    context.response_received = false;

    // Send request and wait for response
    if (SendRequest(trans, index)) {
        if (!context.response_received) {
        SSLN_LOG_INFO(file_logger, "[{}], waiting response start", this->name());
        wait(context.response_event);
        SSLN_LOG_INFO(file_logger, "[{}], waiting response end", this->name());
        }
    } else {
        trans.set_response_status(tlm::TLM_GENERIC_ERROR_RESPONSE);
    }

    // Clear context
    context.payload = nullptr;
}

bool AxiMasterTlmProxy::SendRequest(const tlm::tlm_generic_payload& trans,
                                   uint32_t index) {
    profiling->add_start_time("SendRequest");

    flatbuffers::FlatBufferBuilder builder;

    // 1. Prepare variable data
    std::vector<uint8_t> variable_data;
    XuserExtension* xuser_ext = nullptr;
    trans.get_extension(xuser_ext);

    uint32_t data_len = 0;
    uint32_t be_len = 0;
    uint32_t axuser_len = 0;
    uint32_t xuser_len = 0;

    if (trans.is_write()) {
        data_len = trans.get_data_length();
        be_len = trans.get_byte_enable_length();
        variable_data.insert(variable_data.end(), trans.get_data_ptr(), trans.get_data_ptr() + data_len);
        if (trans.get_byte_enable_ptr() && be_len > 0) {
            variable_data.insert(variable_data.end(), trans.get_byte_enable_ptr(), trans.get_byte_enable_ptr() + be_len);
        }
    }

    if (xuser_ext) {
        axuser_len = xuser_ext->req_length;
        xuser_len = xuser_ext->data_length;
        variable_data.insert(variable_data.end(), xuser_ext->get_req_ptr(), xuser_ext->get_req_ptr() + axuser_len);
        if (trans.is_write()) {
            variable_data.insert(variable_data.end(), xuser_ext->get_data_ptr(), xuser_ext->get_data_ptr() + xuser_len);
        }
    }
    
    auto fb_variable_data = builder.CreateVector(variable_data);

    // 2. Build the payload
    auto payload = ssln::hybrid::dispatch::CreatePayload(
        builder,
        dest_id_,
        index, // id
        static_cast<uint8_t>(trans.get_command()),
        trans.get_address(),
        0, // response (not used in request)
        trans.get_streaming_width(),
        fb_variable_data,
        data_len,
        be_len,
        axuser_len,
        xuser_len);
        
    builder.Finish(payload);

    // 3. Send the buffer
    uint8_t* buffer = builder.GetBufferPointer();
    size_t size = builder.GetSize();

    SSLN_LOG_INFO(file_logger, "[{}], Sending request to dest_id={}, size={}", this->name(), dest_id_, size);
    bool ret = dispatcher_->send(dest_id_, buffer, size);
    profiling->add_duration_time("SendRequest");
    return ret;
}

void AxiMasterTlmProxy::ProcessResponse(const TlmPayload* resp_payload,
                                      uint32_t index) {
    auto& context = transactions_[index];
    if (!context.payload) {
        SSLN_LOG_ERROR(file_logger, "[{}], ProcessResponse can't find index={}", this->name(), index);
        return;
    }

    SSLN_LOG_INFO(file_logger, "[{}], recv TlmPayload: id={}, cmd={}, addr={:#x}", this->name(), resp_payload->id, resp_payload->command, resp_payload->address);
    SSLN_LOG_DEBUG(file_logger, "[{}], {}", this->name(), *resp_payload);

    // Update response status
    context.payload->set_response_status(
        static_cast<tlm::tlm_response_status>(resp_payload->response));

    // Copy read data if applicable
    if (context.payload->get_command() == 0) {
        std::memcpy(context.payload->get_data_ptr(),
                    resp_payload->data,
                    resp_payload->data_length);
        if (context.payload->get_byte_enable_ptr() &&
            context.payload->get_byte_enable_length() > 0) {
        std::memcpy(context.payload->get_byte_enable_ptr(),
                    resp_payload->data + resp_payload->data_length,
                    resp_payload->byte_enable_length);
        }
    }

    // user data
    // tlm::tlm_extension_base* base_ext = nullptr;
    XuserExtension* xuser_ext = nullptr;
    context.payload->get_extension(xuser_ext);
    // context.payload->get_extension(base_ext);
    // if(base_ext != nullptr){
    //   xuser_ext = dynamic_cast<XuserExtension*>(base_ext);
    // }
    if(xuser_ext != nullptr){
        if (context.payload->get_command() == 0){
        uint8_t* xuser_ptr = resp_payload->data + resp_payload->data_length;
        
        memcpy(xuser_ext->get_data_ptr(), xuser_ptr, resp_payload->xuser_length);
        }
        else{
        // uint8_t* xuser_ptr = resp_payload->data;
        // memcpy(xuser_ext->get_data_ptr(), xuser_ptr, resp_payload)
        memcpy(reinterpret_cast<uint8_t*>(&xuser_ext->response), resp_payload->data, resp_payload->xuser_length);
        }
    }

    // Notify waiting thread
    context.response_received = true;
    context.response_event.notify();
    SSLN_LOG_INFO(file_logger, "[{}], response event norify", this->name());
}

uint32_t AxiMasterTlmProxy::get_component_id() const {
    return comp_id_;
}

bool AxiMasterTlmProxy::handle_data(const void* data, size_t size) {
    profiling->add_start_time_cthread("ProcessResponse");
    auto resp_payload = flatbuffers::GetRoot<ssln::hybrid::dispatch::Payload>(data);
    uint32_t index = resp_payload->id();

    auto& context = transactions_[index];
    if (!context.payload) {
        SSLN_LOG_ERROR(file_logger, "[{}] handle_data can't find index={}", this->name(), index);
        return false;
    }

    SSLN_LOG_INFO(file_logger, "[{}] recv FlatBuffer: id={}, cmd={}, addr={:#x}", this->name(), resp_payload->id(), resp_payload->command(), resp_payload->address());

    // Update response status
    context.payload->set_response_status(static_cast<tlm::tlm_response_status>(resp_payload->response()));

    // Copy read data if applicable
    const uint8_t* variable_data_ptr = resp_payload->variable_data()->data();
    if (context.payload->is_read()) {
        uint32_t data_len = resp_payload->data_length();
        uint32_t be_len = resp_payload->byte_enable_length();

        std::memcpy(context.payload->get_data_ptr(), variable_data_ptr, data_len);
        if (context.payload->get_byte_enable_ptr() && be_len > 0) {
            std::memcpy(context.payload->get_byte_enable_ptr(), variable_data_ptr + data_len, be_len);
        }
    }

    // Handle user extensions
    XuserExtension* xuser_ext = nullptr;
    context.payload->get_extension(xuser_ext);
    if (xuser_ext) {
        uint32_t resp_xuser_len = resp_payload->xuser_length();
        if (resp_xuser_len > 0) {
            // In the new format, all variable data is concatenated.
            // For a read response, xuser data would typically follow data and byte_enable.
            const uint8_t* xuser_ptr = variable_data_ptr + resp_payload->data_length() + resp_payload->byte_enable_length();
            if (context.payload->is_read()) {
                 memcpy(xuser_ext->get_data_ptr(), xuser_ptr, resp_xuser_len);
            } else { // Write response
                 memcpy(reinterpret_cast<uint8_t*>(&xuser_ext->response), xuser_ptr, resp_xuser_len);
            }
        }
    }

    // Notify waiting thread
    context.response_received = true;
    context.response_event.notify();
    SSLN_LOG_INFO(file_logger, "[{}] response event notify for index {}", this->name(), index);
    profiling->add_duration_time_cthread("ProcessResponse");
    return true;
}

bool AxiMasterTlmProxy::get_direct_mem_ptr(tlm::tlm_generic_payload& trans,
                                          tlm::tlm_dmi& dmi_data) {
  return false;  // DMI not supported
}

unsigned int AxiMasterTlmProxy::transport_dbg(tlm::tlm_generic_payload& trans) {
  return 0;  // Debug transport not supported
}

tlm::tlm_sync_enum AxiMasterTlmProxy::nb_transport_fw(
    tlm::tlm_generic_payload& trans,
    tlm::tlm_phase& phase,
    sc_core::sc_time& delay) {
  return tlm::TLM_COMPLETED;  // Non-blocking transport not supported
}

}  // namespace axi
}  // namespace hybrid
}  // namespace ssln 