#include <gtest/gtest.h>
#include "axi_master_tlm_proxy.h"
#include <tlm_utils/simple_initiator_socket.h>
#include <systemc>
#include <tlm>
#include <thread>
using namespace std;
using namespace ssln::hybrid::axi;
using namespace ssln::hybrid;
using namespace tlm;


class ScStopModule: public sc_core::sc_module{
public:
    SC_HAS_PROCESS(ScStopModule);
    ScStopModule(sc_core::sc_module_name name): sc_core::sc_module(name){
        stop_event = new async_event("stop_event");
        SC_THREAD(StopThread);
    }

    void StopThread(){
        while(true){
            wait(*stop_event);
            sc_core::sc_stop();
        }
    }
    ~ScStopModule(){
        delete stop_event;
    }
    async_event* stop_event;
};




class FakeMaster: public sc_core::sc_module{
public:
    SC_HAS_PROCESS(FakeMaster);
    FakeMaster(sc_core::sc_module_name name):sc_module(name){
        init_socket = new tlm_utils::simple_initiator_socket<FakeMaster>("init_socket");
        send_seq = new async_event("send_seq");
        SC_THREAD(send_seq_function);
    }
    
    ~FakeMaster(){
        delete init_socket;
        delete send_seq;
    }

    void send_seq_function(){
        while(true){
            cout << "send_seq_function wait s" << endl;
            wait(*send_seq);
            cout << "send_seq_function wait e" << endl;
            for(auto tlm_gp: seq){
                sc_core::sc_time delay;
                (*init_socket)->b_transport(*tlm_gp, delay);
            }
        }
    }
    
    tlm_utils::simple_initiator_socket<FakeMaster>* init_socket;
    vector<tlm::tlm_generic_payload*> seq;
    async_event* send_seq;

};



class AxiMasterTlmProxyTest: public testing::Test{
public:
    void SetUp() override{
        string channel_name = "axi_master_proxy";
        ipc::channel::clear_storage((channel_name + "_resp").c_str());
        ipc::channel::clear_storage((channel_name + "_req").c_str());
        axi_master_tlm_proxy = new AxiMasterTlmProxy("axi_master_tlm_proxy", channel_name);
        resp_channel_ = new ipc::channel((channel_name + "_resp").c_str(), ipc::sender);
        req_channel_ = new ipc::channel((channel_name + "_req").c_str(), ipc::receiver);

        sc_stop_module = new ScStopModule("stopmodule");
        fake_master = new FakeMaster("fake_master");
        fake_master->init_socket->bind(axi_master_tlm_proxy->targ_sock);
        sim_thread = new thread(
            [](){
                sc_core::sc_start();
            }
        );
    }

    void TearDown() override{
        sc_stop_module->stop_event->notify();
        if(sim_thread->joinable()){
            sim_thread->join();
        }
        delete sim_thread;
        delete axi_master_tlm_proxy;
        delete fake_master;
        req_channel_->disconnect();
        resp_channel_->disconnect();
        delete req_channel_;
        delete resp_channel_;
        // delete sc_core::sc_curr_simcontext; // avoid
        sc_core::sc_curr_simcontext = new sc_core::sc_simcontext();
        sc_core::sc_default_global_context = nullptr;
    }

    AxiMasterTlmProxy* axi_master_tlm_proxy;
    FakeMaster* fake_master;
    ipc::channel* req_channel_;
    ipc::channel* resp_channel_;
    ScStopModule* sc_stop_module;
    thread* sim_thread;
};


TEST_F(AxiMasterTlmProxyTest, R_SINGLE_S){
    tlm_generic_payload* tlm_gp = new tlm_generic_payload();
    uint32_t data_length = 0x100;
    tlm_gp->set_address(0x0);
    tlm_gp->set_data_length(data_length);
    tlm_gp->set_read();
    tlm_gp->set_response_status(TLM_INCOMPLETE_RESPONSE);
    uint8_t* data_ptr = new uint8_t [data_length];
    tlm_gp->set_data_ptr(data_ptr);


    fake_master->seq.push_back(tlm_gp);
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    cout << "notify s" << endl;
    fake_master->send_seq->notify();
    cout << "notify e" << endl;
    auto recv_data = req_channel_->recv();
    EXPECT_EQ(recv_data.empty(), false);

    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload));
    TlmPayload* req_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    EXPECT_EQ(req_payload->data, nullptr);
    EXPECT_EQ(req_payload->command, TLM_READ_COMMAND);
    EXPECT_EQ(req_payload->data_length, 0x100);
    EXPECT_EQ(req_payload->streaming_width, 0x0);


    uint32_t resp_size = sizeof(TlmPayload) + data_length;
    uint8_t* resp_ptr = new uint8_t [resp_size];
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(resp_ptr);
    memcpy(resp_payload, req_payload, sizeof(TlmPayload));
    resp_payload->data = resp_ptr + sizeof(TlmPayload);

    for(int i = 0; i < data_length; i++){
        resp_payload->data[i] = i % 256;
    }
    resp_payload->response = TLM_OK_RESPONSE;

    resp_channel_->wait_for_recv(1);
    resp_channel_->send(resp_payload, resp_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(500));

    // check
    tlm_generic_payload* resp_tlm_gp = fake_master->seq[0];
    EXPECT_EQ(resp_tlm_gp->get_response_status(), TLM_OK_RESPONSE);
    for(int i = 0; i < data_length; i++){
        EXPECT_EQ(resp_tlm_gp->get_data_ptr()[i], i % 256);
    }

    delete [] data_ptr;
    delete tlm_gp;
    delete [] resp_ptr;

}


TEST_F(AxiMasterTlmProxyTest, R_MULTI_S){
    tlm_generic_payload* tlm_gp = new tlm_generic_payload();
    uint32_t data_length = 0x100;
    tlm_gp->set_address(0x0);
    tlm_gp->set_data_length(data_length);
    tlm_gp->set_streaming_width(0x20);
    tlm_gp->set_read();
    tlm_gp->set_response_status(TLM_INCOMPLETE_RESPONSE);
    uint8_t* data_ptr = new uint8_t [data_length];
    tlm_gp->set_data_ptr(data_ptr);


    fake_master->seq.push_back(tlm_gp);
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    cout << "notify s" << endl;
    fake_master->send_seq->notify();
    cout << "notify e" << endl;
    auto recv_data = req_channel_->recv();
    EXPECT_EQ(recv_data.empty(), false);

    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload));
    TlmPayload* req_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    EXPECT_EQ(req_payload->data, nullptr);
    EXPECT_EQ(req_payload->command, TLM_READ_COMMAND);
    EXPECT_EQ(req_payload->data_length, 0x100);
    EXPECT_EQ(req_payload->streaming_width, 0x20);


    uint32_t resp_size = sizeof(TlmPayload) + data_length;
    uint8_t* resp_ptr = new uint8_t [resp_size];
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(resp_ptr);
    memcpy(resp_payload, req_payload, sizeof(TlmPayload));
    resp_payload->data = resp_ptr + sizeof(TlmPayload);

    for(int i = 0; i < data_length; i += 0x20){
        for(int j = 0; j < 0x20; j++){
            resp_payload->data[i + j] = j % 256;
        }
    }

    resp_payload->response = TLM_OK_RESPONSE;
    resp_channel_->wait_for_recv(1);
    resp_channel_->send(resp_payload, resp_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(500));

    // check
    tlm_generic_payload* resp_tlm_gp = fake_master->seq[0];
    EXPECT_EQ(resp_tlm_gp->get_response_status(), TLM_OK_RESPONSE);
    for(int i = 0; i < data_length; i += 0x20){
        for(int j = 0; j < 0x20; j++){
            EXPECT_EQ(resp_tlm_gp->get_data_ptr()[i + j], j % 256);
        }
    }

    delete [] data_ptr;
    delete tlm_gp;
    delete [] resp_ptr;

}



TEST_F(AxiMasterTlmProxyTest, W_SINGLE_S){
    tlm_generic_payload* tlm_gp = new tlm_generic_payload();
    uint32_t data_length = 0x100;
    tlm_gp->set_address(0x0);
    tlm_gp->set_data_length(data_length);
    tlm_gp->set_write();
    tlm_gp->set_response_status(TLM_INCOMPLETE_RESPONSE);
    uint8_t* data_ptr = new uint8_t [data_length];
    for(int i = 0; i < data_length; i++){
        data_ptr[i] = i % 256;
    }
    tlm_gp->set_data_ptr(data_ptr);

    fake_master->seq.push_back(tlm_gp);
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    cout << "notify s" << endl;
    fake_master->send_seq->notify();
    cout << "notify e" << endl;
    auto recv_data = req_channel_->recv();
    EXPECT_EQ(recv_data.empty(), false);

    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload) + data_length);
    TlmPayload* req_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    req_payload->data = reinterpret_cast<uint8_t*>(req_payload) + sizeof(TlmPayload);
    EXPECT_EQ(req_payload->command, TLM_WRITE_COMMAND);
    EXPECT_EQ(req_payload->data_length, 0x100);
    EXPECT_EQ(req_payload->streaming_width, 0x0);


    uint32_t resp_size = sizeof(TlmPayload);
    uint8_t* resp_ptr = new uint8_t [resp_size];
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(resp_ptr);
    memcpy(resp_payload, req_payload, sizeof(TlmPayload));
    resp_payload->data = nullptr;

    resp_payload->response = TLM_OK_RESPONSE;
    resp_channel_->wait_for_recv(1);
    resp_channel_->send(resp_payload, resp_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(500));

    // check
    tlm_generic_payload* resp_tlm_gp = fake_master->seq[0];
    EXPECT_EQ(resp_tlm_gp->get_response_status(), TLM_OK_RESPONSE);
    for(int i = 0; i < data_length; i++){
        EXPECT_EQ(resp_tlm_gp->get_data_ptr()[i], i % 256);
    }

    delete [] data_ptr;
    delete tlm_gp;
    delete [] resp_ptr;

}



TEST_F(AxiMasterTlmProxyTest, W_MULTI_S){
    tlm_generic_payload* tlm_gp = new tlm_generic_payload();
    uint32_t data_length = 0x100;
    tlm_gp->set_address(0x0);
    tlm_gp->set_data_length(data_length);
    tlm_gp->set_streaming_width(0x20);
    tlm_gp->set_write();
    tlm_gp->set_response_status(TLM_INCOMPLETE_RESPONSE);
    uint8_t* data_ptr = new uint8_t [data_length];
    for(int i = 0; i < data_length; i += 0x20){
        for(int j = 0; j < 0x20; j++){
            data_ptr[i + j] = j % 256;
        }
    }
    
    tlm_gp->set_data_ptr(data_ptr);

    fake_master->seq.push_back(tlm_gp);
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    cout << "notify s" << endl;
    fake_master->send_seq->notify();
    cout << "notify e" << endl;
    auto recv_data = req_channel_->recv();
    EXPECT_EQ(recv_data.empty(), false);

    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload) + data_length);
    TlmPayload* req_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    req_payload->data = reinterpret_cast<uint8_t*>(req_payload) + sizeof(TlmPayload);
    EXPECT_EQ(req_payload->command, TLM_WRITE_COMMAND);
    EXPECT_EQ(req_payload->data_length, 0x100);
    EXPECT_EQ(req_payload->streaming_width, 0x20);


    uint32_t resp_size = sizeof(TlmPayload);
    uint8_t* resp_ptr = new uint8_t [resp_size];
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(resp_ptr);
    memcpy(resp_payload, req_payload, sizeof(TlmPayload));
    resp_payload->data = nullptr;

    resp_payload->response = TLM_OK_RESPONSE;
    resp_channel_->wait_for_recv(1);
    resp_channel_->send(resp_payload, resp_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(500));

    // check
    tlm_generic_payload* resp_tlm_gp = fake_master->seq[0];
    EXPECT_EQ(resp_tlm_gp->get_response_status(), TLM_OK_RESPONSE);
    for(int i = 0; i < 0x100; i += 0x20){
        for(int j = 0; j < 0x20; j++){
            EXPECT_EQ(resp_tlm_gp->get_data_ptr()[i+j], j % 256);
        }
    }
    
    delete [] data_ptr;
    delete tlm_gp;
    delete [] resp_ptr;

}


TEST_F(AxiMasterTlmProxyTest, R_ERR){
    tlm_generic_payload* tlm_gp = new tlm_generic_payload();
    uint32_t data_length = 0x100;
    tlm_gp->set_address(0x0);
    tlm_gp->set_data_length(data_length);
    tlm_gp->set_read();
    tlm_gp->set_response_status(TLM_INCOMPLETE_RESPONSE);
    uint8_t* data_ptr = new uint8_t [data_length];
    tlm_gp->set_data_ptr(data_ptr);


    fake_master->seq.push_back(tlm_gp);
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    cout << "notify s" << endl;
    fake_master->send_seq->notify();
    cout << "notify e" << endl;
    auto recv_data = req_channel_->recv();
    EXPECT_EQ(recv_data.empty(), false);

    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload));
    TlmPayload* req_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    EXPECT_EQ(req_payload->data, nullptr);
    EXPECT_EQ(req_payload->command, TLM_READ_COMMAND);
    EXPECT_EQ(req_payload->data_length, 0x100);
    EXPECT_EQ(req_payload->streaming_width, 0x0);


    uint32_t resp_size = sizeof(TlmPayload) + data_length;
    uint8_t* resp_ptr = new uint8_t [resp_size];
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(resp_ptr);
    memcpy(resp_payload, req_payload, sizeof(TlmPayload));
    resp_payload->data = resp_ptr + sizeof(TlmPayload);

    for(int i = 0; i < data_length; i++){
        resp_payload->data[i] = i % 256;
    }
    resp_payload->response = TLM_GENERIC_ERROR_RESPONSE;
    resp_channel_->wait_for_recv(1);
    resp_channel_->send(resp_payload, resp_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(500));

    // check
    tlm_generic_payload* resp_tlm_gp = fake_master->seq[0];
    EXPECT_EQ(resp_tlm_gp->get_response_status(), TLM_GENERIC_ERROR_RESPONSE);
    for(int i = 0; i < data_length; i++){
        EXPECT_EQ(resp_tlm_gp->get_data_ptr()[i], i % 256);
    }

    delete [] data_ptr;
    delete tlm_gp;
    delete [] resp_ptr;

}

TEST_F(AxiMasterTlmProxyTest, W_ERR){
    tlm_generic_payload* tlm_gp = new tlm_generic_payload();
    uint32_t data_length = 0x100;
    tlm_gp->set_address(0x0);
    tlm_gp->set_data_length(data_length);
    tlm_gp->set_write();
    tlm_gp->set_response_status(TLM_INCOMPLETE_RESPONSE);
    uint8_t* data_ptr = new uint8_t [data_length];
    for(int i = 0; i < data_length; i++){
        data_ptr[i] = i % 256;
    }
    tlm_gp->set_data_ptr(data_ptr);

    fake_master->seq.push_back(tlm_gp);
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    cout << "notify s" << endl;
    fake_master->send_seq->notify();
    cout << "notify e" << endl;
    auto recv_data = req_channel_->recv();
    EXPECT_EQ(recv_data.empty(), false);

    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload) + data_length);
    TlmPayload* req_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    req_payload->data = reinterpret_cast<uint8_t*>(req_payload) + sizeof(TlmPayload);
    EXPECT_EQ(req_payload->command, TLM_WRITE_COMMAND);
    EXPECT_EQ(req_payload->data_length, 0x100);
    EXPECT_EQ(req_payload->streaming_width, 0x0);


    uint32_t resp_size = sizeof(TlmPayload);
    uint8_t* resp_ptr = new uint8_t [resp_size];
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(resp_ptr);
    memcpy(resp_payload, req_payload, sizeof(TlmPayload));
    resp_payload->data = nullptr;

    resp_payload->response = TLM_GENERIC_ERROR_RESPONSE;
    resp_channel_->wait_for_recv(1);
    resp_channel_->send(resp_payload, resp_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(500));

    // check
    tlm_generic_payload* resp_tlm_gp = fake_master->seq[0];
    EXPECT_EQ(resp_tlm_gp->get_response_status(), TLM_GENERIC_ERROR_RESPONSE);
    for(int i = 0; i < data_length; i++){
        EXPECT_EQ(resp_tlm_gp->get_data_ptr()[i], i % 256);
    }

    delete [] data_ptr;
    delete tlm_gp;
    delete [] resp_ptr;

}
int sc_main(int argc, char* argv[]) {
    return 0;
}