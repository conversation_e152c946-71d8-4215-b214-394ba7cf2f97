
add_executable(axi_master_ST_test
    src/axi_master_ST_test.cpp
)

target_link_directories(axi_master_ST_test
    PRIVATE
        $ENV{UMICOM_HOME}/lib
        ${PROJECT_ROOT}/thirdparty/fake_install/lib
)

target_link_libraries(axi_master_ST_test
    PRIVATE
        GTest::gtest_main
        fake_xtor
        axi_master_bridge
        axi_master_tlm_proxy
        systemc
)

include(GoogleTest)
gtest_discover_tests(axi_master_ST_test)

add_dependencies(build_utests axi_master_ST_test)