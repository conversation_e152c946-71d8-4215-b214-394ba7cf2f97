#include <gtest/gtest.h>
#include "axi_master_tlm_proxy.h"
#include "axi_master_bridge.h"
#include "axi_master_proxy.h"
#include "axi_transaction_item.h"
#include "tlm_payload.h"
#include <tlm_utils/simple_initiator_socket.h>
#include <systemc>
#include <tlm>
#include <thread>

using namespace std;
using namespace umi_axi;
using namespace ssln::hybrid::axi;
using namespace ssln::hybrid;
using namespace tlm;
const int sleep_time = 200;
class ScStopModule: public sc_core::sc_module{
public:
    SC_HAS_PROCESS(ScStopModule);
    ScStopModule(sc_core::sc_module_name name): sc_core::sc_module(name){
        stop_event = new async_event("stop_event");
        SC_THREAD(StopThread);
    }

    void StopThread(){
        while(true){
            wait(*stop_event);
            sc_core::sc_stop();
        }
    }
    ~ScStopModule(){
        delete stop_event;
    }
    async_event* stop_event;
};

class FakeMaster: public sc_core::sc_module{
public:
    SC_HAS_PROCESS(FakeMaster);
    FakeMaster(sc_core::sc_module_name name):sc_module(name){
        init_socket = new tlm_utils::simple_initiator_socket<FakeMaster>("init_socket");
        send_seq = new async_event("send_seq");
        SC_THREAD(send_seq_function);
    }
    
    ~FakeMaster(){
        delete init_socket;
        delete send_seq;
    }

    void send_seq_function(){
        while(true){
            cout << "send_seq_function wait s" << endl;
            wait(*send_seq);
            cout << "send_seq_function wait e" << endl;
            for(auto tlm_gp: seq){
                sc_core::sc_time delay;
                (*init_socket)->b_transport(*tlm_gp, delay);
            }
        }
    }
    
    tlm_utils::simple_initiator_socket<FakeMaster>* init_socket;
    vector<tlm::tlm_generic_payload*> seq;
    async_event* send_seq;

};

class AxiMasterTest: public testing::Test{
public:
    void SetUp() override{
        string channel_name = "test_channel";
        axi_proxy = make_shared<axi_master_proxy>(256, spec_version_t::AMBA4);
        axi_master_bridge = new AxiMasterBridge("axi_master_bridge", channel_name, axi_proxy);
        axi_master_tlm_proxy = new AxiMasterTlmProxy("axi_master_tlm_proxy", channel_name);
        fake_master = new FakeMaster("fake_master");
        sc_stop_module = new ScStopModule("stopmodule");

        fake_master->init_socket->bind(axi_master_tlm_proxy->targ_sock);
        sim_thread = new thread(
            [](){
                sc_core::sc_start();
            }
        );
    }

    void TearDown() override{
        sc_stop_module->stop_event->notify();
        if(sim_thread->joinable()){
            sim_thread->join();
        }
        delete sim_thread;
        delete axi_master_tlm_proxy;
        delete axi_master_bridge;
        delete fake_master;
        sc_core::sc_curr_simcontext = new sc_core::sc_simcontext();
        sc_core::sc_default_global_context = nullptr;
    }

    void call_afterrecv(void * resp){
        cout << "call_afterrecv s" << endl;
        axi_master_bridge->HandleResponse(resp);
        cout << "call_afterrecv e" << endl;
    }

    thread* sim_thread;
    AxiMasterBridge* axi_master_bridge;
    AxiMasterTlmProxy* axi_master_tlm_proxy;
    shared_ptr<axi_master_proxy> axi_proxy;
    FakeMaster* fake_master;
    ScStopModule* sc_stop_module;
};


TEST_F(AxiMasterTest, R_INCR){
    uint32_t data_length = 0x100;
    tlm_generic_payload* tlm_gp = new tlm_generic_payload();
    tlm_gp->set_address(0x0);
    tlm_gp->set_data_length(data_length);
    tlm_gp->set_read();
    tlm_gp->set_response_status(TLM_INCOMPLETE_RESPONSE);
    uint8_t* data_ptr = new uint8_t [data_length];
    tlm_gp->set_data_ptr(data_ptr);

    fake_master->seq.push_back(tlm_gp);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    cout << "notify s" << endl;
    fake_master->send_seq->notify();
    cout << "notify e" << endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    EXPECT_EQ(axi_proxy->b_send_lists.size(), 1);
    axi_transaction_item& item = axi_proxy->b_send_lists[0];
    EXPECT_EQ(item.addr, 0);
    //EXPECT_EQ(item.id, 0);
    EXPECT_EQ(item.len, 7);
    EXPECT_EQ(item.size, size_type_t::BYTE_32);
    EXPECT_EQ(item.burst, burst_type_t::INCR);
    EXPECT_EQ(item.cmd, cmd_type_t::READ);

    axi_transaction_item resp = item;
    resp.data = vector<unsigned char>(0x100, 0);
    resp.data_width = bus_width_t::BIT_256;
    for(int i = 0; i < 0x100; i++){
        resp.data[i] = i % 256;
    }
    for(int i = 0; i < resp.len + 1; i ++){
        resp.resp.push_back(response_type_t::OKAY);
    }
    call_afterrecv(&resp);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    // check
    tlm_generic_payload* resp_tlm_gp = fake_master->seq[0];
    EXPECT_EQ(resp_tlm_gp->get_response_status(), TLM_OK_RESPONSE);
    for(int i = 0; i < data_length; i++){
        EXPECT_EQ(resp_tlm_gp->get_data_ptr()[i], i % 256);
    }

    delete [] data_ptr;
    delete tlm_gp;
}


TEST_F(AxiMasterTest, R_INCR_MULTI){
    uint32_t data_length = 0x3000;
    tlm_generic_payload* tlm_gp = new tlm_generic_payload();
    tlm_gp->set_address(0x0);
    tlm_gp->set_data_length(data_length);
    tlm_gp->set_read();
    tlm_gp->set_response_status(TLM_INCOMPLETE_RESPONSE);
    uint8_t* data_ptr = new uint8_t [data_length];
    tlm_gp->set_data_ptr(data_ptr);

    fake_master->seq.push_back(tlm_gp);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    cout << "notify s" << endl;
    fake_master->send_seq->notify();
    cout << "notify e" << endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    // check
    EXPECT_EQ(axi_proxy->b_send_lists.size(), 3);
    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item& item = axi_proxy->b_send_lists[i];
        EXPECT_EQ(item.addr, i * 0x1000);
        //EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, 127);
        EXPECT_EQ(item.size, size_type_t::BYTE_32);
        EXPECT_EQ(item.burst, burst_type_t::INCR);
        EXPECT_EQ(item.cmd, cmd_type_t::READ);
    }

    // response
    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item resp = axi_proxy->b_send_lists[i];
        resp.data = vector<unsigned char>(0x1000, 0);
        resp.data_width = bus_width_t::BIT_256;
        for(int j = 0; j < 0x1000; j++){
            resp.data[j] = j % 256;
        }
        for(int j = 0; j < resp.len + 1; j ++){
            resp.resp.push_back(response_type_t::OKAY);
        }
        call_afterrecv(&resp);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    }

    // check
    tlm_generic_payload* resp_tlm_gp = fake_master->seq[0];
    EXPECT_EQ(resp_tlm_gp->get_response_status(), TLM_OK_RESPONSE);
    for(int i = 0; i < data_length; i++){
        EXPECT_EQ(resp_tlm_gp->get_data_ptr()[i], i % 256);
        // std::cout << int(resp_tlm_gp->get_data_ptr()[i]) << " ";
    }

    delete [] data_ptr;
    delete tlm_gp;
}


TEST_F(AxiMasterTest, R_FIXED){
    uint32_t data_length = 0x100;
    tlm_generic_payload* tlm_gp = new tlm_generic_payload();
    tlm_gp->set_address(0x0);
    tlm_gp->set_data_length(data_length);
    tlm_gp->set_streaming_width(32);
    tlm_gp->set_read();
    tlm_gp->set_response_status(TLM_INCOMPLETE_RESPONSE);
    uint8_t* data_ptr = new uint8_t [data_length];
    tlm_gp->set_data_ptr(data_ptr);

    fake_master->seq.push_back(tlm_gp);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    cout << "notify s" << endl;
    fake_master->send_seq->notify();
    cout << "notify e" << endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    EXPECT_EQ(axi_proxy->b_send_lists.size(), 1);
    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item& item = axi_proxy->b_send_lists[i];
        EXPECT_EQ(item.addr, 0);
        //EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, 7);
        EXPECT_EQ(item.size, size_type_t::BYTE_32);
        EXPECT_EQ(item.burst, burst_type_t::FIXED);
        EXPECT_EQ(item.cmd, cmd_type_t::READ);
    }

    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item resp = axi_proxy->b_send_lists[i];
        resp.data = vector<unsigned char>(0x100, 0);
        resp.data_width = bus_width_t::BIT_256;
        for(int j = 0; j < 0x100; j++){
            resp.data[j] = j % 256;
        }
        for(int j = 0; j < resp.len + 1; j ++){
            resp.resp.push_back(response_type_t::OKAY);
        }
        call_afterrecv(&resp);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    }

    // check
    tlm_generic_payload* resp_tlm_gp = fake_master->seq[0];
    EXPECT_EQ(resp_tlm_gp->get_response_status(), TLM_OK_RESPONSE);
    for(int i = 0; i < data_length; i++){
        EXPECT_EQ(resp_tlm_gp->get_data_ptr()[i], i % 256);
    }

    delete [] data_ptr;
    delete tlm_gp;
}



TEST_F(AxiMasterTest, R_FIXED_MULTI){
    uint32_t data_length = 0x4000;
    tlm_generic_payload* tlm_gp = new tlm_generic_payload();
    tlm_gp->set_address(0x0);
    tlm_gp->set_data_length(data_length);
    tlm_gp->set_streaming_width(32);
    tlm_gp->set_read();
    tlm_gp->set_response_status(TLM_INCOMPLETE_RESPONSE);
    uint8_t* data_ptr = new uint8_t [data_length];
    tlm_gp->set_data_ptr(data_ptr);

    fake_master->seq.push_back(tlm_gp);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    cout << "notify s" << endl;
    fake_master->send_seq->notify();
    cout << "notify e" << endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    ASSERT_EQ(axi_proxy->b_send_lists.size(), 4);
    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item& item = axi_proxy->b_send_lists[i];
        EXPECT_EQ(item.addr, 0);
        //EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, 127);
        EXPECT_EQ(item.size, size_type_t::BYTE_32);
        EXPECT_EQ(item.burst, burst_type_t::FIXED);
        EXPECT_EQ(item.cmd, cmd_type_t::READ);
    }

    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item resp = axi_proxy->b_send_lists[i];
        resp.data = vector<unsigned char>(0x1000, 0);
        resp.data_width = bus_width_t::BIT_256;
        for(int j = 0; j < 0x1000; j++){
            resp.data[j] = j % 256;
        }
        for(int j = 0; j < resp.len + 1; j ++){
            resp.resp.push_back(response_type_t::OKAY);
        }
        call_afterrecv(&resp);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    }

    // check
    tlm_generic_payload* resp_tlm_gp = fake_master->seq[0];
    EXPECT_EQ(resp_tlm_gp->get_response_status(), TLM_OK_RESPONSE);
    for(int i = 0; i < data_length; i++){
        EXPECT_EQ(resp_tlm_gp->get_data_ptr()[i], i % 256);
    }

    delete [] data_ptr;
    delete tlm_gp;
}


TEST_F(AxiMasterTest, R_INCR_UNALIGN){
    uint32_t data_length = 0x2020;
    tlm_generic_payload* tlm_gp = new tlm_generic_payload();
    tlm_gp->set_address(0x10);
    tlm_gp->set_data_length(data_length);
    tlm_gp->set_read();
    tlm_gp->set_response_status(TLM_INCOMPLETE_RESPONSE);
    uint8_t* data_ptr = new uint8_t [data_length];
    tlm_gp->set_data_ptr(data_ptr);

    fake_master->seq.push_back(tlm_gp);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    cout << "notify s" << endl;
    fake_master->send_seq->notify();
    cout << "notify e" << endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    // check
    ASSERT_EQ(axi_proxy->b_send_lists.size(), 3);

    uint8_t t_len[3] = {127, 127, 1};
    uint64_t t_addr[3] = {0x10, 0x1000, 0x2000};
    uint64_t t_align_addr[3] = {0x0, 0x1000, 0x2000};
    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item& item = axi_proxy->b_send_lists[i];
        EXPECT_EQ(item.addr, t_addr[i]);
        //EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, t_len[i]);
        EXPECT_EQ(item.size, size_type_t::BYTE_32);
        EXPECT_EQ(item.burst, burst_type_t::INCR);
        EXPECT_EQ(item.cmd, cmd_type_t::READ);
    }

    // response
    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item resp = axi_proxy->b_send_lists[i];
        resp.data_width = bus_width_t::BIT_256;
        uint32_t burst_len = (t_len[i] + 1)*32;
        resp.data = vector<unsigned char>(burst_len, 0);
        for(int j = 0; j < burst_len; j++){
            resp.data[j] = (t_align_addr[i] + j) % 256;
        }
        for(int j = 0; j < resp.len + 1; j ++){
            resp.resp.push_back(response_type_t::OKAY);
        }
        call_afterrecv(&resp);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    }

    // check
    tlm_generic_payload* resp_tlm_gp = fake_master->seq[0];
    EXPECT_EQ(resp_tlm_gp->get_response_status(), TLM_OK_RESPONSE);
    for(int i = 0; i < data_length; i++){
        EXPECT_EQ(resp_tlm_gp->get_data_ptr()[i], (i + 0x10) % 256);
    }

    delete [] data_ptr;
    delete tlm_gp;
}



TEST_F(AxiMasterTest, R_FIXED_UNALIGN){
    uint32_t data_length = 0x810;
    tlm_generic_payload* tlm_gp = new tlm_generic_payload();
    tlm_gp->set_address(0x10);
    tlm_gp->set_data_length(data_length);
    tlm_gp->set_streaming_width(0x8);
    tlm_gp->set_read();
    tlm_gp->set_response_status(TLM_INCOMPLETE_RESPONSE);
    uint8_t* data_ptr = new uint8_t [data_length];
    tlm_gp->set_data_ptr(data_ptr);

    fake_master->seq.push_back(tlm_gp);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    cout << "notify s" << endl;
    fake_master->send_seq->notify();
    cout << "notify e" << endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    ASSERT_EQ(axi_proxy->b_send_lists.size(), 3);
    uint8_t t_len[3] = {127, 127, 1};
    
    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item& item = axi_proxy->b_send_lists[i];
        EXPECT_EQ(item.addr, 0x10);
        //EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, t_len[i]);
        EXPECT_EQ(item.size, size_type_t::BYTE_32);
        EXPECT_EQ(item.burst, burst_type_t::FIXED);
        EXPECT_EQ(item.cmd, cmd_type_t::READ);
    }

    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item resp = axi_proxy->b_send_lists[i];
        resp.data_width = bus_width_t::BIT_256;
        uint32_t t_burst_len = (resp.len + 1) * (1 << resp.size);
        resp.data = vector<unsigned char>(t_burst_len, 0);

        for(int j = 0; j < resp.len + 1; j++){
            for(int k = 0 ; k < (1 << resp.size); k++){
                resp.data[j*(1 << resp.size) + k] = (k % 256);
            }
        }
        for(int j = 0; j < resp.len + 1; j ++){
            resp.resp.push_back(response_type_t::OKAY);
        }
        call_afterrecv(&resp);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    }

    
    // check
    tlm_generic_payload* resp_tlm_gp = fake_master->seq[0];
    EXPECT_EQ(resp_tlm_gp->get_response_status(), TLM_OK_RESPONSE);
    for(int i = 0; i < 0x810; i+=0x8){
        for(int j = 0; j < 0x8; j++){
            EXPECT_EQ(resp_tlm_gp->get_data_ptr()[i+j], (0x10 + j)%256);
        }
    }

    delete [] data_ptr;
    delete tlm_gp;
}



TEST_F(AxiMasterTest, R_INCR_4K){
    uint32_t data_length = 0x20;
    tlm_generic_payload* tlm_gp = new tlm_generic_payload();
    tlm_gp->set_address(0xff0);
    tlm_gp->set_data_length(data_length);
    tlm_gp->set_read();
    tlm_gp->set_response_status(TLM_INCOMPLETE_RESPONSE);
    uint8_t* data_ptr = new uint8_t [data_length];
    tlm_gp->set_data_ptr(data_ptr);

    fake_master->seq.push_back(tlm_gp);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    cout << "notify s" << endl;
    fake_master->send_seq->notify();
    cout << "notify e" << endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    EXPECT_EQ(axi_proxy->b_send_lists.size(), 2);
    uint64_t t_addr[2] = {0xff0, 0x1000};
    uint64_t t_align_addr[2] = {0xfe0, 0x1000};
    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item& item = axi_proxy->b_send_lists[i];
        EXPECT_EQ(item.addr, t_addr[i]);
        //EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, 0);
        EXPECT_EQ(item.size, size_type_t::BYTE_32);
        EXPECT_EQ(item.burst, burst_type_t::INCR);
        EXPECT_EQ(item.cmd, cmd_type_t::READ);
    }

    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item resp = axi_proxy->b_send_lists[i];
        resp.data = vector<unsigned char>(0x20, 0);
        resp.data_width = bus_width_t::BIT_256;
        for(int j = 0; j < 0x20; j++){
            resp.data[j] = (j + t_align_addr[i]) % 256;
        }
        for(int j = 0; j < resp.len + 1; j ++){
            resp.resp.push_back(response_type_t::OKAY);
        }

        call_afterrecv(&resp);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    }

    // check
    tlm_generic_payload* resp_tlm_gp = fake_master->seq[0];
    EXPECT_EQ(resp_tlm_gp->get_response_status(), TLM_OK_RESPONSE);
    for(int i = 0; i < data_length; i++){
        EXPECT_EQ(resp_tlm_gp->get_data_ptr()[i], (i + 0xff0)%256);
    }

    delete [] data_ptr;
    delete tlm_gp;
}



TEST_F(AxiMasterTest, R_FIXED_4K){
    uint32_t data_length = 0x40;
    tlm_generic_payload* tlm_gp = new tlm_generic_payload();
    tlm_gp->set_address(0xff0);
    tlm_gp->set_data_length(data_length);
    tlm_gp->set_streaming_width(0x20);
    tlm_gp->set_read();
    tlm_gp->set_response_status(TLM_INCOMPLETE_RESPONSE);
    uint8_t* data_ptr = new uint8_t [data_length];
    tlm_gp->set_data_ptr(data_ptr);

    fake_master->seq.push_back(tlm_gp);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    cout << "notify s" << endl;
    fake_master->send_seq->notify();
    cout << "notify e" << endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    ASSERT_EQ(axi_proxy->b_send_lists.size(), 4);
    uint32_t t_addr[4] = {0xff0, 0x1000, 0xff0, 0x1000};
    uint32_t t_align_addr[4] = {0xfe0, 0x1000, 0xfe0, 0x1000};
    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item& item = axi_proxy->b_send_lists[i];
        EXPECT_EQ(item.addr, t_addr[i]);
        //EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, 0);
        EXPECT_EQ(item.size, size_type_t::BYTE_32);
        EXPECT_EQ(item.burst, burst_type_t::INCR);
        EXPECT_EQ(item.cmd, cmd_type_t::READ);
    }

    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item resp = axi_proxy->b_send_lists[i];
        resp.data_width = bus_width_t::BIT_256;
        uint32_t t_burst_len = (resp.len + 1) * (1 << resp.size);
        resp.data = vector<unsigned char>(t_burst_len, 0);

        for(int j = 0; j < resp.len + 1; j++){
            for(int k = 0 ; k < (1 << resp.size); k++){
                resp.data[j*(1 << resp.size) + k] = ((k + t_align_addr[i]) % 256);
            }
        }
        for(int j = 0; j < resp.len + 1; j ++){
            resp.resp.push_back(response_type_t::OKAY);
        }
        call_afterrecv(&resp);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    }

    // check
    tlm_generic_payload* resp_tlm_gp = fake_master->seq[0];
    EXPECT_EQ(resp_tlm_gp->get_response_status(), TLM_OK_RESPONSE);
    
    for(int i = 0; i < 0x40; i+=0x20){
        for(int j = 0; j < 0x20; j++){
            EXPECT_EQ(resp_tlm_gp->get_data_ptr()[i+j], (0xff0 + j)%256);
        }
    }

    delete [] data_ptr;
    delete tlm_gp;
}


TEST_F(AxiMasterTest, W_INCR){
    uint32_t data_length = 0x100;
    tlm_generic_payload* tlm_gp = new tlm_generic_payload();
    tlm_gp->set_address(0x0);
    tlm_gp->set_data_length(data_length);
    tlm_gp->set_write();
    tlm_gp->set_response_status(TLM_INCOMPLETE_RESPONSE);
    uint8_t* data_ptr = new uint8_t [data_length];
    tlm_gp->set_data_ptr(data_ptr);

    for(int i = 0; i < tlm_gp->get_data_length(); i++){
        data_ptr[i] = i % 256;
    }


    fake_master->seq.push_back(tlm_gp);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    cout << "notify s" << endl;
    fake_master->send_seq->notify();
    cout << "notify e" << endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    EXPECT_EQ(axi_proxy->b_send_lists.size(), 1);
    axi_transaction_item& item = axi_proxy->b_send_lists[0];
    EXPECT_EQ(item.addr, 0);
    //EXPECT_EQ(item.id, 0);
    EXPECT_EQ(item.len, 7);
    EXPECT_EQ(item.size, size_type_t::BYTE_32);
    EXPECT_EQ(item.burst, burst_type_t::INCR);
    EXPECT_EQ(item.data.size(), data_length);
    EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
    uint32_t burst_len = (item.len + 1) * (1 << item.size);
    for(int i = 0; i < item.data.size(); i++){
        EXPECT_EQ(item.data[i], (item.addr + i) % 256);
    }


    axi_transaction_item resp = item;
    resp.data.clear();
    for(int i = 0; i < 1; i ++){
        resp.resp.push_back(response_type_t::OKAY);
    }
    call_afterrecv(&resp);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    // check
    tlm_generic_payload* resp_tlm_gp = fake_master->seq[0];
    EXPECT_EQ(resp_tlm_gp->get_response_status(), TLM_OK_RESPONSE);
    for(int i = 0; i < data_length; i++){
        EXPECT_EQ(resp_tlm_gp->get_data_ptr()[i], i % 256);
    }

    delete [] data_ptr;
    delete tlm_gp;
}



TEST_F(AxiMasterTest, W_INCR_MULTI){
    uint32_t data_length = 0x3000;
    tlm_generic_payload* tlm_gp = new tlm_generic_payload();
    tlm_gp->set_address(0x0);
    tlm_gp->set_data_length(data_length);
    tlm_gp->set_write();
    tlm_gp->set_response_status(TLM_INCOMPLETE_RESPONSE);
    uint8_t* data_ptr = new uint8_t [data_length];
    tlm_gp->set_data_ptr(data_ptr);

    for(int i = 0; i < tlm_gp->get_data_length(); i++){
        data_ptr[i] = i % 256;
    }


    fake_master->seq.push_back(tlm_gp);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    cout << "notify s" << endl;
    fake_master->send_seq->notify();
    cout << "notify e" << endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    EXPECT_EQ(axi_proxy->b_send_lists.size(), 3);
    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item& item = axi_proxy->b_send_lists[i];
        EXPECT_EQ(item.addr, i * 0x1000);
        //EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, 127);
        EXPECT_EQ(item.size, size_type_t::BYTE_32);
        EXPECT_EQ(item.burst, burst_type_t::INCR);
        EXPECT_EQ(item.data.size(), 0x1000);
        EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
        for(int j = 0; j < 0x1000; j++){
            EXPECT_EQ(item.data[j], (item.addr + j) % 256);
        }
    }


    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item resp = axi_proxy->b_send_lists[i];
        resp.data.clear();
        resp.data_width = bus_width_t::BIT_256;
        
        for(int j = 0; j < 1; j ++){
            resp.resp.push_back(response_type_t::OKAY);
        }
        call_afterrecv(&resp);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    }

    // check
    tlm_generic_payload* resp_tlm_gp = fake_master->seq[0];
    EXPECT_EQ(resp_tlm_gp->get_response_status(), TLM_OK_RESPONSE);
    for(int i = 0; i < data_length; i++){
        EXPECT_EQ(resp_tlm_gp->get_data_ptr()[i], i % 256);
    }

    delete [] data_ptr;
    delete tlm_gp;
}



TEST_F(AxiMasterTest, W_FIXED){
    uint32_t data_length = 0x100;
    tlm_generic_payload* tlm_gp = new tlm_generic_payload();
    tlm_gp->set_address(0x0);
    tlm_gp->set_data_length(data_length);
    tlm_gp->set_streaming_width(32);
    tlm_gp->set_write();
    tlm_gp->set_response_status(TLM_INCOMPLETE_RESPONSE);
    uint8_t* data_ptr = new uint8_t [data_length];
    tlm_gp->set_data_ptr(data_ptr);
    for(int i = 0; i < tlm_gp->get_data_length(); i += tlm_gp->get_streaming_width()){
        for(int j = 0; j < tlm_gp->get_streaming_width(); j++){
            if(i + j >= data_length) break;
            tlm_gp->get_data_ptr()[i + j] = (tlm_gp->get_address() + j) % 256;
        }
    }


    fake_master->seq.push_back(tlm_gp);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    cout << "notify s" << endl;
    fake_master->send_seq->notify();
    cout << "notify e" << endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    EXPECT_EQ(axi_proxy->b_send_lists.size(), 1);
    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item& item = axi_proxy->b_send_lists[i];
        EXPECT_EQ(item.addr, 0);
        //EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, 7);
        EXPECT_EQ(item.size, size_type_t::BYTE_32);
        EXPECT_EQ(item.burst, burst_type_t::FIXED);
        EXPECT_EQ(item.data.size(), 0x100);
        EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
        for(int j = 0; j < item.len + 1; j++){
            for(int k = 0; k < (1 << item.size); k++){
                EXPECT_EQ(item.data[j * 32 + k], (item.addr + k) % 256);
            }
        }
    }

    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item resp = axi_proxy->b_send_lists[i];
        resp.data.clear();
        resp.data_width = bus_width_t::BIT_256;
        for(int j = 0; j < 1; j ++){
            resp.resp.push_back(response_type_t::OKAY);
        }
        call_afterrecv(&resp);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    }

    // check
    tlm_generic_payload* resp_tlm_gp = fake_master->seq[0];
    EXPECT_EQ(resp_tlm_gp->get_response_status(), TLM_OK_RESPONSE);
    

    delete [] data_ptr;
    delete tlm_gp;
}


TEST_F(AxiMasterTest, W_FIXED_MULTI){
    uint32_t data_length = 0x4000;
    tlm_generic_payload* tlm_gp = new tlm_generic_payload();
    tlm_gp->set_address(0x0);
    tlm_gp->set_data_length(data_length);
    tlm_gp->set_streaming_width(32);
    tlm_gp->set_write();
    tlm_gp->set_response_status(TLM_INCOMPLETE_RESPONSE);
    uint8_t* data_ptr = new uint8_t [data_length];
    tlm_gp->set_data_ptr(data_ptr);
    for(int i = 0; i < tlm_gp->get_data_length(); i += tlm_gp->get_streaming_width()){
        for(int j = 0; j < tlm_gp->get_streaming_width(); j++){
            if(i + j >= data_length) break;
            tlm_gp->get_data_ptr()[i + j] = (tlm_gp->get_address() + j) % 256;
        }
    }


    fake_master->seq.push_back(tlm_gp);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    cout << "notify s" << endl;
    fake_master->send_seq->notify();
    cout << "notify e" << endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    ASSERT_EQ(axi_proxy->b_send_lists.size(), 4);
    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item& item = axi_proxy->b_send_lists[i];
        EXPECT_EQ(item.addr, 0);
        //EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, 127);
        EXPECT_EQ(item.size, size_type_t::BYTE_32);
        EXPECT_EQ(item.burst, burst_type_t::FIXED);
        EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
        EXPECT_EQ(item.data.size(), 0x1000);
        for(int j = 0; j < item.len + 1; j++){
            for(int k = 0; k < (1 << item.size); k++){
                EXPECT_EQ(item.data[j * 32 + k], (item.addr + k) % 256);
            }
        }
    }

    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item resp = axi_proxy->b_send_lists[i];
        resp.data.clear();
        resp.data_width = bus_width_t::BIT_256;
        for(int j = 0; j < 1; j ++){
            resp.resp.push_back(response_type_t::OKAY);
        }
        call_afterrecv(&resp);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    }

    // check
    tlm_generic_payload* resp_tlm_gp = fake_master->seq[0];
    EXPECT_EQ(resp_tlm_gp->get_response_status(), TLM_OK_RESPONSE);
    

    delete [] data_ptr;
    delete tlm_gp;
}



TEST_F(AxiMasterTest, W_INCR_UNALIGN){
    uint32_t data_length = 0x2020;
    tlm_generic_payload* tlm_gp = new tlm_generic_payload();
    tlm_gp->set_address(0x10);
    tlm_gp->set_data_length(data_length);
    tlm_gp->set_write();
    tlm_gp->set_response_status(TLM_INCOMPLETE_RESPONSE);
    uint8_t* data_ptr = new uint8_t [data_length];
    tlm_gp->set_data_ptr(data_ptr);

    for(int i = 0; i < tlm_gp->get_data_length(); i++){
        data_ptr[i] = (i + tlm_gp->get_address()) % 256;
    }


    fake_master->seq.push_back(tlm_gp);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    cout << "notify s" << endl;
    fake_master->send_seq->notify();
    cout << "notify e" << endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    ASSERT_EQ(axi_proxy->b_send_lists.size(), 3);

    uint8_t t_len[3] = {127, 127, 1};
    uint64_t t_addr[3] = {0x10, 0x1000, 0x2000};
    uint64_t t_align_addr[3] = {0x0, 0x1000, 0x2000};
    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item& item = axi_proxy->b_send_lists[i];
        EXPECT_EQ(item.addr, t_addr[i]);
        //EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, t_len[i]);
        EXPECT_EQ(item.size, size_type_t::BYTE_32);
        EXPECT_EQ(item.burst, burst_type_t::INCR);
        EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
        EXPECT_EQ(item.data.size(), (t_len[i] + 1) * 32);
        EXPECT_EQ(item.be.size(), (t_len[i] + 1) * 32);
        uint32_t burst_len = (t_len[i] + 1) * 32;
        for(int j = 0; j < t_len[i] + 1; j++){
            for(int k = 0; k < (1 << item.size); k++){
                if(t_align_addr[i] + j * 32 + k < t_addr[i]) continue;
                if(t_align_addr[i] + j * 32 + k >= data_length + tlm_gp->get_address()) continue;
                EXPECT_EQ(item.data[j * 32 + k], (t_align_addr[i] + j * 32 + k) % 256);
            }
        }
        for(int j = 0; j < t_len[i] + 1; j++){
            for(int k = 0; k < (1 << item.size); k++){
                if(t_align_addr[i] + j * 32 + k < t_addr[i]){
                    EXPECT_EQ(item.be[j * 32 + k], 0x00);
                }
                else if(t_align_addr[i] + j * 32 + k >= data_length + tlm_gp->get_address()){
                    EXPECT_EQ(item.be[j * 32 + k], 0x00);
                }
                else{
                    EXPECT_EQ(item.be[j * 32 + k], 0xff);
                }
            }
        }
    }


    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item resp = axi_proxy->b_send_lists[i];
        resp.data_width = bus_width_t::BIT_256;
        resp.data.clear();
        for(int j = 0; j < 1; j ++){
            resp.resp.push_back(response_type_t::OKAY);
        }
        call_afterrecv(&resp);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    }

    // check
    tlm_generic_payload* resp_tlm_gp = fake_master->seq[0];
    EXPECT_EQ(resp_tlm_gp->get_response_status(), TLM_OK_RESPONSE);
    

    delete [] data_ptr;
    delete tlm_gp;
}



TEST_F(AxiMasterTest, W_FIXED_UNALIGN){
    uint32_t data_length = 0x810;
    tlm_generic_payload* tlm_gp = new tlm_generic_payload();
    tlm_gp->set_address(0x10);
    tlm_gp->set_data_length(data_length);
    tlm_gp->set_streaming_width(0x8);
    tlm_gp->set_write();
    tlm_gp->set_response_status(TLM_INCOMPLETE_RESPONSE);
    uint8_t* data_ptr = new uint8_t [data_length];
    tlm_gp->set_data_ptr(data_ptr);
    for(int i = 0; i < tlm_gp->get_data_length(); i += tlm_gp->get_streaming_width()){
        for(int j = 0; j < tlm_gp->get_streaming_width(); j++){
            if(i + j >= data_length) break;
            tlm_gp->get_data_ptr()[i + j] = (tlm_gp->get_address() + j) % 256;
        }
    }


    fake_master->seq.push_back(tlm_gp);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    cout << "notify s" << endl;
    fake_master->send_seq->notify();
    cout << "notify e" << endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    ASSERT_EQ(axi_proxy->b_send_lists.size(), 3);
    uint8_t t_len[3] = {127, 127, 1};
    
    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item& item = axi_proxy->b_send_lists[i];
        EXPECT_EQ(item.addr, 0x10);
        //EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, t_len[i]);
        EXPECT_EQ(item.size, size_type_t::BYTE_32);
        EXPECT_EQ(item.burst, burst_type_t::FIXED);
        EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
        EXPECT_EQ(item.data.size(), (t_len[i] + 1) * 32);
        EXPECT_EQ(item.be.size(), (t_len[i] + 1) * 32);
        for(int j = 0; j < item.len + 1; j++){
            for(int k = 0; k < (1 << item.size); k++){
                if(k < 0x10) continue;
                if(k >= 0x18) continue;
                EXPECT_EQ(item.data[j * 32 + k] , k % 256);
            }
        }
        for(int j = 0; j < item.len + 1; j++){
            for(int k = 0; k < (1 << item.size); k++){
                if(k < 0x10){
                    ASSERT_EQ(item.be[j * 32 + k] , 0x00);
                }
                else if(k >= 0x18){  
                    ASSERT_EQ(item.be[j * 32 + k] , 0x00);
                }
                else{
                    ASSERT_EQ(item.be[j * 32 + k] , 0xff);
                }
            }
        }
    }

    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item resp = axi_proxy->b_send_lists[i];
        resp.data_width = bus_width_t::BIT_256;
        resp.data.clear();

        for(int j = 0; j < 1; j ++){
            resp.resp.push_back(response_type_t::OKAY);
        }
        call_afterrecv(&resp);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    }

    // check
    tlm_generic_payload* resp_tlm_gp = fake_master->seq[0];
    EXPECT_EQ(resp_tlm_gp->get_response_status(), TLM_OK_RESPONSE);
    

    delete [] data_ptr;
    delete tlm_gp;
}



TEST_F(AxiMasterTest, W_INCR_4K){
    uint32_t data_length = 0x20;
    tlm_generic_payload* tlm_gp = new tlm_generic_payload();
    tlm_gp->set_address(0xff0);
    tlm_gp->set_data_length(data_length);
    tlm_gp->set_write();
    tlm_gp->set_response_status(TLM_INCOMPLETE_RESPONSE);
    uint8_t* data_ptr = new uint8_t [data_length];
    tlm_gp->set_data_ptr(data_ptr);

    for(int i = 0; i < tlm_gp->get_data_length(); i++){
        data_ptr[i] = (i + tlm_gp->get_address()) % 256;
    }


    fake_master->seq.push_back(tlm_gp);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    cout << "notify s" << endl;
    fake_master->send_seq->notify();
    cout << "notify e" << endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    EXPECT_EQ(axi_proxy->b_send_lists.size(), 2);
    uint64_t t_addr[2] = {0xff0, 0x1000};
    uint64_t t_align_addr[2] = {0xfe0, 0x1000};
    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item& item = axi_proxy->b_send_lists[i];
        EXPECT_EQ(item.addr, t_addr[i]);
        //EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, 0);
        EXPECT_EQ(item.size, size_type_t::BYTE_32);
        EXPECT_EQ(item.burst, burst_type_t::INCR);
        EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
        EXPECT_EQ(item.data.size(), 32);
        for(int j = 0; j < 32; j++){
            if(t_align_addr[i] + j < t_addr[i]) continue;
            if(t_align_addr[i] + j >= tlm_gp->get_address() + tlm_gp->get_data_length()) continue; 
            EXPECT_EQ(item.data[j], (t_align_addr[i] + j) % 256);
        }
        for(int j = 0; j < 32; j++){
            if(t_align_addr[i] + j < t_addr[i]){
                EXPECT_EQ(item.be[j], 0x00);
            }
            else if(t_align_addr[i] + j >= tlm_gp->get_address() + tlm_gp->get_data_length()){
                EXPECT_EQ(item.be[j], 0x00);
            }
            else{
                EXPECT_EQ(item.be[j], 0xff);
            }
        }
    }


    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item resp = axi_proxy->b_send_lists[i];
        resp.data.clear();
        resp.data_width = bus_width_t::BIT_256;
        for(int j = 0; j < 1; j ++){
            resp.resp.push_back(response_type_t::OKAY);
        }

        call_afterrecv(&resp);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    }

    // check
    tlm_generic_payload* resp_tlm_gp = fake_master->seq[0];
    EXPECT_EQ(resp_tlm_gp->get_response_status(), TLM_OK_RESPONSE);
    

    delete [] data_ptr;
    delete tlm_gp;
}



TEST_F(AxiMasterTest, W_FIXED_4K){
    uint32_t data_length = 0x40;
    tlm_generic_payload* tlm_gp = new tlm_generic_payload();
    tlm_gp->set_address(0xff0);
    tlm_gp->set_data_length(data_length);
    tlm_gp->set_streaming_width(32);
    tlm_gp->set_write();
    tlm_gp->set_response_status(TLM_INCOMPLETE_RESPONSE);
    uint8_t* data_ptr = new uint8_t [data_length];
    tlm_gp->set_data_ptr(data_ptr);
    for(int i = 0; i < tlm_gp->get_data_length(); i += tlm_gp->get_streaming_width()){
        for(int j = 0; j < tlm_gp->get_streaming_width(); j++){
            if(i + j >= data_length) break;
            tlm_gp->get_data_ptr()[i + j] = (tlm_gp->get_address() + j) % 256;
        }
    }


    fake_master->seq.push_back(tlm_gp);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    cout << "notify s" << endl;
    fake_master->send_seq->notify();
    cout << "notify e" << endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    ASSERT_EQ(axi_proxy->b_send_lists.size(), 4);
    // check burst
    uint32_t t_addr[4] = {0xff0, 0x1000, 0xff0, 0x1000};
    uint32_t t_align_addr[4] = {0xfe0, 0x1000, 0xfe0, 0x1000};
    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item& item = axi_proxy->b_send_lists[i];
        EXPECT_EQ(item.addr, t_addr[i]);
        //EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, 0);
        EXPECT_EQ(item.size, size_type_t::BYTE_32);
        EXPECT_EQ(item.burst, burst_type_t::INCR);
        EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
        EXPECT_EQ(item.data.size(), 32);
        for(int j = 0; j < 32; j++){
            if(t_align_addr[i] + j < t_addr[i]) continue;
            if(t_align_addr[i] + j >= t_addr[i] + 0x10) continue;
            EXPECT_EQ(item.data[j], (t_align_addr[i] + j) % 256);
        }
        for(int j = 0; j < 32; j++){
            if(t_align_addr[i] + j < t_addr[i]){
                EXPECT_EQ(item.be[j], 0x00);
            }
            else if(t_align_addr[i] + j >= t_addr[i] + 0x10){
                EXPECT_EQ(item.be[j], 0x00);
            }
            else{
                EXPECT_EQ(item.be[j], 0xff);
            }
        }
    }

    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item resp = axi_proxy->b_send_lists[i];
        resp.data_width = bus_width_t::BIT_256;
        uint32_t t_burst_len = (resp.len + 1) * (1 << resp.size);
        resp.data.clear();
        for(int j = 0; j < 1; j ++){
            resp.resp.push_back(response_type_t::OKAY);
        }
        call_afterrecv(&resp);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    }

    // check
    tlm_generic_payload* resp_tlm_gp = fake_master->seq[0];
    EXPECT_EQ(resp_tlm_gp->get_response_status(), TLM_OK_RESPONSE);
    

    delete [] data_ptr;
    delete tlm_gp;
}



TEST_F(AxiMasterTest, W_BE_INCR){
    uint32_t data_length = 0x100;
    uint32_t be_length = 0x100;
    tlm_generic_payload* tlm_gp = new tlm_generic_payload();
    tlm_gp->set_address(0x0);
    tlm_gp->set_data_length(data_length);
    tlm_gp->set_write();
    tlm_gp->set_response_status(TLM_INCOMPLETE_RESPONSE);
    uint8_t* data_ptr = new uint8_t [data_length];
    tlm_gp->set_data_ptr(data_ptr);
    for(int i = 0; i < tlm_gp->get_data_length(); i++){
        data_ptr[i] = i % 256;
    }

    tlm_gp->set_byte_enable_length(be_length);
    uint8_t* be_ptr = new uint8_t [be_length];
    tlm_gp->set_byte_enable_ptr(be_ptr);
    for(int i = 0; i < be_length; i++){
        be_ptr[i] = i % 2 ? 0xff : 0x0;
    }


    fake_master->seq.push_back(tlm_gp);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    cout << "notify s" << endl;
    fake_master->send_seq->notify();
    cout << "notify e" << endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    EXPECT_EQ(axi_proxy->b_send_lists.size(), 1);
    axi_transaction_item& item = axi_proxy->b_send_lists[0];
    EXPECT_EQ(item.addr, 0);
    //EXPECT_EQ(item.id, 0);
    EXPECT_EQ(item.len, 7);
    EXPECT_EQ(item.size, size_type_t::BYTE_32);
    EXPECT_EQ(item.burst, burst_type_t::INCR);
    EXPECT_EQ(item.data.size(), data_length);
    EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
    EXPECT_EQ(item.be.size(), be_length);
    uint32_t burst_len = (item.len + 1) * (1 << item.size);
    for(int i = 0; i < item.data.size(); i++){
        EXPECT_EQ(item.data[i], (item.addr + i) % 256);
    }
    for(int i = 0; i < item.be.size(); i++){
        EXPECT_EQ(item.be[i], i % 2 ? 0xff : 0x0);
    }


    axi_transaction_item resp = item;
    resp.data.clear();
    for(int i = 0; i < 1; i ++){
        resp.resp.push_back(response_type_t::OKAY);
    }
    call_afterrecv(&resp);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    // check
    tlm_generic_payload* resp_tlm_gp = fake_master->seq[0];
    EXPECT_EQ(resp_tlm_gp->get_response_status(), TLM_OK_RESPONSE);
    

    delete [] data_ptr;
    delete tlm_gp;
}


TEST_F(AxiMasterTest, W_BE_FIXED){
    uint32_t data_length = 0x100;
    uint32_t be_length = 32;
    tlm_generic_payload* tlm_gp = new tlm_generic_payload();
    tlm_gp->set_address(0x0);
    tlm_gp->set_data_length(data_length);
    tlm_gp->set_streaming_width(32);
    tlm_gp->set_write();
    tlm_gp->set_response_status(TLM_INCOMPLETE_RESPONSE);
    uint8_t* data_ptr = new uint8_t [data_length];
    tlm_gp->set_data_ptr(data_ptr);
    for(int i = 0; i < tlm_gp->get_data_length(); i += tlm_gp->get_streaming_width()){
        for(int j = 0; j < tlm_gp->get_streaming_width(); j++){
            if(i + j >= data_length) break;
            data_ptr[i + j] = j % 256;
        }
    }

    tlm_gp->set_byte_enable_length(be_length);
    uint8_t* be_ptr = new uint8_t [be_length];
    tlm_gp->set_byte_enable_ptr(be_ptr);
    for(int i = 0; i < be_length; i++){
        be_ptr[i] = i % 2 ? 0xff : 0x0;
    }


    fake_master->seq.push_back(tlm_gp);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    cout << "notify s" << endl;
    fake_master->send_seq->notify();
    cout << "notify e" << endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    EXPECT_EQ(axi_proxy->b_send_lists.size(), 1);
    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item& item = axi_proxy->b_send_lists[i];
        EXPECT_EQ(item.addr, 0);
        //EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, 7);
        EXPECT_EQ(item.size, size_type_t::BYTE_32);
        EXPECT_EQ(item.burst, burst_type_t::FIXED);
        EXPECT_EQ(item.data.size(), 0x100);
        EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
        EXPECT_EQ(item.be.size(), 0x100);
        // cout << "!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!" << endl;
        for(int j = 0; j < item.len + 1; j++){
            for(int k = 0; k < (1 << item.size); k++){
                EXPECT_EQ(item.data[j * 32 + k], (item.addr + k) % 256);
                // cout << int(item.data[j * 32 + k]) << " ";
            }
            // cout << endl;
        }
        // cout << "be size: " << item.be.size() << endl;
        // cout << "+++++++++++++++++++++++++++++++++++" << endl;
        for(int j = 0; j < item.len + 1; j++){
            for(int k = 0; k < (1 << item.size); k++){
                EXPECT_EQ(item.be[j * 32 + k], (item.addr + k) % 2 ? 0xff : 0x0);
                // cout << int(item.be[j * 32 + k]) << " ";
            }
            // cout << endl;
        }
        // cout << "------------------------------------" << endl;

    }


    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item resp = axi_proxy->b_send_lists[i];
        resp.data.clear();
        resp.data_width = bus_width_t::BIT_256;
        for(int j = 0; j < 1; j ++){
            resp.resp.push_back(response_type_t::OKAY);
        }
        call_afterrecv(&resp);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    }

    // check
    tlm_generic_payload* resp_tlm_gp = fake_master->seq[0];
    EXPECT_EQ(resp_tlm_gp->get_response_status(), TLM_OK_RESPONSE);
    

    delete [] data_ptr;
    delete tlm_gp;
}



TEST_F(AxiMasterTest, R_USER){
    uint32_t data_length = 0x100;
    tlm_generic_payload* tlm_gp = new tlm_generic_payload();
    tlm_gp->set_address(0x0);
    tlm_gp->set_data_length(data_length);
    tlm_gp->set_read();
    tlm_gp->set_response_status(TLM_INCOMPLETE_RESPONSE);
    uint8_t* data_ptr = new uint8_t [data_length];
    tlm_gp->set_data_ptr(data_ptr);


    // user extension
    axi_proxy->req_user_width = 0x80;
    axi_proxy->data_r_user_width = 0x80;
    axi_proxy->data_w_user_width = 0x80;
    axi_proxy->resp_w_user_width = 0x10;
    uint32_t user_data_length = 0x100;
    uint32_t user_req_length = 0x10;
    XuserExtension* ext = new XuserExtension(user_data_length, user_req_length);
    for(int i = 0; i < ext->req_length; i++){
        ext->get_req_ptr()[i] = i % 256;
    }

    tlm_gp->set_extension(ext);


    fake_master->seq.push_back(tlm_gp);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    cout << "notify s" << endl;
    fake_master->send_seq->notify();
    cout << "notify e" << endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    // check
    EXPECT_EQ(axi_proxy->b_send_lists.size(), 1);
    uint32_t req_user_size[3] = {0x10, 0x0, 0x0};
    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item& item = axi_proxy->b_send_lists[i];
        EXPECT_EQ(item.addr, i * 0x1000);
        //EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, 7);
        EXPECT_EQ(item.size, size_type_t::BYTE_32);
        EXPECT_EQ(item.burst, burst_type_t::INCR);
        EXPECT_EQ(item.cmd, cmd_type_t::READ);

        EXPECT_EQ(item.req_user.size(), req_user_size[i]);
        for(int j = 0; j < item.req_user.size(); j++){
            EXPECT_EQ(item.req_user[j], j % 256);
        }
    }

    // response
    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item resp = axi_proxy->b_send_lists[i];
        resp.data = vector<unsigned char>(0x1000, 0);
        resp.data_width = bus_width_t::BIT_256;
        for(int j = 0; j < 0x1000; j++){
            resp.data[j] = j % 256;
        }
        for(int j = 0; j < resp.len + 1; j ++){
            resp.resp.push_back(response_type_t::OKAY);
        }

        for(int j = 0; j < 0x100; j++){
            resp.data_user.push_back(j % 256);
        }

        call_afterrecv(&resp);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    }

    // check
    tlm_generic_payload* resp_tlm_gp = fake_master->seq[0];
    EXPECT_EQ(resp_tlm_gp->get_response_status(), TLM_OK_RESPONSE);
    for(int i = 0; i < data_length; i++){
        EXPECT_EQ(resp_tlm_gp->get_data_ptr()[i], i % 256);
    }

    XuserExtension* resp_ext = nullptr;
    resp_tlm_gp->get_extension(resp_ext);
    EXPECT_NE(resp_ext, nullptr);
    
    EXPECT_EQ(resp_ext->data_length, user_data_length);
    EXPECT_EQ(resp_ext->req_length, user_req_length);
    for(int i = 0; i < user_data_length; i++){
        EXPECT_EQ(resp_ext->get_data_ptr()[i], i % 256);
        // cout << int(resp_ext->get_data_ptr()[i]) << " ";
    }
    // cout << endl;

    delete [] data_ptr;
    delete tlm_gp;
}


TEST_F(AxiMasterTest, W_USER){
    uint32_t data_length = 0x100;
    tlm_generic_payload* tlm_gp = new tlm_generic_payload();
    tlm_gp->set_address(0x0);
    tlm_gp->set_data_length(data_length);
    tlm_gp->set_write();
    tlm_gp->set_response_status(TLM_INCOMPLETE_RESPONSE);
    uint8_t* data_ptr = new uint8_t [data_length];
    tlm_gp->set_data_ptr(data_ptr);

    for(int i = 0; i < tlm_gp->get_data_length(); i++){
        data_ptr[i] = i % 256;
    }

    axi_proxy->req_user_width = 0x80;
    axi_proxy->data_r_user_width = 0x80;
    axi_proxy->data_w_user_width = 0x100;
    axi_proxy->resp_w_user_width = 0x10;
    uint32_t user_data_length = 0x100;
    uint32_t user_req_length = 0x10;
    XuserExtension* ext = new XuserExtension(user_data_length, user_req_length);
    for(int i = 0; i < ext->req_length; i++){
        ext->get_req_ptr()[i] = i % 256;
    }
    for(int i = 0; i < ext->data_length; i++){
        ext->get_data_ptr()[i] = i % 256;
    }

    tlm_gp->set_extension(ext);


    fake_master->seq.push_back(tlm_gp);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    cout << "notify s" << endl;
    fake_master->send_seq->notify();
    cout << "notify e" << endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    EXPECT_EQ(axi_proxy->b_send_lists.size(), 1);
    uint32_t req_user_size[3] = {0x10, 0x0, 0x0};
    uint32_t data_user_size[3] = {0x100, 0x0, 0x0};
    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item& item = axi_proxy->b_send_lists[i];
        EXPECT_EQ(item.addr, i * 0x1000);
        //EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, 7);
        EXPECT_EQ(item.size, size_type_t::BYTE_32);
        EXPECT_EQ(item.burst, burst_type_t::INCR);
        EXPECT_EQ(item.data.size(), 0x100);
        EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
        for(int j = 0; j < 0x100; j++){
            EXPECT_EQ(item.data[j], (item.addr + j) % 256);
        }
        EXPECT_EQ(item.req_user.size(), req_user_size[i]);
        for(int j = 0; j < item.req_user.size(); j++){
            EXPECT_EQ(item.req_user[j], j % 256);
        }
        EXPECT_EQ(item.data_user.size(), data_user_size[i]);
    }


    for(int i = 0; i < axi_proxy->b_send_lists.size(); i++){
        axi_transaction_item resp = axi_proxy->b_send_lists[i];
        resp.data.clear();
        resp.data_width = bus_width_t::BIT_256;
        
        for(int j = 0; j < 1; j ++){
            resp.resp.push_back(response_type_t::OKAY);
        }
        if(req_user_size[i]){
            resp.resp_user.push_back(0x1);
            resp.resp_user.push_back(0x1);
        }

        call_afterrecv(&resp);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    }

    // check
    tlm_generic_payload* resp_tlm_gp = fake_master->seq[0];
    EXPECT_EQ(resp_tlm_gp->get_response_status(), TLM_OK_RESPONSE);
    for(int i = 0; i < data_length; i++){
        EXPECT_EQ(resp_tlm_gp->get_data_ptr()[i], i % 256);
    }
    XuserExtension* resp_ext = nullptr;
    resp_tlm_gp->get_extension(resp_ext);
    // cout << resp_ext->response << endl;
    EXPECT_EQ(resp_ext->response, 0x101);

    delete [] data_ptr;
    delete tlm_gp;
}

int sc_main(int argc, char* argv[]) {
    return 0;
}