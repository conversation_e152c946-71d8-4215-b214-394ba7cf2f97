add_library(axi_slave_bridge
    src/axi_burst_resolver.cc
    src/axi_slave_bridge.cc
    src/data_builder.cc
    src/data_packer.cc
    src/transaction_manager.cc
)


target_include_directories(axi_slave_bridge
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}/include
        $ENV{UMICOM_HOME}/include
        $ENV{UMICOM_HOME}/include/systemc
)

#target_link_directories(axi_slave_bridge
#    PUBLIC
#        $ENV{UMICOM_HOME}/lib
#        $ENV{XTOR_ROOT}/lib/Linux64_GCC-10.3
#)

target_link_libraries(axi_slave_bridge
    PUBLIC
        common
        dispatcher
        axi_common
        sslogger
)

target_compile_definitions(axi_slave_bridge
    PUBLIC
        #SC_CPLUSPLUS=201402L
)

target_compile_features(axi_slave_bridge
    PUBLIC
        cxx_std_17
)

install(TARGETS axi_slave_bridge
    LIBRARY DESTINATION ${CMAKE_INSTALL_PREFIX}/${LIB_VERSION}
    ARCHIVE DESTINATION ${CMAKE_INSTALL_PREFIX}/${LIB_VERSION}
)


add_subdirectory(tests EXCLUDE_FROM_ALL)
