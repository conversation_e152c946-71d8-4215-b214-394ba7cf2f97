#ifndef SSLN_HYBRID_AXI_BURST_RESOLVER_H_
#define SSLN_HYBRID_AXI_BURST_RESOLVER_H_

#include <cstdint>
#include <string>
#include "axi_transaction_item.h"

namespace ssln::hybrid::axi {

/**
 * @brief Struct containing transfer information.
 */
struct TransferInfo {
  uint64_t address;         ///< The address of the transfer.
  uint32_t lower_byte_lane; ///< The lower byte lane of the transfer.
  uint32_t upper_byte_lane; ///< The upper byte lane of the transfer.
  uint32_t transfer_size;   ///< The size of the transfer.
};

/**
 * @brief Prints transfer information.
 * 
 * @param mode The transfer mode.
 * @param start_address The starting address of the transfer.
 * @param number_bytes The number of bytes to transfer.
 * @param burst_length The burst length of the transfer.
 * @param data_bus_bytes The number of bytes in the data bus.
 */
void PrintTransferInfo(umi_axi::burst_type_t mode, uint64_t start_address, uint32_t number_bytes,
                       uint32_t burst_length, uint32_t data_bus_bytes);

/**
 * @brief Prints detailed information about a transfer.
 * 
 * @param info The transfer information.
 * @param is_write Whether the transfer is a write operation.
 */
void PrintTransfer(const TransferInfo& info, bool is_write);

/**
 * @brief Aligns the address to the nearest multiple of number_bytes.
 * 
 * @param addr The address to align.
 * @param number_bytes The number of bytes to align to.
 * @return The aligned address.
 */
uint64_t AlignAddress(uint64_t addr, uint32_t number_bytes);

/**
 * @brief Calculates the boundary for wrap mode.
 * 
 * @param addr The address to calculate the boundary for.
 * @param number_bytes The number of bytes to transfer.
 * @param burst_length The burst length of the transfer.
 * @return The calculated wrap boundary.
 */
uint64_t CalculateWrapBoundary(uint64_t addr, uint32_t number_bytes,
                               uint32_t burst_length);

/**
 * @brief Class for calculating transfer information.
 */
class AxiBurstResolver {
 public:
  /**
   * @brief Constructs a TransferCalculator with given parameters.
   * 
   * @param start_address The starting address of the transfer.
   * @param number_bytes The number of bytes to transfer.
   * @param burst_length The burst length of the transfer.
   * @param data_bus_bytes The number of bytes in the data bus.
   * @param mode The transfer mode.
   */
  AxiBurstResolver(uint64_t start_address, uint32_t number_bytes,
                     uint32_t burst_length, uint32_t data_bus_bytes, umi_axi::burst_type_t burst);

  /**
   * @brief Generates the next transfer info.
   * 
   * @return The next transfer information.
   */
  TransferInfo Next();

  /**
   * @brief Checks if more transfers are available.
   * 
   * @return True if more transfers are available, false otherwise.
   */
  bool HasNext() const;

 private:
  uint64_t addr_;                 ///< The current address.
  uint64_t aligned_address_;      ///< The aligned address.
  bool aligned_;                  ///< Whether the address is aligned.
  uint64_t lower_wrap_boundary_;  ///< The lower wrap boundary.
  uint64_t upper_wrap_boundary_;  ///< The upper wrap boundary.
  uint32_t number_bytes_;         ///< The number of bytes to transfer.
  uint32_t burst_length_;         ///< The burst length of the transfer.
  uint32_t data_bus_bytes_;       ///< The number of bytes in the data bus.
  umi_axi::burst_type_t burst_;                     ///< The transfer mode.
  uint32_t current_burst_;        ///< The current burst count.
};

} /* ssln::hybrid::axi */

#endif  // SSLN_HYBRID_AXI_BURST_RESOLVER_H_

