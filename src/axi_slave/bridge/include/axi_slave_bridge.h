#ifndef SSLN_HYBRID_AXI_SLAVE_BRIDGE_H_
#define SSLN_HYBRID_AXI_SLAVE_BRIDGE_H_

#include <thread>
#include <atomic>
#include <tlm>
#include <readerwriterqueue.h>
#include "data_packer.h"
#include "data_builder.h"
#include "transaction_manager.h"
#include "axi_slave_proxy.h"
#include "tlm_payload.h"
#include "axi_transaction_item.h"
#include "async_event.h"
#include "ssln/sslogger.h"
#include "ssln_profiling.h"
#include "axi_format.h"
#include "quill/std/Vector.h"
#include "dispatcher/include/dispatcher.h"
#include "dispatcher/include/i_endpoint.h"

namespace ssln {
namespace hybrid {
namespace axi {

/**
 * @brief Bridge between AXI slave and TLM proxy
 * 
 * This class orchestrates the conversion between AXI transactions and TLM payloads,
 * managing the complete lifecycle of transactions from request to response.
 */
class AxiSlaveBridge : public sc_core::sc_module, public IEndpoint {
public:
    SC_HAS_PROCESS(AxiSlaveBridge);
    /**
     * @brief Construct a new AxiSlaveBridge
     * 
     * @param proxy Pointer to the AXI slave proxy
     * @param channel_name Base name for IPC channels. "_req" and "_resp" will be appended
     * @throw std::invalid_argument if proxy is null
     */
    explicit AxiSlaveBridge(
                    sc_core::sc_module_name name,
                    Dispatcher* dispatcher,
                    uint32_t comp_id,
                    uint32_t dest_id,
                    const std::string& bfm_hdl_path);



    /**
     * @brief Destructor
     * 
     * Stops response receiver thread and cleans up IPC channels
     */
    ~AxiSlaveBridge();

    /**
     * @brief Initialize bridge and start IPC communication
     * 
     * Registers callback with proxy and initializes IPC channels
     * @throw std::runtime_error if initialization fails
     */
    void Initialize();

    /**
     * @brief Callback function for handling AXI transactions from proxy
     * 
     * This function is registered with the proxy and called when new
     * AXI transactions arrive from the master.
     * 
     * @param trans Pointer to AXI transaction from master
     */
    void HandleAxiTransaction(void* trans);

    /**
     * @brief Response receiver thread function
     * 
     * Continuously receives responses from proxy through IPC
     */
    // IEndpoint interface implementation
    bool handle_data(const void* data, size_t size) override;
    uint32_t get_component_id() const override;

private:
    /**
     * @brief Process write request from AXI master
     * 
     * @param axi_trans AXI write transaction from master
     * @return Response transaction ID for later completion
     * @throw std::runtime_error if request processing fails
     */
    uint64_t ProcessWriteRequest(const umi_axi::axi_transaction_item& axi_trans);

    /**
     * @brief Process read request from AXI master
     * 
     * @param axi_trans AXI read transaction from master
     * @return Response transaction ID for later completion
     * @throw std::runtime_error if request processing fails
     */
    uint64_t ProcessReadRequest(const umi_axi::axi_transaction_item& axi_trans);

    /**
     * @brief Validate transaction parameters
     * 
     * @param axi_trans Transaction to validate
     * @throw std::invalid_argument if validation fails
     */
    void ValidateTransaction(const umi_axi::axi_transaction_item& axi_trans);
    
    bool Check4KBoundary (const umi_axi::axi_transaction_item& axi_trans);

    /**
     * @brief Send TLM payload to proxy through IPC
     * 
     * @param tlm_payload Payload to send
     * @throw std::runtime_error if send fails
     */
    bool SendToTlmProxy(const TlmPayload& tlm_payload);

    void SendRequest(const TlmPayload& tlm_payload);

    void HandleResponseSc();

    DataPacker data_packer_;
    DataBuilder data_builder_;
    TransactionManager trans_manager_;
    std::shared_ptr<umi_axi::axi_slave_proxy> proxy_;
    Dispatcher* dispatcher_;
    uint32_t comp_id_;
    uint32_t dest_id_;

    moodycamel::ReaderWriterQueue<std::vector<char>>* recv_responses_;
    async_event recv_event_;
    SslnProfiling* profiling;
};

}  // namespace axi
}  // namespace hybrid
}  // namespace ssln

#endif  // SSLN_HYBRID_AXI_SLAVE_BRIDGE_H_ 
