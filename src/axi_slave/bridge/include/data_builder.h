#ifndef SSLN_HYBRID_AXI_DATA_BUILDER_H_
#define SSLN_HYBRID_AXI_DATA_BUILDER_H_

#include <cstdint>
#include <memory>
#include <vector>

#include "tlm_payload.h"
#include "axi_transaction_item.h"
#include "ssln/sslogger.h"
#include "ssln_profiling.h"
#include "axi_format.h"
#include "quill/std/Vector.h"

namespace ssln {
namespace hybrid {
namespace axi {

class DataBuilder {
public:
    DataBuilder() = default;
    ~DataBuilder() = default;

    std::string name() const { return "DataBuilder"; }

    // Build AXI write response from TlmPayload
    void BuildWriteResponse(const TlmPayload& tlm_payload,
                          umi_axi::axi_transaction_item& axi_trans);
    
    // Build AXI read data response from TlmPayload
    void BuildReadResponse(const TlmPayload& tlm_payload,
                          umi_axi::axi_transaction_item& axi_trans);

private:
    // Helper methods for data alignment and response generation
    void HandleNarrowResponse(const TlmPayload& tlm_payload,
                            umi_axi::axi_transaction_item& axi_trans);

    void GenerateAxiResponse(const TlmPayload& tlm_payload,
                           umi_axi::axi_transaction_item& axi_trans);

    // Helper methods for data manipulation
    void ExtractReadData(const TlmPayload& tlm_payload,
                        umi_axi::axi_transaction_item& axi_trans);

    void AlignReadData(const std::vector<uint8_t>& raw_data,
                      umi_axi::axi_transaction_item& axi_trans);

    // Response status conversion
    umi_axi::response_type_t ConvertTlmResponseToAxi(uint8_t tlm_response);
};

}  // namespace axi
}  // namespace hybrid
}  // namespace ssln

#endif  // SSLN_HYBRID_AXI_DATA_BUILDER_H_ 