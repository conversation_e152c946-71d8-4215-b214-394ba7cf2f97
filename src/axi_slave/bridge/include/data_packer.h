#ifndef SSLN_HYBRID_AXI_DATA_PACKER_H_
#define SSLN_HYBRID_AXI_DATA_PACKER_H_

#include "tlm_payload.h"
#include "axi_transaction_item.h"
#include "ssln/sslogger.h"
#include "ssln_profiling.h"
#include "axi_format.h"
#include "quill/std/Vector.h"

namespace ssln {
namespace hybrid {
namespace axi {

/**
 * @brief Class responsible for packing AXI transaction data into TlmPayload format
 * 
 * This class handles the conversion of data from AXI transaction format to TLM payload format,
 * taking care of proper data alignment, byte enables, and burst handling.
 */
class DataPacker {
public:
    /**
     * @brief Pack AXI write data into TlmPayload
     * 
     * Extracts valid data from multi-beat AXI write transaction and packs it into
     * a continuous TLM payload buffer with appropriate byte enables.
     * 
     * @param axi_trans Source AXI transaction containing write data
     * @param tlm_payload Target TLM payload to pack data into
     */
    void PackWriteData(const umi_axi::axi_transaction_item& axi_trans, TlmPayload& tlm_payload);
    
    /**
     * @brief Pack AXI read request into TlmPayload
     * 
     * Sets up TLM payload for read request by configuring the data length
     * based on AXI transaction parameters.
     * 
     * @param axi_trans Source AXI transaction containing read request parameters
     * @param tlm_payload Target TLM payload to configure
     */
    void PackReadRequest(const umi_axi::axi_transaction_item& axi_trans, TlmPayload& tlm_payload);

    std::string name() const { return "DataPacker"; }
};

}  // namespace axi
}  // namespace hybrid
}  // namespace ssln

#endif  // SSLN_HYBRID_AXI_DATA_PACKER_H_ 