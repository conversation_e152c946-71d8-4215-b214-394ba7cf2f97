#ifndef SSLN_HYBRID_TRANSACTION_MANAGER_H_
#define SSLN_HYBRID_TRANSACTION_MANAGER_H_

#include "axi_transaction_item.h"
#include <memory>
#include <vector>
#include <mutex>

namespace ssln {
namespace hybrid {
namespace axi {

using umi_axi::axi_transaction_item;

class TransactionManager {
public:
    /**
     * @brief Construct a new TransactionManager
     * 
     * @param max_outstanding Maximum number of outstanding transactions (default 1024)
     */
    explicit TransactionManager(size_t max_outstanding = 1024);
    ~TransactionManager();

    /**
     * @brief Register a new transaction
     * 
     * @param axi_trans Original transaction to register
     * @return Index in the ring buffer where transaction is stored
     * @throw std::runtime_error if ring buffer is full
     */
    size_t RegisterTransaction(const axi_transaction_item& axi_trans);
    
    /**
     * @brief Get next transaction to be completed
     * 
     * @return Pointer to the transaction at head of queue, or nullptr if empty
     */
    axi_transaction_item* GetNextTransaction();
    
    /**
     * @brief Complete current transaction and advance ring buffer
     */
    void CompleteTransaction();

    /**
     * @brief Get number of outstanding transactions
     */
    size_t GetOutstandingCount() const;

private:
    /**
     * @brief Copy necessary fields from source transaction to destination
     * 
     * For write transactions, only copies control information.
     * For read transactions, copies control information.
     * Data buffers are managed by DataBuilder.
     */
    void CopyTransactionFields(const axi_transaction_item& src, axi_transaction_item& dst);

    std::vector<axi_transaction_item> trans_pool_;     ///< Pool of pre-allocated transactions
    std::vector<axi_transaction_item*> transactions_;  ///< Ring buffer of pointers to active transactions
    size_t head_{0};  ///< Index of oldest outstanding transaction
    size_t tail_{0};  ///< Index where next transaction will be inserted
    size_t count_{0}; ///< Number of outstanding transactions
    const size_t capacity_;  ///< Maximum number of outstanding transactions
    mutable std::mutex mutex_;  ///< Mutex for thread safety
};

}  // namespace axi
}  // namespace hybrid
}  // namespace ssln

#endif  // SSLN_HYBRID_TRANSACTION_MANAGER_H_ 