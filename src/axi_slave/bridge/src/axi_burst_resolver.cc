#include "axi_burst_resolver.h"

#include <iomanip>
#include <iostream>

namespace ssln::hybrid::axi {

// Prints transfer information.
void PrintTransferInfo(umi_axi::burst_type_t burst, uint64_t start_address, uint32_t number_bytes,
                       uint32_t burst_length, uint32_t data_bus_bytes) {
  std::cout << "Mode = " << umi_axi::enumToStr(burst) << std::endl;
  std::cout << "Start_Address = 0x" << std::hex << std::setw(16)
            << std::setfill('0') << start_address << std::dec << std::endl;
  std::cout << "Number_Bytes = " << number_bytes << std::endl;
  std::cout << "Burst_Length = " << burst_length << std::endl;
  std::cout << "Data Width = " << data_bus_bytes << std::endl;
  std::cout << "----------------------------------------" << std::endl;
}

// Prints detailed information about a transfer.
void PrintTransfer(const TransferInfo& info, bool is_write) {
  std::cout << (is_write ? "Write" : "Read") << " at address 0x" 
            << std::hex << std::setw(16) << std::setfill('0') << info.address 
            << ", bytes " << std::dec << info.lower_byte_lane << " to "
            << info.upper_byte_lane << std::endl;
}

// Aligns the address to the nearest multiple of number_bytes.
uint64_t AlignAddress(uint64_t addr, uint32_t number_bytes) {
  return (addr / number_bytes) * number_bytes;
}

// Calculates the boundary for wrap mode.
uint64_t CalculateWrapBoundary(uint64_t addr, uint32_t number_bytes,
                               uint32_t burst_length) {
  uint64_t dtsize = static_cast<uint64_t>(number_bytes) * burst_length;
  return (addr / dtsize) * dtsize;
}

// AxiBurstResolver constructor.
AxiBurstResolver::AxiBurstResolver(uint64_t start_address,
                                       uint32_t number_bytes,
                                       uint32_t burst_length,
                                       uint32_t data_bus_bytes, umi_axi::burst_type_t burst)
    : addr_(start_address),
      number_bytes_(number_bytes),
      burst_length_(burst_length),
      data_bus_bytes_(data_bus_bytes),
      burst_(burst),
      current_burst_(0) {
  aligned_address_ = AlignAddress(addr_, number_bytes_);
  aligned_ = (aligned_address_ == addr_);
  
  if (burst_ == umi_axi::burst_type_t::WRAP) {
    lower_wrap_boundary_ = CalculateWrapBoundary(addr_, number_bytes_,
                                                 burst_length_);
    upper_wrap_boundary_ = lower_wrap_boundary_ +
                           static_cast<uint64_t>(number_bytes_) * burst_length_;
  }
}

// Generates the next transfer info.
TransferInfo AxiBurstResolver::Next() {
  uint32_t lower_byte_lane = addr_ % data_bus_bytes_;
  uint32_t upper_byte_lane = aligned_ ? 
      lower_byte_lane + number_bytes_ - 1 : 
      (aligned_address_ + number_bytes_ - 1) % data_bus_bytes_;
  
  TransferInfo info = {addr_, lower_byte_lane, upper_byte_lane, number_bytes_};
  
  if (burst_ != umi_axi::burst_type_t::FIXED) {
    if (aligned_) {
      addr_ += number_bytes_;
      if (burst_ == umi_axi::burst_type_t::WRAP && addr_ >= upper_wrap_boundary_) {
        addr_ = lower_wrap_boundary_;
      }
    } else {
      if (current_burst_ == 0) {
        addr_ = aligned_address_;
      }
      addr_ += number_bytes_;
      aligned_ = true;
    }
  }

  current_burst_++;
  return info;
}

// Checks if more transfers are available.
bool AxiBurstResolver::HasNext() const {
  return current_burst_ < burst_length_;
}

} /* namespace ssln::hybrid::axi */
