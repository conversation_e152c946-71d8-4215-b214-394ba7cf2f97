#include "axi_slave_bridge.h"
#include <stdexcept>
#include "axi_utils.h"
#include "dispatcher/generated/payload_generated.h"
namespace ssln {
namespace hybrid {
namespace axi {

AxiSlaveBridge::AxiSlaveBridge(sc_core::sc_module_name name,
                               Dispatcher* dispatcher,
                               uint32_t comp_id,
                               uint32_t dest_id,
                               const std::string& bfm_hdl_path)
    : sc_core::sc_module(name),
      dispatcher_(dispatcher),
      comp_id_(comp_id),
      dest_id_(dest_id)
    {
    
    proxy_ = std::make_shared<umi_axi::axi_slave_proxy>("axi_slave_xtor_proxy", bfm_hdl_path, false, 0x20000000, 22);
    recv_responses_ = new moodycamel::ReaderWriterQueue<std::vector<char>>(15);
    profiling = new SslnProfiling(this->name());

    SC_THREAD(HandleResponseSc);
    Initialize();
    
    dispatcher_->register_endpoint(this);
}

AxiSlaveBridge::AxiSlaveBridge(sc_core::sc_module_name name, const std::string& channel_name, const std::string& bfm_hdl_path)
    : sc_core::sc_module(name)
    {

    ipc::channel::clear_storage((channel_name + "_resp").c_str());
    ipc::channel::clear_storage((channel_name + "_req").c_str());

    req_channel_ = new ipc::channel((channel_name + "_req").c_str(), ipc::sender);
    resp_channel_ = new ipc::channel((channel_name + "_resp").c_str(), ipc::receiver);
    profiling = new SslnProfiling(this->name());

    proxy_ = std::make_shared<umi_axi::axi_slave_proxy>("axi_slave_xtor_proxy", bfm_hdl_path, false, 0x20000000, 22);

    recv_responses = new moodycamel::ReaderWriterQueue<ipc::buffer*>(15);

    SC_THREAD(HandleResponseSc);
    Initialize();

}


AxiSlaveBridge::~AxiSlaveBridge() {
    delete profiling;
    delete recv_responses_;
}




void AxiSlaveBridge::Initialize() {
    // Register callback with proxy
    proxy_->setHandler("AFTER_RECV_TRANS", this, &AxiSlaveBridge::HandleAxiTransaction);
}

void AxiSlaveBridge::HandleAxiTransaction(void* trans) {
    SSLN_LOG_INFO(file_logger, "[{}], Handle AxiTransaction start", this->name());
    auto* axi_trans = static_cast<umi_axi::axi_transaction_item*>(trans);
    if (!axi_trans) {
        throw std::runtime_error("Invalid transaction pointer");
    }
    SSLN_LOG_DEBUG(file_logger, "[{}], axi_trans: {}", this->name(), *axi_trans);
    try {
        // Use the original transaction ID
        uint32_t trans_id = axi_trans->id;
        if (axi_trans->cmd == umi_axi::WRITE) {
            profiling->add_start_time("ProcessWriteRequest");
            ProcessWriteRequest(*axi_trans);
            profiling->add_duration_time("ProcessWriteRequest");
        } else {
            profiling->add_start_time("ProcessReadRequest");
            ProcessReadRequest(*axi_trans);
            profiling->add_duration_time("ProcessReadRequest");
        }
    } catch (const std::exception& e) {
        // Log error and possibly notify proxy of failure
        throw;
    }
    // std::cout << "Handle AxiTransaction end" << std::endl;
    SSLN_LOG_INFO(file_logger, "[{}], Handle AxiTransaction end", this->name());
}

uint64_t AxiSlaveBridge::ProcessWriteRequest(const umi_axi::axi_transaction_item& axi_trans) {
    ValidateTransaction(axi_trans);

    // Register transaction using its ID
    profiling->add_start_time("RegisterTransaction");
    trans_manager_.RegisterTransaction(axi_trans);
    profiling->add_duration_time("RegisterTransaction");

    try {
        // Calculate total valid data size
        size_t tlmpayload_data_bytes = (1 << axi_trans.size) * (axi_trans.len + 1);
        size_t total_tlm_data_bytes = 0;
        size_t payload_size = 0;
        bool is_aligned = (axi_trans.addr % (1 << axi_trans.size) == 0);
        
        // Allocate memory for TlmPayload
        if (axi_trans.be.empty()) {
            total_tlm_data_bytes += tlmpayload_data_bytes;
        } else {
            total_tlm_data_bytes += tlmpayload_data_bytes * 2;
        }
        // Handle user
        if (!axi_trans.req_user.empty()) {
            const uint32_t req_user_bytes = (axi_trans.req_user_width + 7) / 8;
            payload_size += req_user_bytes;
        }
        if (!axi_trans.data_user.empty()) {
            const uint32_t data_user_bytes = (axi_trans.data_w_user_width + 7) / 8;
            payload_size += data_user_bytes;
        }
        payload_size += sizeof(TlmPayload) + total_tlm_data_bytes;
        
        // Prepare IPC payload
        std::unique_ptr<char[]> payload_buffer(new char[payload_size]);
        std::memset(payload_buffer.get(), 0, payload_size);
        auto* tlm_payload = reinterpret_cast<TlmPayload*>(payload_buffer.get());
        tlm_payload->data = reinterpret_cast<unsigned char*>(tlm_payload) + sizeof(TlmPayload);
        std::fill_n(tlm_payload->data, payload_size - sizeof(TlmPayload), 0);
        
        // Initialize TlmPayload fields
        tlm_payload->id = axi_trans.id;
        tlm_payload->command = 1;  // WRITE
        tlm_payload->address = axi_trans.addr; // use original address for write
        tlm_payload->data_length = tlmpayload_data_bytes;
        if (axi_trans.burst == umi_axi::burst_type_t::FIXED) {
            tlm_payload->streaming_width = (1 << axi_trans.size);
        }
        SSLN_LOG_INFO(file_logger, "[{}], tlm payload cmd: {} , id: {}, address: {}, payload size: {}", this->name(), tlm_payload->command, tlm_payload->id, tlm_payload->address, payload_size);
        
        // Use data_packer to pack valid data into TlmPayload
        profiling->add_start_time("PackWriteData");
        data_packer_.PackWriteData(axi_trans, *tlm_payload);
        profiling->add_duration_time("PackWriteData");

        SSLN_LOG_INFO(file_logger, "[{}], After Pack Write data len: {}", this->name(),tlm_payload->data_length);
        SSLN_LOG_DEBUG(file_logger, "[{}], After Pack Write data : {}", this->name(),*tlm_payload);
        // Send to proxy through IPC
        profiling->add_start_time("SendToTlmProxy");
        SendToTlmProxy(*tlm_payload);
        profiling->add_duration_time("SendToTlmProxy");

        return axi_trans.id;
    } catch (const std::exception& e) {
        trans_manager_.CompleteTransaction();
        throw std::runtime_error(std::string("Write request processing failed: ") + e.what());
    }
}

uint64_t AxiSlaveBridge::ProcessReadRequest(const umi_axi::axi_transaction_item& axi_trans) {
    ValidateTransaction(axi_trans);

    // Register transaction using its original ID
    profiling->add_start_time("RegisterTransaction");
    trans_manager_.RegisterTransaction(axi_trans);
    profiling->add_duration_time("RegisterTransaction");

    try {
        // Calculate total data size based on burst length and size
        size_t total_bytes = (axi_trans.len + 1) * (1 << axi_trans.size);
        
        // Get aligned address for TLM payload
        uint64_t aligned_addr = utils::GetAlignedAddress(axi_trans.addr, axi_trans.size);
        
        // Allocate memory for TlmPayload
        size_t total_buffer_size = sizeof(TlmPayload);
        // Handle user
        if (!axi_trans.req_user.empty()) {
            const uint32_t req_user_bytes = (axi_trans.req_user_width + 7) / 8;
            total_buffer_size += req_user_bytes;
        }
        std::unique_ptr<char[]> payload_buffer(new char[total_buffer_size]);
        std::memset(payload_buffer.get(), 0, total_buffer_size);
        auto* tlm_payload = reinterpret_cast<TlmPayload*>(payload_buffer.get());
        if (!axi_trans.req_user.empty()) {
            tlm_payload->data = reinterpret_cast<uint8_t*>(tlm_payload) + sizeof(TlmPayload);
        }
        
        // Initialize TlmPayload fields
        tlm_payload->id = axi_trans.id;
        tlm_payload->command = 0;  // READ
        tlm_payload->address = aligned_addr;  // Use aligned address
        tlm_payload->data_length = total_bytes;
        tlm_payload->byte_enable_length = 0;  // No byte enable for read request
        if (axi_trans.burst == umi_axi::burst_type_t::FIXED) {
            tlm_payload->streaming_width = (1 << axi_trans.size);
        }
        SSLN_LOG_INFO(file_logger, "[{}], tlm payload cmd: {} , id: {}, address: {}, payload size: {}", this->name(), tlm_payload->command, tlm_payload->id, tlm_payload->address, total_buffer_size);
        profiling->add_start_time("PackWriteData");
        data_packer_.PackReadRequest(axi_trans, *tlm_payload);
        profiling->add_duration_time("PackWriteData");

        // Send to proxy through IPC 
        profiling->add_start_time("SendToTlmProxy");
        SendToTlmProxy(*tlm_payload);
        profiling->add_duration_time("SendToTlmProxy");

        return axi_trans.id;
    } catch (const std::exception& e) {
        trans_manager_.CompleteTransaction();
        throw std::runtime_error(std::string("Read request processing failed: ") + e.what());
    }
}


void AxiSlaveBridge::HandleResponseSc(){
    proxy_->xtor_start_run();
    proxy_->wait_reset_deassert();
    SSLN_LOG_INFO(file_logger, "[{}], proxy wait_reset_deassert ok", this->name());

    while (true)
    {
        if(recv_responses_->peek() == nullptr){
            SSLN_LOG_INFO(file_logger, "[{}], wait recv event start", this->name());
            wait(recv_event_);
            SSLN_LOG_INFO(file_logger, "[{}], wait recv event end", this->name());
        }
        
        std::vector<char> data;
        recv_responses_->try_dequeue(data);

        auto* fb_payload = flatbuffers::GetRoot<ssln::hybrid::dispatch::Payload>(data.data());

        // Create a temporary TlmPayload to reuse the existing data_builder logic
        TlmPayload tlm_payload;
        tlm_payload.id = fb_payload->id();
        tlm_payload.command = fb_payload->command();
        tlm_payload.address = fb_payload->address();
        tlm_payload.response = fb_payload->response();
        tlm_payload.data_length = fb_payload->data_length();
        tlm_payload.byte_enable_length = fb_payload->byte_enable_length();
        tlm_payload.streaming_width = fb_payload->streaming_width();
        tlm_payload.axuser_length = fb_payload->axuser_length();
        tlm_payload.xuser_length = fb_payload->xuser_length();
        tlm_payload.data = const_cast<uint8_t*>(fb_payload->variable_data()->data());

        SSLN_LOG_INFO(file_logger, "[{}], Handle Response start", this->name());
        profiling->add_start_time("GetNextTransaction");
        umi_axi::axi_transaction_item* axi_trans = trans_manager_.GetNextTransaction();
        profiling->add_duration_time("GetNextTransaction");

        if (!axi_trans) {
            continue;
        }
       
        try {
            if (axi_trans->cmd == umi_axi::WRITE) {
                profiling->add_start_time("BuildWriteResponse");
                data_builder_.BuildWriteResponse(tlm_payload, *axi_trans);
                profiling->add_duration_time("BuildWriteResponse");
            } else {
                profiling->add_start_time("BuildReadResponse");
                data_builder_.BuildReadResponse(tlm_payload, *axi_trans);
                profiling->add_duration_time("BuildReadResponse");
            }
            SSLN_LOG_INFO(file_logger, "[{}], Response axi_trans addr: {}, id: {}, data size: {}", this->name(), axi_trans->addr, axi_trans->id, axi_trans->data.size());
            SSLN_LOG_DEBUG(file_logger, "[{}], Response axi_trans: {}", this->name(), *axi_trans);

            SSLN_LOG_INFO(file_logger, "[{}], Send_response start", this->name());
            axi_trans->print();
            profiling->add_start_time("SendResponse");
            proxy_->send_response(*axi_trans);
            SSLN_LOG_INFO(file_logger, "[{}], Send_response end", this->name());
            profiling->add_duration_time("SendResponse");
            trans_manager_.CompleteTransaction();
        } catch (const std::exception& e) {
            trans_manager_.CompleteTransaction();
            throw std::runtime_error(std::string("Response handling failed: ") + e.what());
        }
        SSLN_LOG_INFO(file_logger, "[{}], Handle Response end", this->name());
    }
}


void AxiSlaveBridge::ValidateTransaction(const umi_axi::axi_transaction_item& axi_trans) {
    SSLN_LOG_INFO(file_logger, "[{}], Validate transaction start", this->name());
    // Validate burst type
    if (axi_trans.burst > umi_axi::burst_type_t::WRAP) {
        throw std::invalid_argument("Invalid burst type");
    }

    // Validate size
    if (axi_trans.size >= 8) {  // Maximum size is 128 bytes (size = 7)
        throw std::invalid_argument("Invalid transfer size");
    }

    // For WRAP burst, validate address alignment
    if (axi_trans.burst == umi_axi::burst_type_t::WRAP) {
        uint32_t burst_size = 1 << axi_trans.size;
        if (axi_trans.addr % burst_size != 0) {
            throw std::invalid_argument("Address must be aligned for WRAP burst");
        }
    }
    if (Check4KBoundary(axi_trans)) {
        throw std::invalid_argument("Address must be under 4K boundary");
    }
    SSLN_LOG_INFO(file_logger, "[{}], Validate transaction end", this->name());
}

bool AxiSlaveBridge::Check4KBoundary(const umi_axi::axi_transaction_item& axi_trans) {
    uint32_t total_bytes = (axi_trans.len + 1) * (1 << axi_trans.size);
    const uint64_t addr_upper_4k = axi_trans.addr | 0xFFF; 
    if (axi_trans.burst != umi_axi::burst_type_t::FIXED && total_bytes > 0x1000) return true;
    if (axi_trans.burst == umi_axi::burst_type_t::INCR) {
        if (axi_trans.addr + total_bytes - 1 > (axi_trans.addr | 0xFFF)) {
            return true;
        }
    } else if (axi_trans.burst == umi_axi::burst_type_t::FIXED) {
        if (axi_trans.addr + (1 << axi_trans.size) - 1 > addr_upper_4k) {
            return true;
        }
    } else if (axi_trans.burst == umi_axi::burst_type_t::WRAP) {
        const uint64_t lower_wrap_boundary = (axi_trans.addr / total_bytes) * total_bytes;
        const uint64_t upper_wrap_boundary = lower_wrap_boundary + total_bytes;
        if (upper_wrap_boundary > addr_upper_4k) {
            return true;
        }
    }
    return false;
}

bool AxiSlaveBridge::SendToTlmProxy(const TlmPayload& tlm_payload) {
    SSLN_LOG_INFO(file_logger, "[{}], Send request to tlm proxy start", this->name());
    
    flatbuffers::FlatBufferBuilder builder;

    std::vector<uint8_t> variable_data;
    if (tlm_payload.command == tlm::TLM_WRITE_COMMAND) {
        variable_data.insert(variable_data.end(), tlm_payload.data, tlm_payload.data + tlm_payload.data_length);
        variable_data.insert(variable_data.end(), tlm_payload.data + tlm_payload.data_length, tlm_payload.data + tlm_payload.data_length + tlm_payload.byte_enable_length);
        variable_data.insert(variable_data.end(), tlm_payload.data + tlm_payload.data_length + tlm_payload.byte_enable_length, tlm_payload.data + tlm_payload.data_length + tlm_payload.byte_enable_length + tlm_payload.axuser_length);
        variable_data.insert(variable_data.end(), tlm_payload.data + tlm_payload.data_length + tlm_payload.byte_enable_length + tlm_payload.axuser_length, tlm_payload.data + tlm_payload.data_length + tlm_payload.byte_enable_length + tlm_payload.axuser_length + tlm_payload.xuser_length);
    } else { // READ
        variable_data.insert(variable_data.end(), tlm_payload.data, tlm_payload.data + tlm_payload.axuser_length);
    }

    auto fb_variable_data = builder.CreateVector(variable_data);

    auto payload = ssln::hybrid::dispatch::CreatePayload(
        builder,
        dest_id_,
        tlm_payload.id,
        tlm_payload.command,
        tlm_payload.address,
        tlm_payload.response,
        tlm_payload.streaming_width,
        fb_variable_data,
        tlm_payload.data_length,
        tlm_payload.byte_enable_length,
        tlm_payload.axuser_length,
        tlm_payload.xuser_length);
        
    builder.Finish(payload);

    bool ret = dispatcher_->send(dest_id_, builder.GetBufferPointer(), builder.GetSize());
    
    SSLN_LOG_INFO(file_logger, "[{}], Send request to tlm proxy end, size: {}", this->name(), builder.GetSize());

    if (!ret) {
        throw std::runtime_error("Failed to send payload to proxy");
    }
    return ret;
}

uint32_t AxiSlaveBridge::get_component_id() const {
    return comp_id_;
}

bool AxiSlaveBridge::handle_data(const void* data, size_t size) {
    SSLN_LOG_INFO(file_logger, "[{}], handle_data received data size: {}", this->name(), size);
    
    std::vector<char> data_vec(static_cast<const char*>(data), static_cast<const char*>(data) + size);

    if (recv_responses_->try_enqueue(std::move(data_vec))) {
        recv_event_.notify(sc_core::SC_ZERO_TIME);
        return true;
    } else {
        SSLN_LOG_ERROR(file_logger, "[{}], Response queue full, data loss!!!", this->name());
        return false;
    }
}

}  // namespace axi
}  // namespace hybrid
}  // namespace ssln 
