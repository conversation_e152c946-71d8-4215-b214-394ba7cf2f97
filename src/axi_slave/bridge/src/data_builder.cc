#include "data_builder.h"

#include <cstring>
#include <stdexcept>
#include <cassert>
namespace ssln {
namespace hybrid {
namespace axi {

// Pre-requisite:
// 1. tlm_payload.data is already aligned to the AXI address

void DataBuilder::BuildWriteResponse(const TlmPayload& tlm_payload,
                                   umi_axi::axi_transaction_item& axi_trans) {

    // Handle user
    if (tlm_payload.xuser_length > 0 && tlm_payload.data != nullptr) {
        // For write response, we only need the first byte of user data
        const uint32_t buser_bytes = (axi_trans.resp_w_user_width + 7) / 8;
        const uint32_t min_buser_length = std::min(buser_bytes, tlm_payload.xuser_length);
        std::cout << "BuildWriteResponse buser_bytes: " << buser_bytes << " xuser_length: " << tlm_payload.xuser_length << std::endl;
        if (buser_bytes > 0) {
            axi_trans.resp_user.reserve(min_buser_length);
            axi_trans.resp_user.resize(min_buser_length, 0);
            memcpy(axi_trans.resp_user.data(), tlm_payload.data, min_buser_length);
        }
    }
    // For write response, we only need to set the response status
    GenerateAxiResponse(tlm_payload, axi_trans);
}

void DataBuilder::BuildReadResponse(const TlmPayload& tlm_payload,
                                  umi_axi::axi_transaction_item& axi_trans) {
    // Calculate basic parameters
    const uint64_t current_address = axi_trans.addr;
    const uint32_t number_bytes = 1 << axi_trans.size;
    uint64_t aligned_address = (current_address / number_bytes) * number_bytes;
    const uint64_t address_offset = current_address - aligned_address;
    const uint32_t data_width_bytes = axi_trans.data_width >> 3;
    const uint32_t total_beats = axi_trans.len + 1;

    // Pre-calculate sizes and reserve space
    const uint32_t total_data_size = total_beats * data_width_bytes;
    axi_trans.data.reserve(total_data_size);
    axi_trans.be.reserve(total_data_size);
    axi_trans.data.resize(total_data_size, 0);
    axi_trans.be.resize(total_data_size, 0);

    // Calculate wrap boundaries if needed
    const uint32_t dtsize = number_bytes * total_beats;
    const uint64_t lower_wrap_boundary = (current_address / dtsize) * dtsize;
    const uint64_t upper_wrap_boundary = lower_wrap_boundary + dtsize;

    // Process each beat
    uint32_t data_offset = 0;
    uint64_t curr_addr = current_address;
    bool is_aligned = (current_address % number_bytes == 0);
    const uint8_t* src_data = tlm_payload.data;
    // handle user 
    if (tlm_payload.xuser_length > 0 && tlm_payload.data != nullptr) {
        const uint32_t resp_user_bytes = (axi_trans.data_r_user_width + 7) / 8;
        axi_trans.data_user.reserve(resp_user_bytes);
        axi_trans.data_user.resize(resp_user_bytes, 0);
        memcpy(axi_trans.data_user.data(), tlm_payload.data + tlm_payload.data_length, resp_user_bytes);
    }
    
    for (uint32_t beat = 0; beat < total_beats; ++beat) {
        const uint32_t beat_offset = beat * data_width_bytes;
        const uint32_t lower_byte_lane = curr_addr % data_width_bytes;
        const uint32_t upper_byte_lane = is_aligned ? 
            lower_byte_lane + number_bytes - 1 :
            (aligned_address + number_bytes - 1) % data_width_bytes;
        // Fill data and byte enables for this beat
        const uint32_t max_lane = std::min(upper_byte_lane + 1, data_width_bytes);
        uint32_t src_offset = 0;
        if (axi_trans.burst == umi_axi::burst_type_t::WRAP) {
            src_offset = curr_addr - lower_wrap_boundary;
            data_offset = 0;
        }
        for (uint32_t i = lower_byte_lane; i < max_lane; ++i) {
            if (address_offset + data_offset < tlm_payload.data_length) {
                axi_trans.data[beat_offset + i] = src_data[address_offset + data_offset + src_offset];
                axi_trans.be[beat_offset + i] = 0xFF;
                ++data_offset;
            }
        }


        // Update address for next beat
        if (axi_trans.burst != umi_axi::burst_type_t::FIXED) {
            if (is_aligned) {
                curr_addr += number_bytes;
                if (axi_trans.burst == umi_axi::burst_type_t::WRAP && 
                    curr_addr >= upper_wrap_boundary) {
                    curr_addr = lower_wrap_boundary;
                }
            } else {
                curr_addr = aligned_address + number_bytes;
                is_aligned = true;
                aligned_address = curr_addr;
            }
        } else {
            if (!is_aligned) {
                data_offset += address_offset;
            }
        }
        SSLN_LOG_DEBUG(file_logger, "[{}], cur address: {}", this->name(), curr_addr);
    }
    SSLN_LOG_DEBUG(file_logger, "[{}], axi data size: {}", this->name(), axi_trans.data.size());
    GenerateAxiResponse(tlm_payload, axi_trans);
}

void DataBuilder::GenerateAxiResponse(const TlmPayload& tlm_payload,
                                    umi_axi::axi_transaction_item& axi_trans) {
    SSLN_LOG_DEBUG(file_logger, "[{}], trans.resp before size: {}", this->name(), axi_trans.resp.size());
    if(tlm_payload.command == 0){
        for(int i = 0; i < axi_trans.len + 1; i++){
            axi_trans.resp.push_back(ConvertTlmResponseToAxi(tlm_payload.response));
        }
    }
    else{
        axi_trans.resp.push_back(ConvertTlmResponseToAxi(tlm_payload.response));
    }
    SSLN_LOG_DEBUG(file_logger, "[{}], trans.resp after size: {}", this->name(), axi_trans.resp.size());
}

umi_axi::response_type_t DataBuilder::ConvertTlmResponseToAxi(uint8_t tlm_response) {
    // Convert TLM response status to AXI response
    switch (tlm_response) {
        case 1:  // TLM_OK_RESPONSE
            return umi_axi::response_type_t::OKAY;  // OKAY
        case 2:  // TLM_INCOMPLETE_RESPONSE
            return umi_axi::response_type_t::EXOKAY;  // EXOKAY
        case 4:  // TLM_GENERIC_ERROR_RESPONSE
            return umi_axi::response_type_t::SLVERR;  // SLVERR
        case 8:  // TLM_ADDRESS_ERROR_RESPONSE
            return umi_axi::response_type_t::DECERR;  // DECERR
        default:
            return umi_axi::response_type_t::SLVERR;  // SLVERR for unknown responses
    }
}

}  // namespace axi
}  // namespace hybrid
}  // namespace ssln 