#include "data_packer.h"
#include "axi_burst_resolver.h"
#include "axi_utils.h"

#include <cassert>
#include <cstring>
#include <stdexcept>
#include <algorithm>
namespace ssln {
namespace hybrid {
namespace axi {

void DataPacker::PackWriteData(const umi_axi::axi_transaction_item& axi_trans, TlmPayload& tlm_payload) {

    // Calculate total valid data size
    const uint32_t data_width = (axi_trans.data_width+7)/8;
    const uint32_t number_bytes = 1 << axi_trans.size;
    const uint32_t total_beats = axi_trans.len + 1;
    const uint32_t total_number_bytes = number_bytes * total_beats;
    uint32_t total_tlm_data_bytes = total_number_bytes;

    const uint32_t data_user_bytes = (axi_trans.data_w_user_width + 7) / 8 ;
    const uint32_t req_user_bytes = (axi_trans.req_user_width + 7) / 8;

    tlm_payload.data_length = total_number_bytes;
    // calculate wrap boudary
    const uint64_t lower_wrap_boundary = utils::GetLowWrapBoundary(axi_trans.addr, axi_trans.size, axi_trans.len);
    const uint64_t upper_wrap_boundary = utils::GetHighWrapBoundary(axi_trans.addr, axi_trans.size, axi_trans.len);

    // Create burst resolver for address calculation
    AxiBurstResolver resolver(axi_trans.addr, number_bytes,
                            total_beats, data_width,
                            axi_trans.burst);

    if (axi_trans.be.empty()) {
        tlm_payload.byte_enable_length = 0;
    } else {
        assert(axi_trans.be.size() >= total_tlm_data_bytes);
        tlm_payload.byte_enable_length = total_tlm_data_bytes;
    }
    total_tlm_data_bytes += tlm_payload.byte_enable_length;
    // Handle request user signal (awuser/wuser)
    if (!axi_trans.req_user.empty() && req_user_bytes > 0) {
        std::memcpy(tlm_payload.data + total_tlm_data_bytes,
                   axi_trans.req_user.data(),
                   req_user_bytes);
        tlm_payload.axuser_length = req_user_bytes;
    } else {
        tlm_payload.axuser_length = 0;
    }
    if (!axi_trans.data_user.empty() && data_user_bytes > 0) {
        tlm_payload.xuser_length = data_user_bytes;
        std::memcpy(tlm_payload.data + total_tlm_data_bytes + req_user_bytes, axi_trans.data_user.data(), data_user_bytes);
    } else {
        tlm_payload.xuser_length = 0;
    }
    uint64_t aligned_addr = utils::GetAlignedAddress(axi_trans.addr, axi_trans.size);
    tlm_payload.address = aligned_addr;
    // padding 0 for offset
    uint32_t padding_length = utils::GetStartOffset(axi_trans.addr, axi_trans.size);;
    std::memset(tlm_payload.data, 0, padding_length);
    if (!axi_trans.be.empty()) {
        std::memset(tlm_payload.data + total_number_bytes, 0, padding_length);
    }
    if (axi_trans.burst == umi_axi::burst_type_t::WRAP) {
        tlm_payload.address = lower_wrap_boundary;
    }
    // Process each beat
    uint32_t data_index = 0;
    uint32_t beat_index = 0;
    uint32_t wrap_index = 0;
    while (resolver.HasNext()) {
        TransferInfo transfer = resolver.Next();
        uint64_t cur_address = transfer.address;
        // data offset is the offset of the first valid byte in the transfer
        uint32_t data_offset = utils::GetStartOffset(cur_address, axi_trans.size);
        uint32_t axi_data_index = beat_index * data_width;
        uint32_t tlm_data_index = beat_index * number_bytes;;


        // Copy valid data bytes from this beat
        uint32_t valid_bytes = transfer.upper_byte_lane - transfer.lower_byte_lane + 1;
        // For wrap write
        if (axi_trans.burst == umi_axi::burst_type_t::WRAP) {
            tlm_data_index += (axi_trans.addr - lower_wrap_boundary);
            tlm_data_index = tlm_data_index % total_number_bytes;
        } 
        
        SSLN_LOG_DEBUG(file_logger, "[{}], tlm_data_index: {},  cur_address: {}, lower_wrap_boundary: {}", this->name(), tlm_data_index, cur_address, lower_wrap_boundary);
        if (data_index + valid_bytes <= total_number_bytes) {
            std::memcpy(tlm_payload.data + tlm_data_index + data_offset,
                       axi_trans.data.data() + axi_data_index + transfer.lower_byte_lane,
                       valid_bytes);

            // Set byte enables for valid bytes
            if (!axi_trans.be.empty()) {
                std::memcpy(tlm_payload.data + total_number_bytes + tlm_data_index + data_offset,
                           axi_trans.be.data() + axi_data_index + transfer.lower_byte_lane,
                           valid_bytes);
            }
        }
        beat_index++;
        data_index += valid_bytes;
    }
    SSLN_LOG_DEBUG(file_logger, "[{}], Pack write data: {}", this->name(), tlm_payload);
}

void DataPacker::PackReadRequest(const umi_axi::axi_transaction_item& axi_trans, TlmPayload& tlm_payload) {
    // calculate wrap boudary
    const uint64_t lower_wrap_boundary = utils::GetLowWrapBoundary(axi_trans.addr, axi_trans.size, axi_trans.len);
    const uint64_t upper_wrap_boundary = utils::GetHighWrapBoundary(axi_trans.addr, axi_trans.size, axi_trans.len);
    if (axi_trans.burst == umi_axi::burst_type_t::WRAP) {
        tlm_payload.address = lower_wrap_boundary;
    }
    
    // Handle request user signal (aruser)
    if (!axi_trans.req_user.empty()) {
        const uint32_t req_user_bytes = (axi_trans.req_user_width + 7) / 8;
        tlm_payload.axuser_length = req_user_bytes;
        tlm_payload.xuser_length = (axi_trans.data_r_user_width + 7) / 8; 

        std::memcpy(tlm_payload.data,
                   axi_trans.req_user.data(),
                   req_user_bytes);
        SSLN_LOG_DEBUG(file_logger, "[{}], Pack read data: {}", this->name(), tlm_payload);
    } else {
        tlm_payload.axuser_length = 0;
        tlm_payload.xuser_length = 0;
    }

    // For read request, we only need to set the data length
    tlm_payload.data_length = (1 << axi_trans.size) * (axi_trans.len + 1);
}

}  // namespace axi
}  // namespace hybrid
}  // namespace ssln 