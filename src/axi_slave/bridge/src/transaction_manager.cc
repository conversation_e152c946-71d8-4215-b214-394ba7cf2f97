#include "transaction_manager.h"
#include <stdexcept>

namespace ssln {
namespace hybrid {
namespace axi {

TransactionManager::TransactionManager(size_t max_outstanding)
    : capacity_(max_outstanding) {
    if (capacity_ == 0) {
        throw std::invalid_argument("max_outstanding must be greater than 0");
    }
    trans_pool_.reserve(capacity_);
    trans_pool_.resize(capacity_);
    transactions_.reserve(capacity_);
    transactions_.resize(capacity_, nullptr);
}

TransactionManager::~TransactionManager() {
    trans_pool_.clear();
    transactions_.clear();
}

size_t TransactionManager::RegisterTransaction(const axi_transaction_item& axi_trans) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (count_ >= capacity_) {
        throw std::runtime_error("Transaction buffer is full");
    }
    
    try {
        // Use transaction from pool
        CopyTransactionFields(axi_trans, trans_pool_[tail_]);
        std::cout << "RegisterTransaction tail: " << tail_ << std::endl;
        transactions_[tail_] = &trans_pool_[tail_];
        std::cout << "tail_ = " << tail_ << ", capacity_ = " << capacity_ << std::endl;
        // Update ring buffer state
        size_t current_index = tail_;
        tail_ = (tail_ + 1) % capacity_;
        count_++;
        
        return current_index;
    } catch (const std::exception& e) {
        throw std::runtime_error("Failed to register transaction: " + std::string(e.what()));
    }
}

axi_transaction_item* TransactionManager::GetNextTransaction() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (count_ == 0) {
        return nullptr;
    }
    std::cout << "GetNextTransaction: " << head_ << std::endl;
    return transactions_[head_];
}

void TransactionManager::CompleteTransaction() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (count_ == 0) {
        throw std::runtime_error("No transaction to complete");
    }
    
    // Clear the transaction pointer (actual object remains in pool)
    transactions_[head_] = nullptr;
    
    // Update ring buffer state
    head_ = (head_ + 1) % capacity_;
    count_--;
}

size_t TransactionManager::GetOutstandingCount() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return count_;
}

void TransactionManager::CopyTransactionFields(const axi_transaction_item& src, axi_transaction_item& dst) {
    std::cout << "Copying Transaction Fields" << std::endl;
    // Copy control information
    // dst.do_copy(src);
    dst.addr = src.addr;
    dst.burst = src.burst;
    dst.len = src.len;
    dst.size = src.size;
    dst.cmd = src.cmd;
    dst.id = src.id;
    dst.data_width = src.data_width;

    // Clear data and be vectors - DataBuilder will manage them
    dst.data.clear();
    dst.data.shrink_to_fit();
    dst.be.clear();
    dst.be.shrink_to_fit();

    // Handle user
    dst.req_user_width = src.req_user_width;
    dst.resp_w_user_width = src.resp_w_user_width;
    dst.data_w_user_width = src.data_w_user_width;
    dst.data_r_user_width = src.data_r_user_width;
    dst.req_user.clear();
    dst.req_user.shrink_to_fit();
    dst.data_user.clear();
    dst.data_user.shrink_to_fit();
    dst.resp_user.clear();
    dst.resp_user.shrink_to_fit();

    dst.resp.clear(); 

    dst.last.clear();
}

}  // namespace axi
}  // namespace hybrid
}  // namespace ssln 