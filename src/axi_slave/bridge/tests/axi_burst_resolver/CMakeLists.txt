add_executable(axi_burst_resolver_test
    src/axi_burst_resolver_test.cc
)


target_link_directories(axi_burst_resolver_test
    PRIVATE
        ${PROJECT_ROOT}/thirdparty/fake_install/lib
        $ENV{UMICOM_HOME}/lib
)

target_link_libraries(axi_burst_resolver_test
    PRIVATE
        GTest::gtest_main
        axi_slave_bridge
        fake_xtor
        systemc
)

include(GoogleTest)
gtest_discover_tests(axi_burst_resolver_test)

add_dependencies(build_utests axi_burst_resolver_test)