#include <systemc>
#include <gtest/gtest.h>
#include "axi_burst_resolver.h"

using namespace umi_axi;

namespace ssln::hybrid::axi {

class AxiBurstResolverTest : public ::testing::Test {
 protected:
  void SetUp() override {
  }

  void TearDown() override {
  }
};

TEST_F(AxiBurstResolverTest, IncrModeTest) {
  AxiBurstResolver resolver(0x1000, 4, 4, 8, burst_type_t::INCR);
  
  EXPECT_TRUE(resolver.HasNext());
  TransferInfo info = resolver.Next();
  EXPECT_EQ(info.address, 0x1000);
  EXPECT_EQ(info.lower_byte_lane, 0);
  EXPECT_EQ(info.upper_byte_lane, 3);
  
  EXPECT_TRUE(resolver.HasNext());
  info = resolver.Next();
  EXPECT_EQ(info.address, 0x1004);
  EXPECT_EQ(info.lower_byte_lane, 4);
  EXPECT_EQ(info.upper_byte_lane, 7);
  
  EXPECT_TRUE(resolver.HasNext());
  info = resolver.Next();
  EXPECT_EQ(info.address, 0x1008);
  EXPECT_EQ(info.lower_byte_lane, 0);
  EXPECT_EQ(info.upper_byte_lane, 3);
  
  EXPECT_TRUE(resolver.HasNext());
  info = resolver.Next();
  EXPECT_EQ(info.address, 0x100C);
  EXPECT_EQ(info.lower_byte_lane, 4);
  EXPECT_EQ(info.upper_byte_lane, 7);
  
  EXPECT_FALSE(resolver.HasNext());
}

TEST_F(AxiBurstResolverTest, WrapModeTest) {
  AxiBurstResolver resolver(0x1002, 2, 8, 8, burst_type_t::WRAP);
  
  EXPECT_TRUE(resolver.HasNext());
  TransferInfo info = resolver.Next();
  EXPECT_EQ(info.address, 0x1002);
  EXPECT_EQ(info.lower_byte_lane, 2);
  EXPECT_EQ(info.upper_byte_lane, 3);
  
  EXPECT_TRUE(resolver.HasNext());
  info = resolver.Next();
  EXPECT_EQ(info.address, 0x1004);
  EXPECT_EQ(info.lower_byte_lane, 4);
  EXPECT_EQ(info.upper_byte_lane, 5);

  EXPECT_TRUE(resolver.HasNext());
  info = resolver.Next();
  EXPECT_EQ(info.address, 0x1006);
  EXPECT_EQ(info.lower_byte_lane, 6);
  EXPECT_EQ(info.upper_byte_lane, 7);

  EXPECT_TRUE(resolver.HasNext());
  info = resolver.Next();
  EXPECT_EQ(info.address, 0x1008);
  EXPECT_EQ(info.lower_byte_lane, 0);
  EXPECT_EQ(info.upper_byte_lane, 1);

  EXPECT_TRUE(resolver.HasNext());
  info = resolver.Next();
  EXPECT_EQ(info.address, 0x100A);
  EXPECT_EQ(info.lower_byte_lane, 2);
  EXPECT_EQ(info.upper_byte_lane, 3);

  EXPECT_TRUE(resolver.HasNext());
  info = resolver.Next();
  EXPECT_EQ(info.address, 0x100C);
  EXPECT_EQ(info.lower_byte_lane, 4);
  EXPECT_EQ(info.upper_byte_lane, 5);

  EXPECT_TRUE(resolver.HasNext());
  info = resolver.Next();
  EXPECT_EQ(info.address, 0x100E);
  EXPECT_EQ(info.lower_byte_lane, 6);
  EXPECT_EQ(info.upper_byte_lane, 7);

  EXPECT_TRUE(resolver.HasNext());
  info = resolver.Next();
  EXPECT_EQ(info.address, 0x1000);
  EXPECT_EQ(info.lower_byte_lane, 0);
  EXPECT_EQ(info.upper_byte_lane, 1);

  EXPECT_FALSE(resolver.HasNext());
}

TEST_F(AxiBurstResolverTest, FixedModeTest) {
  AxiBurstResolver resolver(0x1003, 4, 16, 16, burst_type_t::FIXED);
  
  EXPECT_TRUE(resolver.HasNext());
  TransferInfo info = resolver.Next();
  EXPECT_EQ(info.address, 0x1003);
  EXPECT_EQ(info.lower_byte_lane, 3);
  EXPECT_EQ(info.upper_byte_lane, 3);

  EXPECT_TRUE(resolver.HasNext());
  info = resolver.Next();
  EXPECT_EQ(info.address, 0x1003);
  EXPECT_EQ(info.lower_byte_lane, 3);
  EXPECT_EQ(info.upper_byte_lane, 3);

  EXPECT_TRUE(resolver.HasNext());
  info = resolver.Next();
  EXPECT_EQ(info.address, 0x1003);
  EXPECT_EQ(info.lower_byte_lane, 3);
  EXPECT_EQ(info.upper_byte_lane, 3);
}

}  // namespace ssln::hybrid::axi

int sc_main(int argc, char **argv) {
  ::testing::InitGoogleTest(&argc, argv);
  return RUN_ALL_TESTS();
} 