#include <systemc.h>
#include "axi_slave_proxy.h"
#include "axi_slave_bridge.h"
#include <gtest/gtest.h>
#include "tlm_payload.h"
#include "axi_transaction_item.h"
#include <thread>
#include <systemc>
#include <tlm>
#include "async_event.h"
#include "axi_utils.h"

namespace ssln::hybrid::axi {

using namespace umi_axi;

const int sleep_time = 200;

class ScStopModule: public sc_core::sc_module{
public:
    SC_HAS_PROCESS(ScStopModule);
    ScStopModule(sc_core::sc_module_name name): sc_core::sc_module(name){

        SC_THREAD(StopThread);
    }

    void StopThread(){
        while(true){
            wait(stop_event);
            sc_core::sc_stop();
        }
    }

    async_event stop_event;
};

class FakeSlave: public sc_core::sc_module{
public:
    SC_HAS_PROCESS(FakeSlave);
    FakeSlave(sc_core::sc_module_name name, std::shared_ptr<axi_slave_proxy> axi_proxy)
            :sc_module(name),
             xtor_slave_proxy_(std::move(axi_proxy)){
        send_seq = new async_event("send_seq");
        SC_THREAD(send_seq_function);
    }
    
    ~FakeSlave(){
        delete send_seq;
    }

    void send_seq_function(){
        while(true){
            std::cout << "send_seq_function wait start" << std::endl;
            wait(*send_seq);
            std::cout << "send_seq_function wait end" << std::endl;
            std::cout << "seq.size: " << seq.size() << std::endl; 
            for(auto axi_trans: seq){
                xtor_slave_proxy_->triggerEvent("AFTER_RECV_TRANS", axi_trans);
            }
        }
    }
    
    std::shared_ptr<axi_slave_proxy> xtor_slave_proxy_;
    vector<axi_transaction_item*> seq;
    async_event* send_seq;

};

class AxiSlaveBridgeTest : public ::testing::Test {
 protected:
    void SetUp() override {
        std::cout << "create" << endl;
        std::string channel_name = "axi_slave_bridge_test";
        xtor_slave_proxy_ = std::make_shared<axi_slave_proxy>(
                256,  
                spec_version_t::AMBA4
            );
        req_channel_ = new ipc::channel((channel_name + "_req").c_str(), ipc::receiver);
        resp_channel_ = new ipc::channel((channel_name + "_resp").c_str(), ipc::sender);
        axi_slave_bridge_ = std::make_shared<AxiSlaveBridge>("axi_slave_bridge", channel_name, xtor_slave_proxy_);
        fake_slave_ = std::make_shared<FakeSlave>("fake_slave", xtor_slave_proxy_);
        stop_module_ = new ScStopModule("stopmodule");
        sim_thread_ = new std::thread([]() {
                sc_core::sc_start();
                std::cout << "sc end" << std::endl;
            });
            
    }

    void TearDown() override {
        stop_module_->stop_event.notify();
        if (sim_thread_->joinable()) {
            sim_thread_->join();
        }
        delete stop_module_;
        delete sim_thread_;
        req_channel_->disconnect();
        resp_channel_->disconnect();
        delete req_channel_;
        delete resp_channel_;
        sc_core::sc_curr_simcontext = new sc_core::sc_simcontext();
        sc_core::sc_default_global_context = nullptr;
        }

    void initialize_write_transaction(axi_transaction_item &trans, uint32_t data_size) {
        trans.data = std::vector<uint8_t>(data_size, 0);
        trans.be = std::vector<uint8_t>(data_size, 0xFF);  

        trans.cmd = umi_axi::WRITE;  
        trans.id = 0x123;            
        trans.addr = 0x1000;         
        trans.size = umi_axi::BYTE_4;  
        trans.len = 5;              
        trans.burst = umi_axi::INCR;  
        trans.data_width = umi_axi::BIT_32;  

        for (int i = 0; i < data_size; ++i) {
            trans.data[i] = static_cast<uint8_t>(i);  
        }

        std::cout << "Write Transaction initialized with " << data_size << " bytes." << std::endl;
}

    std::shared_ptr<axi_slave_proxy> xtor_slave_proxy_;
    std::shared_ptr<AxiSlaveBridge> axi_slave_bridge_;
    std::shared_ptr<FakeSlave> fake_slave_;
    ipc::channel* req_channel_;
    ipc::channel* resp_channel_;
    ScStopModule* stop_module_;
    std::thread* sim_thread_;
};

TEST_F(AxiSlaveBridgeTest, R_INCR) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    uint32_t data_size = 4096;
    trans.cmd = umi_axi::READ;
    trans.id = 0x123;
    trans.addr = 0x1000;
    trans.size = size_type_t::BYTE_32;
    trans.len = 127;  
    trans.burst = umi_axi::burst_type_t::INCR;
    trans.data_width = umi_axi::bus_width_t::BIT_256;
  
    fake_slave_->seq.push_back(&trans);
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    auto recv_data = req_channel_->recv();
    EXPECT_EQ(recv_data.empty(), false);
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload));
    TlmPayload* req_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    EXPECT_EQ(req_payload->data, nullptr);
    EXPECT_EQ(req_payload->command, tlm::TLM_READ_COMMAND);
    EXPECT_EQ(req_payload->data_length, data_size);
    EXPECT_EQ(req_payload->streaming_width, 0x0);
    EXPECT_EQ(req_payload->address, utils::GetAlignedAddress(trans.addr, trans.size));

    uint32_t resp_size = sizeof(TlmPayload) + data_size;
    uint8_t* resp_ptr = new uint8_t [resp_size];
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(resp_ptr);
    memcpy(resp_payload, req_payload, sizeof(TlmPayload));
    resp_payload->data = resp_ptr + sizeof(TlmPayload);
    for(int i = 0; i < data_size; ++i){
        resp_payload->data[i] = i % 256;
    }
    resp_payload->response = tlm::TLM_OK_RESPONSE;
    
    resp_channel_->send(resp_payload, resp_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x1000);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 127);
    EXPECT_EQ(item.size, size_type_t::BYTE_32);
    EXPECT_EQ(item.burst, burst_type_t::INCR);
    EXPECT_EQ(item.cmd, cmd_type_t::READ);

    delete [] resp_ptr;
}

TEST_F(AxiSlaveBridgeTest, R_INCR_NARROW) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    trans.cmd = umi_axi::READ;
    trans.id = 0x123;
    trans.addr = 0x0034;
    trans.size = size_type_t::BYTE_4;
    trans.len = 4;  
    trans.burst = umi_axi::burst_type_t::INCR;
    trans.data_width = umi_axi::bus_width_t::BIT_64;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);
  
    fake_slave_->seq.push_back(&trans);
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    auto recv_data = req_channel_->recv();
    EXPECT_EQ(recv_data.empty(), false);
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload));
    TlmPayload* req_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    EXPECT_EQ(req_payload->data, nullptr);
    EXPECT_EQ(req_payload->command, tlm::TLM_READ_COMMAND);
    EXPECT_EQ(req_payload->data_length, data_size);
    EXPECT_EQ(req_payload->streaming_width, 0x0);
    EXPECT_EQ(req_payload->address, utils::GetAlignedAddress(trans.addr, trans.size));

    uint32_t resp_size = sizeof(TlmPayload) + data_size;
    uint8_t* resp_ptr = new uint8_t [resp_size];
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(resp_ptr);
    memcpy(resp_payload, req_payload, sizeof(TlmPayload));
    resp_payload->data = resp_ptr + sizeof(TlmPayload);
    for(int i = 0; i < data_size; ++i){
        resp_payload->data[i] = static_cast<uint8_t>(i);
    }
    resp_payload->response = tlm::TLM_OK_RESPONSE;

    std::vector<uint8_t> expected_data = {
        0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x02, 0x03,
        0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b,
        0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b,
        0x0c, 0x0d, 0x0e, 0x0f, 0x10, 0x11, 0x12, 0x13,
        0x0c, 0x0d, 0x0e, 0x0f, 0x10, 0x11, 0x12, 0x13
    };

    std::vector<uint8_t> expected_be = {
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF
    };
    
    resp_channel_->send(resp_payload, resp_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x0034);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 4);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::INCR);
    EXPECT_EQ(item.cmd, cmd_type_t::READ);
    for (int i = 0; i < item.data.size(); ++i) {
        EXPECT_EQ(item.data[i]&item.be[i], expected_data[i]&expected_be[i]);
    }

    delete [] resp_ptr;
}

TEST_F(AxiSlaveBridgeTest, R_INCR_UNALIGN) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    trans.cmd = umi_axi::READ;
    trans.id = 0x123;
    trans.addr = 0x0037;
    trans.size = size_type_t::BYTE_4;
    trans.len = 4;  
    trans.burst = umi_axi::burst_type_t::INCR;
    trans.data_width = umi_axi::bus_width_t::BIT_32;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);
  
    fake_slave_->seq.push_back(&trans);
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    auto recv_data = req_channel_->recv();
    EXPECT_EQ(recv_data.empty(), false);
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload));
    TlmPayload* req_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    EXPECT_EQ(req_payload->data, nullptr);
    EXPECT_EQ(req_payload->command, tlm::TLM_READ_COMMAND);
    EXPECT_EQ(req_payload->data_length, data_size);
    EXPECT_EQ(req_payload->streaming_width, 0x0);
    EXPECT_EQ(req_payload->address, utils::GetAlignedAddress(trans.addr, trans.size));

    uint32_t resp_size = sizeof(TlmPayload) + data_size;
    uint8_t* resp_ptr = new uint8_t [resp_size];
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(resp_ptr);
    memcpy(resp_payload, req_payload, sizeof(TlmPayload));
    resp_payload->data = resp_ptr + sizeof(TlmPayload);
    for(int i = 0; i < data_size; ++i){
        resp_payload->data[i] = static_cast<uint8_t>(i);
    }
    resp_payload->response = tlm::TLM_OK_RESPONSE;

    std::vector<uint8_t> expected_data = {
        0x00, 0x01, 0x02, 0x03,
        0x04, 0x05, 0x06, 0x07, 
        0x08, 0x09, 0x0a, 0x0b,
        0x0c, 0x0d, 0x0e, 0x0f, 
        0x10, 0x11, 0x12, 0x13,
    };

    std::vector<uint8_t> expected_be = {
        0x00, 0x00, 0x00, 0xFF, 
        0xFF, 0xFF, 0xFF, 0xFF, 
        0xFF, 0xFF, 0xFF, 0xFF, 
        0xFF, 0xFF, 0xFF, 0xFF, 
        0xFF, 0xFF, 0xFF, 0xFF, 
    };
    
    resp_channel_->send(resp_payload, resp_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x0037);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 4);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::INCR);
    EXPECT_EQ(item.cmd, cmd_type_t::READ);
    for (int i = 0; i < item.data.size(); ++i) {
        EXPECT_EQ(item.data[i]&item.be[i], expected_data[i]&expected_be[i]);
    }

    delete [] resp_ptr;
}

TEST_F(AxiSlaveBridgeTest, R_INCR_UNALIGN_NARROW) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    trans.cmd = umi_axi::READ;
    trans.id = 0x123;
    trans.addr = 0x0037;
    trans.size = size_type_t::BYTE_4;
    trans.len = 4;  
    trans.burst = umi_axi::burst_type_t::INCR;
    trans.data_width = umi_axi::bus_width_t::BIT_64;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);
  
    fake_slave_->seq.push_back(&trans);
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    auto recv_data = req_channel_->recv();
    EXPECT_EQ(recv_data.empty(), false);
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload));
    TlmPayload* req_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    EXPECT_EQ(req_payload->data, nullptr);
    EXPECT_EQ(req_payload->command, tlm::TLM_READ_COMMAND);
    EXPECT_EQ(req_payload->data_length, data_size);
    EXPECT_EQ(req_payload->streaming_width, 0x0);
    EXPECT_EQ(req_payload->address, utils::GetAlignedAddress(trans.addr, trans.size));

    uint32_t resp_size = sizeof(TlmPayload) + data_size;
    uint8_t* resp_ptr = new uint8_t [resp_size];
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(resp_ptr);
    memcpy(resp_payload, req_payload, sizeof(TlmPayload));
    resp_payload->data = resp_ptr + sizeof(TlmPayload);
    for(int i = 0; i < data_size; ++i){
        resp_payload->data[i] = static_cast<uint8_t>(i);
    }
    resp_payload->response = tlm::TLM_OK_RESPONSE;

    std::vector<uint8_t> expected_data = {
        0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x02, 0x03,
        0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b,
        0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b,
        0x0c, 0x0d, 0x0e, 0x0f, 0x10, 0x11, 0x12, 0x13,
        0x0c, 0x0d, 0x0e, 0x0f, 0x10, 0x11, 0x12, 0x13
    };

    std::vector<uint8_t> expected_be = {
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF,
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF
    };
    
    resp_channel_->send(resp_payload, resp_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x0037);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 4);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::INCR);
    EXPECT_EQ(item.cmd, cmd_type_t::READ);
    for (int i = 0; i < item.data.size(); ++i) {
        EXPECT_EQ(item.data[i]&item.be[i], expected_data[i]&expected_be[i]);
    }

    delete [] resp_ptr;
}

TEST_F(AxiSlaveBridgeTest, R_INCR_MULTI) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    std::vector<axi_transaction_item> transactions;
    const int MULTI_SIZE = 5;
    const int DATA_SIZE = 4096;
    // 4KB transfer
    for (int i = 0; i < MULTI_SIZE; ++i) {
        axi_transaction_item trans;
        trans.cmd = umi_axi::READ;
        trans.id = i;
        trans.addr = i * 0x1000;
        trans.size = umi_axi::BYTE_32;
        trans.len = 127;
        trans.burst = umi_axi::INCR;
        trans.data_width = umi_axi::bus_width_t::BIT_256;
        transactions.push_back(trans);
    }
    
    
    for (size_t i = 0; i < transactions.size(); ++i) {
        auto& trans = transactions[i];
        fake_slave_->seq.push_back(&trans);
    }
    std::cout << "Send axi item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "Send axi item notify end" << std::endl;
    for (size_t i = 0; i < MULTI_SIZE; ++i) {
        std::cout << "Receive burst transaction " << i << " of " << transactions.size() << std::endl;
        // Verify received data
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
        auto recv_data = req_channel_->recv();
        EXPECT_FALSE(recv_data.empty());
        EXPECT_EQ(recv_data.size(), sizeof(TlmPayload));

        TlmPayload* req_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
        std::cout << "req_payload id: " << req_payload->id << std::endl;
        std::cout << "req_payload address: " << req_payload->address << std::endl;
        EXPECT_EQ(req_payload->data, nullptr);
        EXPECT_EQ(req_payload->command, tlm::TLM_READ_COMMAND);
        EXPECT_EQ(req_payload->data_length, DATA_SIZE);
        EXPECT_EQ(req_payload->streaming_width, 0x0);
        EXPECT_EQ(req_payload->address, i * 0x1000);

        uint32_t resp_size = sizeof(TlmPayload) + DATA_SIZE;
        uint8_t* resp_ptr = new uint8_t [resp_size];
        TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(resp_ptr);
        memcpy(resp_payload, req_payload, sizeof(TlmPayload));
        resp_payload->data = resp_ptr + sizeof(TlmPayload);
        for(int j = 0; j < DATA_SIZE; ++j){
            resp_payload->data[j] = j % 256;
        }
        resp_payload->response = tlm::TLM_OK_RESPONSE;
        
        resp_channel_->send(resp_payload, resp_size);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
     

        delete [] resp_ptr;
    }

    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), MULTI_SIZE);
    for (int i = 0; i < xtor_slave_proxy_->response_lists_.size(); ++i) {
        axi_transaction_item& item = xtor_slave_proxy_->response_lists_[i];
        EXPECT_EQ(item.addr, i * 0x1000);
        EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, 127);
        EXPECT_EQ(item.size, size_type_t::BYTE_32);
        EXPECT_EQ(item.burst, burst_type_t::INCR);
        EXPECT_EQ(item.cmd, cmd_type_t::READ);
    }
}

TEST_F(AxiSlaveBridgeTest, R_FIXED) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    uint32_t data_size = 4096;
    trans.cmd = umi_axi::READ;
    trans.id = 0x123;
    trans.addr = 0x1000;
    trans.size = size_type_t::BYTE_32;
    trans.len = 127;  
    trans.data_width = umi_axi::bus_width_t::BIT_256;
    trans.burst = umi_axi::burst_type_t::FIXED;
  
    fake_slave_->seq.push_back(&trans);
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    auto recv_data = req_channel_->recv();
    EXPECT_EQ(recv_data.empty(), false);
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload));
    TlmPayload* req_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    EXPECT_EQ(req_payload->data, nullptr);
    EXPECT_EQ(req_payload->command, tlm::TLM_READ_COMMAND);
    EXPECT_EQ(req_payload->data_length, data_size);
    EXPECT_EQ(req_payload->streaming_width, 1 << trans.size);
    EXPECT_EQ(req_payload->address, utils::GetAlignedAddress(trans.addr, trans.size));

    uint32_t resp_size = sizeof(TlmPayload) + data_size;
    uint8_t* resp_ptr = new uint8_t [resp_size];
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(resp_ptr);
    memcpy(resp_payload, req_payload, sizeof(TlmPayload));
    resp_payload->data = resp_ptr + sizeof(TlmPayload);
    for(int i = 0; i < data_size; ++i){
        resp_payload->data[i] = i % 256;
    }
    resp_payload->response = tlm::TLM_OK_RESPONSE;
    
    resp_channel_->send(resp_payload, resp_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x1000);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 127);
    EXPECT_EQ(item.size, size_type_t::BYTE_32);
    EXPECT_EQ(item.burst, burst_type_t::FIXED);
    EXPECT_EQ(item.cmd, cmd_type_t::READ);

    delete [] resp_ptr;
}

TEST_F(AxiSlaveBridgeTest, R_FIXED_UNALIGN) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    trans.cmd = umi_axi::READ;
    trans.id = 0x123;
    trans.addr = 0x0037;
    trans.size = size_type_t::BYTE_4;
    trans.len = 4;  
    trans.burst = umi_axi::burst_type_t::FIXED;
    trans.data_width = umi_axi::bus_width_t::BIT_32;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);
  
    fake_slave_->seq.push_back(&trans);
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    auto recv_data = req_channel_->recv();
    EXPECT_EQ(recv_data.empty(), false);
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload));
    TlmPayload* req_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    EXPECT_EQ(req_payload->data, nullptr);
    EXPECT_EQ(req_payload->command, tlm::TLM_READ_COMMAND);
    EXPECT_EQ(req_payload->data_length, data_size);
    EXPECT_EQ(req_payload->streaming_width, 1 << trans.size);
    EXPECT_EQ(req_payload->address, utils::GetAlignedAddress(trans.addr, trans.size));

    uint32_t resp_size = sizeof(TlmPayload) + data_size;
    uint8_t* resp_ptr = new uint8_t [resp_size];
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(resp_ptr);
    memcpy(resp_payload, req_payload, sizeof(TlmPayload));
    resp_payload->data = resp_ptr + sizeof(TlmPayload);
    for(int i = 0; i < data_size; ++i){
        resp_payload->data[i] = static_cast<uint8_t>(i);
    }
    resp_payload->response = tlm::TLM_OK_RESPONSE;

    std::vector<uint8_t> expected_data = {
        0x00, 0x01, 0x02, 0x03,
        0x04, 0x05, 0x06, 0x07, 
        0x08, 0x09, 0x0a, 0x0b,
        0x0c, 0x0d, 0x0e, 0x0f, 
        0x10, 0x11, 0x12, 0x13,
    };

    std::vector<uint8_t> expected_be = {
        0x00, 0x00, 0x00, 0xFF, 
        0x00, 0x00, 0x00, 0xFF, 
        0x00, 0x00, 0x00, 0xFF, 
        0x00, 0x00, 0x00, 0xFF, 
        0x00, 0x00, 0x00, 0xFF, 
    };
    
    resp_channel_->send(resp_payload, resp_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x0037);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 4);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::FIXED);
    EXPECT_EQ(item.cmd, cmd_type_t::READ);
    for (int i = 0; i < item.data.size(); ++i) {
        EXPECT_EQ(item.data[i]&item.be[i], expected_data[i]&expected_be[i]);
    }

    delete [] resp_ptr;
}

TEST_F(AxiSlaveBridgeTest, R_FIXED_NARROW) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    trans.cmd = umi_axi::READ;
    trans.id = 0x123;
    trans.addr = 0x0004;
    trans.size = size_type_t::BYTE_4;
    trans.len = 4;  
    trans.burst = umi_axi::burst_type_t::FIXED;
    trans.data_width = umi_axi::bus_width_t::BIT_64;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);
  
    fake_slave_->seq.push_back(&trans);
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    auto recv_data = req_channel_->recv();
    EXPECT_EQ(recv_data.empty(), false);
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload));
    TlmPayload* req_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    EXPECT_EQ(req_payload->data, nullptr);
    EXPECT_EQ(req_payload->command, tlm::TLM_READ_COMMAND);
    EXPECT_EQ(req_payload->data_length, data_size);
    EXPECT_EQ(req_payload->streaming_width, 1 << trans.size);
    EXPECT_EQ(req_payload->address, utils::GetAlignedAddress(trans.addr, trans.size));

    uint32_t resp_size = sizeof(TlmPayload) + data_size;
    uint8_t* resp_ptr = new uint8_t [resp_size];
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(resp_ptr);
    memcpy(resp_payload, req_payload, sizeof(TlmPayload));
    resp_payload->data = resp_ptr + sizeof(TlmPayload);
    for(int i = 0; i < data_size; ++i){
        resp_payload->data[i] = static_cast<uint8_t>(i);
    }
    resp_payload->response = tlm::TLM_OK_RESPONSE;

    std::vector<uint8_t> expected_data = {
        0x00, 0x01, 0x02, 0x03, 0x00, 0x01, 0x02, 0x03,
        0x04, 0x05, 0x06, 0x07, 0x04, 0x05, 0x06, 0x07,
        0x08, 0x09, 0x0a, 0x0b, 0x08, 0x09, 0x0a, 0x0b,
        0x0c, 0x0d, 0x0e, 0x0f, 0x0c, 0x0d, 0x0e, 0x0f, 
        0x10, 0x11, 0x12, 0x13, 0x10, 0x11, 0x12, 0x13,
    };

    std::vector<uint8_t> expected_be = {
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
    };
    
    resp_channel_->send(resp_payload, resp_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x0004);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 4);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::FIXED);
    EXPECT_EQ(item.cmd, cmd_type_t::READ);
    for (int i = 0; i < item.data.size(); ++i) {
        std::cout << "i: " << i << std::endl;
        std::cout << "resp payload data: " << resp_payload->data[i] << std::endl;
        EXPECT_EQ(item.data[i]&item.be[i], expected_data[i]&expected_be[i]);
    }

    delete [] resp_ptr;
}

TEST_F(AxiSlaveBridgeTest, R_FIXED_UNALIGN_NARROW) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    trans.cmd = umi_axi::READ;
    trans.id = 0x123;
    trans.addr = 0x0031;
    trans.size = size_type_t::BYTE_2;
    trans.len = 2;  
    trans.burst = umi_axi::burst_type_t::FIXED;
    trans.data_width = umi_axi::bus_width_t::BIT_32;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);
  
    fake_slave_->seq.push_back(&trans);
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    auto recv_data = req_channel_->recv();
    EXPECT_EQ(recv_data.empty(), false);
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload));
    TlmPayload* req_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    EXPECT_EQ(req_payload->data, nullptr);
    EXPECT_EQ(req_payload->command, tlm::TLM_READ_COMMAND);
    EXPECT_EQ(req_payload->data_length, data_size);
    EXPECT_EQ(req_payload->streaming_width, 1 << trans.size);
    EXPECT_EQ(req_payload->address, utils::GetAlignedAddress(trans.addr, trans.size));

    uint32_t resp_size = sizeof(TlmPayload) + data_size;
    uint8_t* resp_ptr = new uint8_t [resp_size];
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(resp_ptr);
    memcpy(resp_payload, req_payload, sizeof(TlmPayload));
    resp_payload->data = resp_ptr + sizeof(TlmPayload);
    for(int i = 0; i < data_size; ++i){
        resp_payload->data[i] = static_cast<uint8_t>(i);
    }
    resp_payload->response = tlm::TLM_OK_RESPONSE;

    std::vector<uint8_t> expected_data = {
        0x00, 0x01, 0x01, 0x02,
        0x02, 0x03, 0x02, 0x00,
        0x04, 0x05, 0x00, 0x00
    };

    std::vector<uint8_t> expected_be = {
        0x00, 0xff, 0x00, 0x00,
        0x00, 0xff, 0x00, 0x00,
        0x00, 0xff, 0x00, 0x00,
    };
    
    resp_channel_->send(resp_payload, resp_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x0031);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 2);
    EXPECT_EQ(item.size, size_type_t::BYTE_2);
    EXPECT_EQ(item.burst, burst_type_t::FIXED);
    EXPECT_EQ(item.cmd, cmd_type_t::READ);
    for (int i = 0; i < item.data.size(); ++i) {
        EXPECT_EQ(item.data[i]&item.be[i], expected_data[i]&expected_be[i]);
    }

    delete [] resp_ptr;
}

TEST_F(AxiSlaveBridgeTest, R_FIXED_MULTI) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    std::vector<axi_transaction_item> transactions;
    const int MULTI_SIZE = 5;
    const int DATA_SIZE = 4096;
    // 4KB transfer
    for (int i = 0; i < MULTI_SIZE; ++i) {
        axi_transaction_item trans;
        trans.cmd = umi_axi::READ;
        trans.id = i;
        trans.addr = i * 0x1000;
        trans.size = umi_axi::BYTE_32;
        trans.len = 127;
        trans.burst = umi_axi::FIXED;
        transactions.push_back(trans);
    }
    
    std::vector<uint32_t> expected_sizes(MULTI_SIZE, 4096);
    for (size_t i = 0; i < transactions.size(); ++i) {
        auto& trans = transactions[i];
        fake_slave_->seq.push_back(&trans);
    }
    std::cout << "Send axi item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "Send axi item notify end" << std::endl;
    for (size_t i = 0; i < MULTI_SIZE; ++i) {
        std::cout << "Receive burst transaction " << i << " of " << transactions.size() << std::endl;
        // Verify received data
        auto recv_data = req_channel_->recv();
        EXPECT_FALSE(recv_data.empty());
        EXPECT_EQ(recv_data.size(), sizeof(TlmPayload));

        TlmPayload* req_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
        EXPECT_EQ(req_payload->data, nullptr);
        EXPECT_EQ(req_payload->command, tlm::TLM_READ_COMMAND);
        EXPECT_EQ(req_payload->data_length, DATA_SIZE);
        EXPECT_EQ(req_payload->streaming_width, 32);
        EXPECT_EQ(req_payload->address, i * 0x1000);

        uint32_t resp_size = sizeof(TlmPayload) + DATA_SIZE;
        uint8_t* resp_ptr = new uint8_t [resp_size];
        TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(resp_ptr);
        memcpy(resp_payload, req_payload, sizeof(TlmPayload));
        resp_payload->data = resp_ptr + sizeof(TlmPayload);
        for(int j = 0; j < DATA_SIZE; ++j){
            resp_payload->data[j] = j % 256;
        }
        resp_payload->response = tlm::TLM_OK_RESPONSE;
        
        resp_channel_->send(resp_payload, resp_size);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
     

        delete [] resp_ptr;
    }

    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), MULTI_SIZE);
    for (int i = 0; i < xtor_slave_proxy_->response_lists_.size(); ++i) {
        axi_transaction_item& item = xtor_slave_proxy_->response_lists_[i];
        EXPECT_EQ(item.addr, i * 0x1000);
        EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, 127);
        EXPECT_EQ(item.size, size_type_t::BYTE_32);
        EXPECT_EQ(item.burst, burst_type_t::FIXED);
        EXPECT_EQ(item.cmd, cmd_type_t::READ);
    }
}

TEST_F(AxiSlaveBridgeTest, R_WRAP) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    trans.cmd = umi_axi::READ;
    trans.id = 0x123;
    trans.addr = 0x034;
    trans.size = size_type_t::BYTE_4;
    trans.len = 5;  
    trans.data_width = umi_axi::bus_width_t::BIT_32;
    trans.burst = umi_axi::burst_type_t::WRAP;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);
  
    fake_slave_->seq.push_back(&trans);
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    auto recv_data = req_channel_->recv();
    EXPECT_EQ(recv_data.empty(), false);
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload));
    TlmPayload* req_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    EXPECT_EQ(req_payload->data, nullptr);
    EXPECT_EQ(req_payload->command, tlm::TLM_READ_COMMAND);
    EXPECT_EQ(req_payload->data_length, data_size);
    EXPECT_EQ(req_payload->streaming_width, 0x0);
    EXPECT_EQ(req_payload->address, utils::GetLowWrapBoundary(trans.addr, trans.size, trans.len));

    uint32_t resp_size = sizeof(TlmPayload) + data_size;
    uint8_t* resp_ptr = new uint8_t [resp_size];
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(resp_ptr);
    memcpy(resp_payload, req_payload, sizeof(TlmPayload));
    resp_payload->data = resp_ptr + sizeof(TlmPayload);
    for(int i = 0; i < data_size; ++i){
        resp_payload->data[i] = i % 256;
    }
    resp_payload->response = tlm::TLM_OK_RESPONSE;
    
    resp_channel_->send(resp_payload, resp_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x0034);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 5);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::WRAP);
    EXPECT_EQ(item.cmd, cmd_type_t::READ);

    delete [] resp_ptr;
}

TEST_F(AxiSlaveBridgeTest, R_WRAP_NARROW) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    trans.cmd = umi_axi::READ;
    trans.id = 0x123;
    trans.addr = 0x0034;
    trans.size = size_type_t::BYTE_4;
    trans.len = 5;  
    trans.burst = umi_axi::burst_type_t::WRAP;
    trans.data_width = umi_axi::bus_width_t::BIT_64;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);
  
    fake_slave_->seq.push_back(&trans);
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    auto recv_data = req_channel_->recv();
    EXPECT_EQ(recv_data.empty(), false);
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload));
    TlmPayload* req_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    EXPECT_EQ(req_payload->data, nullptr);
    EXPECT_EQ(req_payload->command, tlm::TLM_READ_COMMAND);
    EXPECT_EQ(req_payload->data_length, data_size);
    EXPECT_EQ(req_payload->streaming_width, 0x0);
    EXPECT_EQ(req_payload->address, utils::GetLowWrapBoundary(trans.addr, trans.size, trans.len));
    uint32_t resp_size = sizeof(TlmPayload) + data_size;
    uint8_t* resp_ptr = new uint8_t [resp_size];
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(resp_ptr);
    memcpy(resp_payload, req_payload, sizeof(TlmPayload));
    resp_payload->data = resp_ptr + sizeof(TlmPayload);
    for(int i = 0; i < data_size; ++i){
        resp_payload->data[i] = static_cast<uint8_t>(i);
    }
    resp_payload->response = tlm::TLM_OK_RESPONSE;

    std::vector<uint8_t> expected_data = {
        0x00, 0x00, 0x00, 0x00, 0x04, 0x05, 0x06, 0x07,
        0x08, 0x09, 0x0a, 0x0b, 0x08, 0x09, 0x0a, 0x0b,
        0x04, 0x05, 0x06, 0x07, 0x0c, 0x0d, 0x0e, 0x0f,
        0x10, 0x11, 0x12, 0x13, 0x10, 0x11, 0x12, 0x13,
        0x0c, 0x0d, 0x0e, 0x0f, 0x14, 0x15, 0x16, 0x17,
        0x00, 0x01, 0x02, 0x03, 0x00, 0x00, 0x00, 0x00
    };

    std::vector<uint8_t> expected_be = {
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00
    };
    
    resp_channel_->send(resp_payload, resp_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x0034);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 5);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::WRAP);
    EXPECT_EQ(item.cmd, cmd_type_t::READ);
    for (int i = 0; i < item.data.size(); ++i) {
        EXPECT_EQ(item.data[i]&item.be[i], expected_data[i]&expected_be[i]);
    }

    delete [] resp_ptr;
}

TEST_F(AxiSlaveBridgeTest, R_WRAP_MULTI) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    std::vector<axi_transaction_item> transactions;
    const int MULTI_SIZE = 5;
    const int DATA_SIZE = 24;
    // 4KB transfer
    for (int i = 0; i < MULTI_SIZE; ++i) {
        axi_transaction_item trans;
        trans.cmd = umi_axi::READ;
        trans.id = i;
        trans.addr = i * 0x0034;
        trans.size = umi_axi::BYTE_4;
        trans.len = 5;
        trans.burst = umi_axi::WRAP;
        trans.data_width = umi_axi::bus_width_t::BIT_64;
        transactions.push_back(trans);
    }
    
    for (size_t i = 0; i < transactions.size(); ++i) {
        auto& trans = transactions[i];
        fake_slave_->seq.push_back(&trans);
    }
    std::cout << "Send axi item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "Send axi item notify end" << std::endl;
    for (size_t i = 0; i < MULTI_SIZE; ++i) {
        std::cout << "Receive burst transaction " << i << " of " << transactions.size() << std::endl;
        // Verify received data
        auto recv_data = req_channel_->recv();
        EXPECT_FALSE(recv_data.empty());
        EXPECT_EQ(recv_data.size(), sizeof(TlmPayload));

        TlmPayload* req_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
        EXPECT_EQ(req_payload->data, nullptr);
        EXPECT_EQ(req_payload->command, tlm::TLM_READ_COMMAND);
        EXPECT_EQ(req_payload->data_length, DATA_SIZE);
        EXPECT_EQ(req_payload->streaming_width, 0x0);
        EXPECT_EQ(req_payload->address, utils::GetLowWrapBoundary(i * 0x0034, umi_axi::BYTE_4, 5));

        uint32_t resp_size = sizeof(TlmPayload) + DATA_SIZE;
        uint8_t* resp_ptr = new uint8_t [resp_size];
        TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(resp_ptr);
        memcpy(resp_payload, req_payload, sizeof(TlmPayload));
        resp_payload->data = resp_ptr + sizeof(TlmPayload);
        for(int j = 0; j < DATA_SIZE; ++j){
            resp_payload->data[j] = j % 256;
        }
        resp_payload->response = tlm::TLM_OK_RESPONSE;
        
        resp_channel_->send(resp_payload, resp_size);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
     

        delete [] resp_ptr;
    }

    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), MULTI_SIZE);
    for (int i = 0; i < xtor_slave_proxy_->response_lists_.size(); ++i) {
        axi_transaction_item& item = xtor_slave_proxy_->response_lists_[i];
        EXPECT_EQ(item.addr, i * 0x0034);
        EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, 5);
        EXPECT_EQ(item.size, size_type_t::BYTE_4);
        EXPECT_EQ(item.burst, burst_type_t::WRAP);
        EXPECT_EQ(item.cmd, cmd_type_t::READ);
    }
}

TEST_F(AxiSlaveBridgeTest, R_INCR_4K) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    trans.cmd = umi_axi::READ;
    trans.id = 0x123;
    trans.addr = 0xff0;
    trans.size = umi_axi::BYTE_32;
    trans.len = 0x11;
    trans.burst = umi_axi::INCR;
    trans.data_width = umi_axi::bus_width_t::BIT_256;
    EXPECT_THROW(axi_slave_bridge_->HandleAxiTransaction(&trans), std::invalid_argument);
}

TEST_F(AxiSlaveBridgeTest, R_FIXED_4K) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    trans.cmd = umi_axi::READ;
    trans.id = 0x123;
    trans.addr = 0xff0;
    trans.size = umi_axi::BYTE_32;
    trans.len = 127;
    trans.burst = umi_axi::FIXED;
    trans.data_width = umi_axi::bus_width_t::BIT_256;
    EXPECT_THROW(axi_slave_bridge_->HandleAxiTransaction(&trans), std::invalid_argument);
}

TEST_F(AxiSlaveBridgeTest, R_WRAP_4K) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    trans.cmd = umi_axi::READ;
    trans.id = 0x123;
    trans.addr = 0xff0;
    trans.size = umi_axi::BYTE_4;
    trans.len = 5;
    trans.burst = umi_axi::WRAP;
    trans.data_width = umi_axi::bus_width_t::BIT_32;
    EXPECT_THROW(axi_slave_bridge_->HandleAxiTransaction(&trans), std::invalid_argument);
}

TEST_F(AxiSlaveBridgeTest, W_INCR) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    uint32_t data_size = 4096;
    trans.data = std::vector<uint8_t>(data_size, 0);
    trans.be = std::vector<uint8_t>(data_size, 0xFF);
    trans.cmd = umi_axi::WRITE;
    trans.id = 0x123;
    trans.addr = 0x1000;
    trans.size = size_type_t::BYTE_32;
    trans.len = 127;  
    trans.burst = umi_axi::burst_type_t::INCR;
    trans.data_width = umi_axi::bus_width_t::BIT_256;
    for (int i = 0; i < data_size; ++i) {
        trans.data[i] = i % 256;
    }  
  
    fake_slave_->seq.push_back(&trans);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    auto recv_data = req_channel_->recv();
    EXPECT_EQ(recv_data.empty(), false);
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload) + data_size*2);
    TlmPayload* req_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    req_payload->data = reinterpret_cast<unsigned char*>(req_payload) + sizeof(TlmPayload);
    EXPECT_EQ(req_payload->command, tlm::TLM_WRITE_COMMAND);
    EXPECT_EQ(req_payload->data_length, data_size);
    EXPECT_EQ(req_payload->streaming_width, 0x0);
    EXPECT_EQ(req_payload->address, utils::GetAlignedAddress(trans.addr, trans.size));
    for (int i = 0; i < data_size; ++i) {
        EXPECT_EQ(req_payload->data[i], i % 256);
    }

    uint32_t resp_size = sizeof(TlmPayload);
    uint8_t* resp_ptr = new uint8_t [resp_size];
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(resp_ptr);
    memcpy(resp_payload, req_payload, sizeof(TlmPayload));
    resp_payload->data = nullptr;
    resp_payload->response = tlm::TLM_OK_RESPONSE;
    
    resp_channel_->send(resp_payload, resp_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x1000);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 127);
    EXPECT_EQ(item.size, size_type_t::BYTE_32);
    EXPECT_EQ(item.burst, burst_type_t::INCR);
    EXPECT_EQ(item.cmd, cmd_type_t::WRITE);

    delete [] resp_ptr;
}

TEST_F(AxiSlaveBridgeTest, W_INCR_NO_BE) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    trans.cmd = umi_axi::WRITE;
    trans.id = 0x123;
    trans.addr = 0x1000;
    trans.size = size_type_t::BYTE_32;
    trans.len = 127;  
    trans.burst = umi_axi::burst_type_t::INCR;
    trans.data_width = umi_axi::bus_width_t::BIT_256;
    uint32_t data_size = (trans.len + 1 ) * (1 << trans.size);
    trans.data = std::vector<uint8_t>(data_size, 0);
    for (int i = 0; i < data_size; ++i) {
        trans.data[i] = i % 256;
    }  
  
    fake_slave_->seq.push_back(&trans);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    auto recv_data = req_channel_->recv();
    EXPECT_EQ(recv_data.empty(), false);
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload) + data_size);
    TlmPayload* req_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    req_payload->data = reinterpret_cast<unsigned char*>(req_payload) + sizeof(TlmPayload);
    EXPECT_EQ(req_payload->command, tlm::TLM_WRITE_COMMAND);
    EXPECT_EQ(req_payload->data_length, data_size);
    EXPECT_EQ(req_payload->byte_enable_length, 0);
    EXPECT_EQ(req_payload->address, utils::GetAlignedAddress(trans.addr, trans.size));
    for (int i = 0; i < data_size; ++i) {
        EXPECT_EQ(req_payload->data[i], i % 256);
    }

    uint32_t resp_size = sizeof(TlmPayload);
    uint8_t* resp_ptr = new uint8_t [resp_size];
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(resp_ptr);
    memcpy(resp_payload, req_payload, sizeof(TlmPayload));
    resp_payload->data = nullptr;
    resp_payload->response = tlm::TLM_OK_RESPONSE;
    
    resp_channel_->send(resp_payload, resp_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x1000);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 127);
    EXPECT_EQ(item.size, size_type_t::BYTE_32);
    EXPECT_EQ(item.burst, burst_type_t::INCR);
    EXPECT_EQ(item.cmd, cmd_type_t::WRITE);

    delete [] resp_ptr;
}

TEST_F(AxiSlaveBridgeTest, W_INCR_NARROW) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    initialize_write_transaction(trans, 256);
    trans.cmd = umi_axi::WRITE;
    trans.id = 0x123;
    trans.addr = 0x1000;
    trans.size = size_type_t::BYTE_4;
    trans.len = 4;  
    trans.burst = umi_axi::burst_type_t::INCR;
    trans.data_width = umi_axi::bus_width_t::BIT_64;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);
  
    fake_slave_->seq.push_back(&trans);
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    auto recv_data = req_channel_->recv();
  
    std::vector<uint8_t> expected_data = {
        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
        0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F,
        0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
        0x18, 0x19, 0x1A, 0x1B, 0x1C, 0x1D, 0x1E, 0x1F,
        0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27
    };

    std::vector<uint8_t> expected_be = {
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
    };

    std::vector<uint8_t> expected_result = {
        0x00, 0x01, 0x02, 0x03,
        0x0c, 0x0d, 0x0e, 0x0f, 
        0x10, 0x11, 0x12, 0x13,
        0x1c, 0x1d, 0x1e, 0x1f,
        0x20, 0x21, 0x22, 0x23,
    };

    EXPECT_EQ(recv_data.empty(), false);
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload) + data_size*2);
    TlmPayload* req_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    req_payload->data = reinterpret_cast<unsigned char*>(req_payload) + sizeof(TlmPayload);
    EXPECT_EQ(req_payload->command, tlm::TLM_WRITE_COMMAND);
    EXPECT_EQ(req_payload->data_length, data_size);
    EXPECT_EQ(req_payload->streaming_width, 0x0);
    EXPECT_EQ(req_payload->address, utils::GetAlignedAddress(trans.addr, trans.size));
    for (int i = 0; i < req_payload->data_length; ++i) {
        EXPECT_EQ(req_payload->data[i] , expected_result[i]);
    }

    uint32_t resp_size = sizeof(TlmPayload);
    uint8_t* resp_ptr = new uint8_t [resp_size];
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(resp_ptr);
    memcpy(resp_payload, req_payload, sizeof(TlmPayload));
    resp_payload->data = nullptr;
    resp_payload->response = tlm::TLM_OK_RESPONSE;

    resp_channel_->send(resp_payload, resp_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x1000);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 4);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::INCR);
    EXPECT_EQ(item.cmd, cmd_type_t::WRITE);

    delete [] resp_ptr;
}

TEST_F(AxiSlaveBridgeTest, W_INCR_UNALIGN) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    initialize_write_transaction(trans, 256);
    trans.cmd = umi_axi::WRITE;
    trans.id = 0x123;
    trans.addr = 0x1001;
    trans.size = size_type_t::BYTE_4;
    trans.len = 4;  
    trans.burst = umi_axi::burst_type_t::INCR;
    trans.data_width = umi_axi::bus_width_t::BIT_32;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);
  
    fake_slave_->seq.push_back(&trans);
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    auto recv_data = req_channel_->recv();

    std::vector<uint8_t> expected_result = {
        0x00, 0x01, 0x02, 0x03,
        0x04, 0x05, 0x06, 0x07,
        0x08, 0x09, 0x0A, 0x0B,
        0x0C, 0x0D, 0x0E, 0x0F,
        0x10, 0x11, 0x12, 0x13,
    };

    EXPECT_EQ(recv_data.empty(), false);
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload) + data_size*2);
    TlmPayload* req_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    req_payload->data = reinterpret_cast<unsigned char*>(req_payload) + sizeof(TlmPayload);
    EXPECT_EQ(req_payload->command, tlm::TLM_WRITE_COMMAND);
    EXPECT_EQ(req_payload->data_length, data_size);
    EXPECT_EQ(req_payload->streaming_width, 0x0);
    EXPECT_EQ(req_payload->address, utils::GetAlignedAddress(trans.addr, trans.size));
    for (int i = 0; i < req_payload->data_length; ++i) {
        EXPECT_EQ(req_payload->data[i] , expected_result[i]);
    }

    uint32_t resp_size = sizeof(TlmPayload);
    uint8_t* resp_ptr = new uint8_t [resp_size];
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(resp_ptr);
    memcpy(resp_payload, req_payload, sizeof(TlmPayload));
    resp_payload->data = nullptr;
    resp_payload->response = tlm::TLM_OK_RESPONSE;

    resp_channel_->send(resp_payload, resp_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x1001);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 4);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::INCR);
    EXPECT_EQ(item.cmd, cmd_type_t::WRITE);

    delete [] resp_ptr;
}

TEST_F(AxiSlaveBridgeTest, W_INCR_UNALIGN_NARROW) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    initialize_write_transaction(trans, 256);
    trans.cmd = umi_axi::WRITE;
    trans.id = 0x123;
    trans.addr = 0x1003;
    trans.size = size_type_t::BYTE_4;
    trans.len = 4;  
    trans.burst = umi_axi::burst_type_t::INCR;
    trans.data_width = umi_axi::bus_width_t::BIT_64;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);
  
    fake_slave_->seq.push_back(&trans);
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    auto recv_data = req_channel_->recv();
  
    std::vector<uint8_t> expected_data = {
        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
        0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F,
        0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
        0x18, 0x19, 0x1A, 0x1B, 0x1C, 0x1D, 0x1E, 0x1F,
        0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27
    };

    std::vector<uint8_t> expected_be = {
        0x00, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
    };

    std::vector<uint8_t> expected_result = {
        0x00, 0x00, 0x00, 0x03,
        0x0c, 0x0d, 0x0e, 0x0f, 
        0x10, 0x11, 0x12, 0x13,
        0x1c, 0x1d, 0x1e, 0x1f,
        0x20, 0x21, 0x22, 0x23,
    };

    EXPECT_EQ(recv_data.empty(), false);
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload) + data_size*2);
    TlmPayload* req_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    req_payload->data = reinterpret_cast<unsigned char*>(req_payload) + sizeof(TlmPayload);
    EXPECT_EQ(req_payload->command, tlm::TLM_WRITE_COMMAND);
    EXPECT_EQ(req_payload->data_length, data_size);
    EXPECT_EQ(req_payload->streaming_width, 0x0);
    EXPECT_EQ(req_payload->address, utils::GetAlignedAddress(trans.addr, trans.size));
    for (int i = 0; i < req_payload->data_length; ++i) {
        EXPECT_EQ(req_payload->data[i] , expected_result[i]);
    }

    uint32_t resp_size = sizeof(TlmPayload);
    uint8_t* resp_ptr = new uint8_t [resp_size];
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(resp_ptr);
    memcpy(resp_payload, req_payload, sizeof(TlmPayload));
    resp_payload->data = nullptr;
    resp_payload->response = tlm::TLM_OK_RESPONSE;

    resp_channel_->send(resp_payload, resp_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x1003);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 4);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::INCR);
    EXPECT_EQ(item.cmd, cmd_type_t::WRITE);

    delete [] resp_ptr;
}

TEST_F(AxiSlaveBridgeTest, W_INCR_MULTI) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    std::vector<axi_transaction_item> transactions;
    const int MULTI_SIZE = 2;
    int DATA_SIZE = 0;
    // 4KB transfer
    for (int i = 0; i < MULTI_SIZE; ++i) {
        axi_transaction_item trans;
        trans.cmd = umi_axi::WRITE;
        trans.id = i;
        trans.addr = i * 0x1000;
        trans.size = umi_axi::BYTE_32;
        trans.len = 127;
        trans.burst = umi_axi::INCR;
        DATA_SIZE = (trans.len + 1) * (1 << trans.size);
        trans.data = std::vector<uint8_t>(DATA_SIZE, 0);
        trans.be = std::vector<uint8_t>(DATA_SIZE, 0xFF);
        trans.data_width = umi_axi::bus_width_t::BIT_256;
        for (int j = 0; j < DATA_SIZE; ++j) {
            trans.data[j] = (i +j) % 256;
        }  
        transactions.push_back(trans);
    }
    
    
    for (size_t i = 0; i < transactions.size(); ++i) {
        auto& trans = transactions[i];
        fake_slave_->seq.push_back(&trans);
    }
    std::cout << "Send axi item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "Send axi item notify end" << std::endl;
    for (size_t i = 0; i < MULTI_SIZE; ++i) {
        std::cout << "Receive burst transaction " << i << " of " << transactions.size() << std::endl;
        // Verify received data
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
        auto recv_data = req_channel_->recv();
        EXPECT_FALSE(recv_data.empty());
        EXPECT_EQ(recv_data.size(), sizeof(TlmPayload) + DATA_SIZE * 2);

        TlmPayload* req_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
        req_payload->data = reinterpret_cast<uint8_t*>(req_payload) + sizeof(TlmPayload);
        EXPECT_NE(req_payload, nullptr);
        std::cout << "req_payload id: " << req_payload->id << std::endl;
        std::cout << "req_payload address: " << req_payload->address << std::endl;
        EXPECT_EQ(req_payload->command, tlm::TLM_WRITE_COMMAND);
        EXPECT_EQ(req_payload->data_length, DATA_SIZE);
        EXPECT_EQ(req_payload->streaming_width, 0x0);
        EXPECT_EQ(req_payload->address, i * 0x1000);
        for (int j = 0; j < DATA_SIZE; ++j) {
            EXPECT_EQ(req_payload->data[j], (i+j) % 256);
        }
        // req_channel_->clear();
        uint32_t resp_size = sizeof(TlmPayload);
        uint8_t* resp_ptr = new uint8_t [resp_size];
        TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(resp_ptr);
        memcpy(resp_payload, req_payload, sizeof(TlmPayload));
        resp_payload->data = nullptr;
        resp_payload->response = tlm::TLM_OK_RESPONSE;
        
        resp_channel_->send(resp_payload, resp_size);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
     

        delete [] resp_ptr;
    }

    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), MULTI_SIZE);
    for (int i = 0; i < xtor_slave_proxy_->response_lists_.size(); ++i) {
        axi_transaction_item& item = xtor_slave_proxy_->response_lists_[i];
        EXPECT_EQ(item.addr, i * 0x1000);
        EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, 127);
        EXPECT_EQ(item.size, size_type_t::BYTE_32);
        EXPECT_EQ(item.burst, burst_type_t::INCR);
        EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
    }
}

TEST_F(AxiSlaveBridgeTest, W_FIXED) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    uint32_t data_size = 4096;
    trans.data = std::vector<uint8_t>(data_size, 0);
    trans.be = std::vector<uint8_t>(data_size, 0xFF);
    trans.cmd = umi_axi::WRITE;
    trans.id = 0x123;
    trans.addr = 0x1000;
    trans.size = size_type_t::BYTE_32;
    trans.len = 127;  
    trans.burst = umi_axi::burst_type_t::FIXED;
    trans.data_width = umi_axi::bus_width_t::BIT_256;
    for (int i = 0; i < data_size; ++i) {
        trans.data[i] = i % 256;
    }  
  
    fake_slave_->seq.push_back(&trans);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    auto recv_data = req_channel_->recv();
    EXPECT_EQ(recv_data.empty(), false);
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload) + data_size*2);
    TlmPayload* req_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    req_payload->data = reinterpret_cast<unsigned char*>(req_payload) + sizeof(TlmPayload);
    EXPECT_EQ(req_payload->command, tlm::TLM_WRITE_COMMAND);
    EXPECT_EQ(req_payload->data_length, data_size);
    EXPECT_EQ(req_payload->streaming_width, 1 << trans.size);
    EXPECT_EQ(req_payload->address, utils::GetAlignedAddress(trans.addr, trans.size));
    for (int i = 0; i < data_size; ++i) {
        EXPECT_EQ(req_payload->data[i], i % 256);
    }

    uint32_t resp_size = sizeof(TlmPayload);
    uint8_t* resp_ptr = new uint8_t [resp_size];
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(resp_ptr);
    memcpy(resp_payload, req_payload, sizeof(TlmPayload));
    resp_payload->data = nullptr;
    resp_payload->response = tlm::TLM_OK_RESPONSE;
    
    resp_channel_->send(resp_payload, resp_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x1000);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 127);
    EXPECT_EQ(item.size, size_type_t::BYTE_32);
    EXPECT_EQ(item.burst, burst_type_t::FIXED);
    EXPECT_EQ(item.cmd, cmd_type_t::WRITE);

    delete [] resp_ptr;
}

TEST_F(AxiSlaveBridgeTest, W_FIXED_NARROW) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    initialize_write_transaction(trans, 256);
    trans.cmd = umi_axi::WRITE;
    trans.id = 0x123;
    trans.addr = 0x1000;
    trans.size = size_type_t::BYTE_4;
    trans.len = 4;  
    trans.burst = umi_axi::burst_type_t::FIXED;
    trans.data_width = umi_axi::bus_width_t::BIT_64;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);
  
    fake_slave_->seq.push_back(&trans);
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    auto recv_data = req_channel_->recv();
  
    std::vector<uint8_t> expected_data = {
        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
        0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F,
        0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
        0x18, 0x19, 0x1A, 0x1B, 0x1C, 0x1D, 0x1E, 0x1F,
        0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27
    };

    std::vector<uint8_t> expected_be = {
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
    };

    std::vector<uint8_t> expected_result = {
        0x00, 0x01, 0x02, 0x03,
        0x08, 0x09, 0x0A, 0x0B, 
        0x10, 0x11, 0x12, 0x13,
        0x18, 0x19, 0x1A, 0x1B,
        0x20, 0x21, 0x22, 0x23,
    };

    EXPECT_EQ(recv_data.empty(), false);
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload) + data_size*2);
    TlmPayload* req_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    req_payload->data = reinterpret_cast<unsigned char*>(req_payload) + sizeof(TlmPayload);
    EXPECT_EQ(req_payload->command, tlm::TLM_WRITE_COMMAND);
    EXPECT_EQ(req_payload->data_length, data_size);
    EXPECT_EQ(req_payload->streaming_width, 1 << trans.size);
    EXPECT_EQ(req_payload->address, utils::GetAlignedAddress(trans.addr, trans.size));
    for (int i = 0; i < req_payload->data_length; ++i) {
        EXPECT_EQ(req_payload->data[i] , expected_result[i]);
    }

    uint32_t resp_size = sizeof(TlmPayload);
    uint8_t* resp_ptr = new uint8_t [resp_size];
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(resp_ptr);
    memcpy(resp_payload, req_payload, sizeof(TlmPayload));
    resp_payload->data = nullptr;
    resp_payload->response = tlm::TLM_OK_RESPONSE;

    resp_channel_->send(resp_payload, resp_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x1000);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 4);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::FIXED);
    EXPECT_EQ(item.cmd, cmd_type_t::WRITE);

    delete [] resp_ptr;
}

TEST_F(AxiSlaveBridgeTest, W_FIXED_UNALIGN) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    initialize_write_transaction(trans, 256);
    trans.cmd = umi_axi::WRITE;
    trans.id = 0x123;
    trans.addr = 0x1001;
    trans.size = size_type_t::BYTE_4;
    trans.len = 4;  
    trans.burst = umi_axi::burst_type_t::FIXED;
    trans.data_width = umi_axi::bus_width_t::BIT_32;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);
  
    fake_slave_->seq.push_back(&trans);
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    auto recv_data = req_channel_->recv();

    std::vector<uint8_t> expected_result = {
        0x00, 0x01, 0x02, 0x03,
        0x00, 0x05, 0x06, 0x07,
        0x00, 0x09, 0x0A, 0x0B,
        0x00, 0x0D, 0x0E, 0x0F,
        0x00, 0x11, 0x12, 0x13,
    };

    EXPECT_EQ(recv_data.empty(), false);
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload) + data_size*2);
    TlmPayload* req_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    req_payload->data = reinterpret_cast<unsigned char*>(req_payload) + sizeof(TlmPayload);
    EXPECT_EQ(req_payload->command, tlm::TLM_WRITE_COMMAND);
    EXPECT_EQ(req_payload->data_length, data_size);
    EXPECT_EQ(req_payload->streaming_width, 1 << trans.size);
    EXPECT_EQ(req_payload->address, utils::GetAlignedAddress(trans.addr, trans.size));
    for (int i = 0; i < req_payload->data_length; ++i) {
        EXPECT_EQ(req_payload->data[i] , expected_result[i]);
    }

    uint32_t resp_size = sizeof(TlmPayload);
    uint8_t* resp_ptr = new uint8_t [resp_size];
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(resp_ptr);
    memcpy(resp_payload, req_payload, sizeof(TlmPayload));
    resp_payload->data = nullptr;
    resp_payload->response = tlm::TLM_OK_RESPONSE;

    resp_channel_->send(resp_payload, resp_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x1001);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 4);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::FIXED);
    EXPECT_EQ(item.cmd, cmd_type_t::WRITE);

    delete [] resp_ptr;
}

TEST_F(AxiSlaveBridgeTest, W_FIXED_UNALIGN_NARROW) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    initialize_write_transaction(trans, 256);
    trans.cmd = umi_axi::WRITE;
    trans.id = 0x123;
    trans.addr = 0x1003;
    trans.size = size_type_t::BYTE_4;
    trans.len = 4;  
    trans.burst = umi_axi::burst_type_t::FIXED;
    trans.data_width = umi_axi::bus_width_t::BIT_64;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);
  
    fake_slave_->seq.push_back(&trans);
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    auto recv_data = req_channel_->recv();
  
    std::vector<uint8_t> expected_data = {
        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
        0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F,
        0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
        0x18, 0x19, 0x1A, 0x1B, 0x1C, 0x1D, 0x1E, 0x1F,
        0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27
    };

    std::vector<uint8_t> expected_be = {
        0x00, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00, 0x00,
    };

    std::vector<uint8_t> expected_result = {
        0x00, 0x00, 0x00, 0x03,
        0x00, 0x00, 0x00, 0x0B, 
        0x00, 0x00, 0x00, 0x13,
        0x00, 0x00, 0x00, 0x1B,
        0x00, 0x00, 0x00, 0x23,
    };

    EXPECT_EQ(recv_data.empty(), false);
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload) + data_size*2);
    TlmPayload* req_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    req_payload->data = reinterpret_cast<unsigned char*>(req_payload) + sizeof(TlmPayload);
    EXPECT_EQ(req_payload->command, tlm::TLM_WRITE_COMMAND);
    EXPECT_EQ(req_payload->data_length, data_size);
    EXPECT_EQ(req_payload->streaming_width, 1 << trans.size);
    EXPECT_EQ(req_payload->address, utils::GetAlignedAddress(trans.addr, trans.size));
    for (int i = 0; i < req_payload->data_length; ++i) {
        EXPECT_EQ(req_payload->data[i] , expected_result[i]);
    }

    uint32_t resp_size = sizeof(TlmPayload);
    uint8_t* resp_ptr = new uint8_t [resp_size];
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(resp_ptr);
    memcpy(resp_payload, req_payload, sizeof(TlmPayload));
    resp_payload->data = nullptr;
    resp_payload->response = tlm::TLM_OK_RESPONSE;

    resp_channel_->send(resp_payload, resp_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x1003);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 4);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::FIXED);
    EXPECT_EQ(item.cmd, cmd_type_t::WRITE);

    delete [] resp_ptr;
}

TEST_F(AxiSlaveBridgeTest, W_FIXED_MULTI) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    std::vector<axi_transaction_item> transactions;
    const int MULTI_SIZE = 5;
    const int DATA_SIZE = 4096;
    // 4KB transfer
    for (int i = 0; i < MULTI_SIZE; ++i) {
        axi_transaction_item trans;
        trans.data = std::vector<uint8_t>(DATA_SIZE, 0);
        trans.be = std::vector<uint8_t>(DATA_SIZE, 0xFF);
        trans.cmd = umi_axi::WRITE;
        trans.id = i;
        trans.addr = i * 0x1000;
        trans.size = umi_axi::BYTE_32;
        trans.len = 127;
        trans.burst = umi_axi::FIXED;
        trans.data_width = umi_axi::bus_width_t::BIT_256;
        for (int j = 0; j < DATA_SIZE; ++j) {
            trans.data[j] = j % 256;
        }  
        transactions.push_back(trans);
    }
    
    
    for (size_t i = 0; i < transactions.size(); ++i) {
        auto& trans = transactions[i];
        fake_slave_->seq.push_back(&trans);
    }
    std::cout << "Send axi item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "Send axi item notify end" << std::endl;
    for (size_t i = 0; i < MULTI_SIZE; ++i) {
        std::cout << "Receive burst transaction " << i << " of " << transactions.size() << std::endl;
        // Verify received data
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
        auto recv_data = req_channel_->recv();
        EXPECT_FALSE(recv_data.empty());
        EXPECT_EQ(recv_data.size(), sizeof(TlmPayload) + DATA_SIZE * 2);

        TlmPayload* req_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
        req_payload->data = reinterpret_cast<uint8_t*>(req_payload) + sizeof(TlmPayload);
        std::cout << "req_payload id: " << req_payload->id << std::endl;
        std::cout << "req_payload address: " << req_payload->address << std::endl;
        EXPECT_EQ(req_payload->command, tlm::TLM_WRITE_COMMAND);
        EXPECT_EQ(req_payload->data_length, DATA_SIZE);
        EXPECT_EQ(req_payload->streaming_width, 32);
        EXPECT_EQ(req_payload->address, i * 0x1000);
        for (int j = 0; j < DATA_SIZE; ++j) {
            EXPECT_EQ(req_payload->data[j], j % 256);
        }

        uint32_t resp_size = sizeof(TlmPayload);
        uint8_t* resp_ptr = new uint8_t [resp_size];
        TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(resp_ptr);
        memcpy(resp_payload, req_payload, sizeof(TlmPayload));
        resp_payload->data = nullptr;
        resp_payload->response = tlm::TLM_OK_RESPONSE;
        
        resp_channel_->send(resp_payload, resp_size);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
     

        delete [] resp_ptr;
    }

    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), MULTI_SIZE);
    for (int i = 0; i < xtor_slave_proxy_->response_lists_.size(); ++i) {
        axi_transaction_item& item = xtor_slave_proxy_->response_lists_[i];
        EXPECT_EQ(item.addr, i * 0x1000);
        EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, 127);
        EXPECT_EQ(item.size, size_type_t::BYTE_32);
        EXPECT_EQ(item.burst, burst_type_t::FIXED);
        EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
    }
}

TEST_F(AxiSlaveBridgeTest, W_WRAP) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    initialize_write_transaction(trans, 256);
    trans.id = 0x123;
    trans.addr = 0x34;
    trans.size = size_type_t::BYTE_4;
    trans.len = 5;  
    trans.burst = umi_axi::burst_type_t::WRAP;
    trans.data_width = umi_axi::bus_width_t::BIT_32;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);
  
    fake_slave_->seq.push_back(&trans);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    auto recv_data = req_channel_->recv();
    EXPECT_EQ(recv_data.empty(), false);
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload) + data_size*2);
    TlmPayload* req_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    req_payload->data = reinterpret_cast<unsigned char*>(req_payload) + sizeof(TlmPayload);
    EXPECT_EQ(req_payload->command, tlm::TLM_WRITE_COMMAND);
    EXPECT_EQ(req_payload->data_length, data_size);
    EXPECT_EQ(req_payload->streaming_width, 0x0);
    EXPECT_EQ(req_payload->address, utils::GetLowWrapBoundary(trans.addr, trans.size, trans.len));
    std::vector<uint8_t> expected_result = {
        0x14, 0x15, 0x16, 0x17,
        0x00, 0x01, 0x02, 0x03,
        0x04, 0x05, 0x06, 0x07,
        0x08, 0x09, 0x0A, 0x0B, 
        0x0C, 0x0D, 0x0E, 0x0F,
        0x10, 0x11, 0x12, 0x13,
    };
    for (int i = 0; i < data_size; ++i) {
        EXPECT_EQ(req_payload->data[i], expected_result[i]);
    }

    uint32_t resp_size = sizeof(TlmPayload);
    uint8_t* resp_ptr = new uint8_t [resp_size];
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(resp_ptr);
    memcpy(resp_payload, req_payload, sizeof(TlmPayload));
    resp_payload->data = nullptr;
    resp_payload->response = tlm::TLM_OK_RESPONSE;
    
    resp_channel_->send(resp_payload, resp_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x34);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 5);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::WRAP);
    EXPECT_EQ(item.cmd, cmd_type_t::WRITE);

    delete [] resp_ptr;
}

TEST_F(AxiSlaveBridgeTest, W_WRAP_NARROW) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    initialize_write_transaction(trans, 256);
    trans.cmd = umi_axi::WRITE;
    trans.id = 0x123;
    trans.addr = 0x1004; // wrap boundary : 0xff0-0x1007
    trans.size = size_type_t::BYTE_4;
    trans.len = 5;  
    trans.burst = umi_axi::burst_type_t::WRAP;
    trans.data_width = umi_axi::bus_width_t::BIT_64;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);
  
    fake_slave_->seq.push_back(&trans);
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    auto recv_data = req_channel_->recv();
  
    std::vector<uint8_t> expected_data = {
        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
        0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F,
        0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
        0x18, 0x19, 0x1A, 0x1B, 0x1C, 0x1D, 0x1E, 0x1F,
        0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27,
        0x28, 0x29, 0x2A, 0x2B, 0x2C, 0x2D, 0x2E, 0x2F,
    };

    std::vector<uint8_t> expected_be = {
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
    };

    std::vector<uint8_t> expected_result = {
        0x08, 0x09, 0x0A, 0x0B,
        0x14, 0x15, 0x16, 0x17, 
        0x18, 0x19, 0x1A, 0x1B,
        0x24, 0x25, 0x26, 0x27,
        0x28, 0x29, 0x2A, 0x2B,
        0x04, 0x05, 0x06, 0x07,
    };

    EXPECT_EQ(recv_data.empty(), false);
    EXPECT_EQ(recv_data.size(), sizeof(TlmPayload) + data_size*2);
    TlmPayload* req_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    req_payload->data = reinterpret_cast<unsigned char*>(req_payload) + sizeof(TlmPayload);
    EXPECT_EQ(req_payload->command, tlm::TLM_WRITE_COMMAND);
    EXPECT_EQ(req_payload->data_length, data_size);
    EXPECT_EQ(req_payload->streaming_width, 0x0);
    EXPECT_EQ(req_payload->address, utils::GetLowWrapBoundary(trans.addr, trans.size, trans.len));
    for (int i = 0; i < req_payload->data_length; ++i) {
        EXPECT_EQ(req_payload->data[i] , expected_result[i]);
    }

    uint32_t resp_size = sizeof(TlmPayload);
    uint8_t* resp_ptr = new uint8_t [resp_size];
    TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(resp_ptr);
    memcpy(resp_payload, req_payload, sizeof(TlmPayload));
    resp_payload->data = nullptr;
    resp_payload->response = tlm::TLM_OK_RESPONSE;

    resp_channel_->send(resp_payload, resp_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x1004);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 5);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::WRAP);
    EXPECT_EQ(item.cmd, cmd_type_t::WRITE);

    delete [] resp_ptr;
}

TEST_F(AxiSlaveBridgeTest, W_WRAP_MULTI) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    std::vector<axi_transaction_item> transactions;
    const int MULTI_SIZE = 3;
    const int DATA_SIZE = 12;
    // 4KB transfer
    for (int i = 0; i < MULTI_SIZE; ++i) {
        axi_transaction_item trans;
        trans.data = std::vector<uint8_t>(DATA_SIZE, 0);
        trans.be = std::vector<uint8_t>(DATA_SIZE, 0xFF);
        trans.cmd = umi_axi::WRITE;
        trans.id = i;
        trans.addr = 0x10; // 0xc - 0x17
        trans.size = umi_axi::BYTE_4;
        trans.len = 2;
        trans.burst = umi_axi::WRAP;
        trans.data_width = umi_axi::bus_width_t::BIT_32;
        for (int j = 0; j < DATA_SIZE; ++j) {
            trans.data[j] = j % 256;
        }  
        transactions.push_back(trans);
    }
    
    
    for (size_t i = 0; i < transactions.size(); ++i) {
        auto& trans = transactions[i];
        fake_slave_->seq.push_back(&trans);
    }
    std::cout << "Send axi item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "Send axi item notify end" << std::endl;
    for (size_t i = 0; i < MULTI_SIZE; ++i) {
        std::cout << "Receive burst transaction " << i << " of " << transactions.size() << std::endl;
        // Verify received data
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
        auto recv_data = req_channel_->recv();
        EXPECT_FALSE(recv_data.empty());
        EXPECT_EQ(recv_data.size(), sizeof(TlmPayload) + DATA_SIZE * 2);

        TlmPayload* req_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
        req_payload->data = reinterpret_cast<unsigned char*>(req_payload) + sizeof(TlmPayload);
        std::cout << "req_payload id: " << req_payload->id << std::endl;
        std::cout << "req_payload address: " << req_payload->address << std::endl;
        EXPECT_EQ(req_payload->command, tlm::TLM_WRITE_COMMAND);
        EXPECT_EQ(req_payload->data_length, DATA_SIZE);
        EXPECT_EQ(req_payload->streaming_width, 0x0);
        EXPECT_EQ(req_payload->address, utils::GetLowWrapBoundary(0x10, umi_axi::BYTE_4, 2));
        std::vector<uint8_t> expected_result = {
            0x08, 0x09, 0x0A, 0x0B,
            0x00, 0x01, 0x02, 0x03,
            0x04, 0x05, 0x06, 0x07,
        };
        for (int j = 0; j < DATA_SIZE; ++j) {
            EXPECT_EQ(req_payload->data[j], expected_result[j]);
        }

        uint32_t resp_size = sizeof(TlmPayload);
        uint8_t* resp_ptr = new uint8_t [resp_size];
        TlmPayload* resp_payload = reinterpret_cast<TlmPayload*>(resp_ptr);
        memcpy(resp_payload, req_payload, sizeof(TlmPayload));
        resp_payload->data = nullptr;
        resp_payload->response = tlm::TLM_OK_RESPONSE;
        
        resp_channel_->send(resp_payload, resp_size);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
     

        delete [] resp_ptr;
    }

    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), MULTI_SIZE);
    for (int i = 0; i < xtor_slave_proxy_->response_lists_.size(); ++i) {
        axi_transaction_item& item = xtor_slave_proxy_->response_lists_[i];
        EXPECT_EQ(item.addr, 0x10);
        EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, 2);
        EXPECT_EQ(item.size, size_type_t::BYTE_4);
        EXPECT_EQ(item.burst, burst_type_t::WRAP);
        EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
    }
}


} // namespace ssln::hybrid::axi

int sc_main(int argc, char* argv[]) {
    return 0;
}