#include <systemc>
#include <gtest/gtest.h>
#include "data_builder.h"
#include "tlm_payload.h"
#include "axi_transaction_item.h"


// the address from tlm_payload should be aligned with axi size

namespace ssln::hybrid::axi {

using umi_axi::axi_transaction_item;
using umi_axi::burst_type_t;
using umi_axi::size_type_t;
using umi_axi::bus_width_t;
using umi_axi::response_type_t;
using umi_axi::cmd_type_t;
using umi_axi::lock_type_t;
using umi_axi::domain_t;
using umi_axi::barrier_t;
using umi_axi::spec_version_t;

class DataBuilderTest : public ::testing::Test {
 protected:
  void SetUp() override {
    tlm_payload_.data_length = 64;
    tlm_payload_.byte_enable_length = 64;
    tlm_payload_.axuser_length = 0;
    tlm_payload_.xuser_length = 0;
    tlm_payload_.streaming_width = 0;
    tlm_payload_.response = 0;
    tlm_payload_.data = new uint8_t[tlm_payload_.data_length+tlm_payload_.byte_enable_length];
    for (int i = 0; i < tlm_payload_.data_length; ++i) {
      tlm_payload_.data[i] = static_cast<uint8_t>(i);
    }
    for (int i = 0; i < tlm_payload_.byte_enable_length; ++i) {
      tlm_payload_.data[tlm_payload_.data_length+i] = 0xFF;
    }
  }

  void TearDown() override {
    delete[] tlm_payload_.data;
  }

  TlmPayload tlm_payload_;
  DataBuilder builder_;
};

TEST_F(DataBuilderTest, AlignIncrNarrow) {
  axi_transaction_item axi_trans;
  axi_trans.addr = 0x0034;
  axi_trans.size = size_type_t::BYTE_4;
  axi_trans.len = 4;
  axi_trans.data_width = bus_width_t::BIT_64;
  axi_trans.burst = burst_type_t::INCR;

  tlm_payload_.address = 0x0034;

  builder_.BuildReadResponse(tlm_payload_, axi_trans);

  std::vector<uint8_t> expected_data = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x02, 0x03,
    0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b,
    0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b,
    0x0c, 0x0d, 0x0e, 0x0f, 0x10, 0x11, 0x12, 0x13,
    0x0c, 0x0d, 0x0e, 0x0f, 0x10, 0x11, 0x12, 0x13
  };

  std::vector<uint8_t> expected_be = {
    0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF
  };

  for (int i = 0; i < axi_trans.data.size(); ++i) {
    ASSERT_EQ(axi_trans.data[i]&axi_trans.be[i], expected_data[i]&expected_be[i]);
  }
}

TEST_F(DataBuilderTest, AlignWrapNarrow) {
  axi_transaction_item axi_trans;
  axi_trans.addr = 0x0034;
  axi_trans.size = size_type_t::BYTE_4;
  axi_trans.len = 5;
  axi_trans.data_width = bus_width_t::BIT_64;
  axi_trans.burst = burst_type_t::WRAP;

  tlm_payload_.address = 0x0034;

  builder_.BuildReadResponse(tlm_payload_, axi_trans);

  // std::vector<uint8_t> expected_data = {
  //   0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x02, 0x03,
  //   0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b,
  //   0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b,
  //   0x0c, 0x0d, 0x0e, 0x0f, 0x10, 0x11, 0x12, 0x13,
  //   0x0c, 0x0d, 0x0e, 0x0f, 0x10, 0x11, 0x12, 0x13,
  //   0x14, 0x15, 0x16, 0x17, 0x00, 0x00, 0x00, 0x00
  // };
  std::vector<uint8_t> expected_data = {
    0x00, 0x00, 0x00, 0x00, 0x04, 0x05, 0x06, 0x07,
    0x08, 0x09, 0x0a, 0x0b, 0x08, 0x09, 0x0a, 0x0b,
    0x04, 0x05, 0x06, 0x07, 0x0c, 0x0d, 0x0e, 0x0f,
    0x10, 0x11, 0x12, 0x13, 0x10, 0x11, 0x12, 0x13,
    0x0c, 0x0d, 0x0e, 0x0f, 0x14, 0x15, 0x16, 0x17,
    0x00, 0x01, 0x02, 0x03, 0x00, 0x00, 0x00, 0x00
  };

  std::vector<uint8_t> expected_be = {
    0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00
  };

  for (int i = 0; i < axi_trans.data.size(); ++i) {
    ASSERT_EQ(axi_trans.data[i]&axi_trans.be[i], expected_data[i]&expected_be[i]);
  }
}

TEST_F(DataBuilderTest, UnalignFixed) {
  axi_transaction_item axi_trans;
  axi_trans.addr = 0x0033;
  axi_trans.size = size_type_t::BYTE_4;
  axi_trans.len = 2;
  axi_trans.data_width = bus_width_t::BIT_32;
  axi_trans.burst = burst_type_t::FIXED;

  tlm_payload_.address = 0x0030;

  builder_.BuildReadResponse(tlm_payload_, axi_trans);

  std::vector<uint8_t> expected_data = {
    0x00, 0x01, 0x02, 0x03,
    0x04, 0x05, 0x06, 0x07,
    0x08, 0x09, 0x0a, 0x0b
  };

  std::vector<uint8_t> expected_be = {
    0x00, 0x00, 0x00, 0xff,
    0x00, 0x00, 0x00, 0xff,
    0x00, 0x00, 0x00, 0xff,
  };

  for (int i = 0; i < axi_trans.data.size(); ++i) {
    ASSERT_EQ(axi_trans.data[i]&axi_trans.be[i], expected_data[i]&expected_be[i]);
  }
}

TEST_F(DataBuilderTest, UnalignFixedSmall) {
  axi_transaction_item axi_trans;
  axi_trans.addr = 0x0031;
  axi_trans.size = size_type_t::BYTE_1;
  axi_trans.len = 2;
  axi_trans.data_width = bus_width_t::BIT_32;
  axi_trans.burst = burst_type_t::FIXED;

  tlm_payload_.address = 0x0031;

  builder_.BuildReadResponse(tlm_payload_, axi_trans);

  std::vector<uint8_t> expected_data = {
    0x00, 0x00, 0x01, 0x02,
    0x00, 0x01, 0x02, 0x00,
    0x00, 0x02, 0x00, 0x00
  };

  std::vector<uint8_t> expected_be = {
    0x00, 0xff, 0x00, 0x00,
    0x00, 0xff, 0x00, 0x00,
    0x00, 0xff, 0x00, 0x00,
  };

  for (int i = 0; i < axi_trans.data.size(); ++i) {
    ASSERT_EQ(axi_trans.data[i]&axi_trans.be[i], expected_data[i]&expected_be[i]);
  }
}

TEST_F(DataBuilderTest, AlignIncrNarrow2) {
  axi_transaction_item axi_trans;
  axi_trans.addr = 0x0034;
  axi_trans.size = size_type_t::BYTE_2;
  axi_trans.len = 5;
  axi_trans.data_width = bus_width_t::BIT_64;
  axi_trans.burst = burst_type_t::INCR;

  tlm_payload_.address = 0x0034;

  builder_.BuildReadResponse(tlm_payload_, axi_trans);

  std::vector<uint8_t> expected_data = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x02, 0x03,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x02, 0x03,
    0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b,
    0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b,
    0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b,
    0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b
  };

  std::vector<uint8_t> expected_be = {
    0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff,
    0xff, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff,
  };

  for (int i = 0; i < axi_trans.data.size(); ++i) {
    ASSERT_EQ(axi_trans.data[i]&axi_trans.be[i], expected_data[i]&expected_be[i]);
  }
}

TEST_F(DataBuilderTest, AlignIncrNarrow_BYTE1) {
  axi_transaction_item axi_trans;
  axi_trans.addr = 0x0033;
  axi_trans.size = size_type_t::BYTE_1;
  axi_trans.len = 15;
  axi_trans.data_width = bus_width_t::BIT_64;
  axi_trans.burst = burst_type_t::INCR;

  tlm_payload_.address = 0x0033;

  builder_.BuildReadResponse(tlm_payload_, axi_trans);

  std::vector<uint8_t> expected_data = {
    0x00, 0x00, 0x00, 0x00, 0x01, 0x02, 0x03, 0x04,
    0x00, 0x00, 0x00, 0x00, 0x01, 0x02, 0x03, 0x04,
    0x00, 0x00, 0x00, 0x00, 0x01, 0x02, 0x03, 0x04,
    0x00, 0x00, 0x00, 0x00, 0x01, 0x02, 0x03, 0x04,
    0x00, 0x00, 0x00, 0x00, 0x01, 0x02, 0x03, 0x04,
    0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c,
    0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c,
    0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c,
    0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c,
    0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c,
    0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c,
    0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c,
    0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c,
    0x0d, 0x0e, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x0d, 0x0e, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x0d, 0x0e, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00
  };

  std::vector<uint8_t> expected_be = {
    0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 
    0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 
    0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00
  };

  for (int i = 0; i < axi_trans.data.size(); ++i) {
    ASSERT_EQ(axi_trans.data[i]&axi_trans.be[i], expected_data[i]&expected_be[i]);
  }
}

TEST_F(DataBuilderTest, AlignIncr) {
  axi_transaction_item axi_trans;
  axi_trans.addr = 0x0036;
  axi_trans.size = size_type_t::BYTE_4;
  axi_trans.len = 4;
  axi_trans.data_width = bus_width_t::BIT_32;
  axi_trans.burst = burst_type_t::INCR;

  tlm_payload_.address = 0x0034;

  builder_.BuildReadResponse(tlm_payload_, axi_trans);

  std::vector<uint8_t> expected_data = {
    0x00, 0x01, 0x02, 0x03, 
    0x04, 0x05, 0x06, 0x07,
    0x08, 0x09, 0x0a, 0x0b, 
    0x0c, 0x0d, 0x0e, 0x0f,
    0x10, 0x11, 0x12, 0x13
  };
  
  std::vector<uint8_t> expected_be = {
    0x00, 0x00, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff
  };

  for (int i = 0; i < axi_trans.data.size(); ++i) {
    ASSERT_EQ(axi_trans.data[i]&axi_trans.be[i], expected_data[i]&expected_be[i]);
  }
}

TEST_F(DataBuilderTest, UnalignIncrNarrow2) {
  axi_transaction_item axi_trans;
  axi_trans.addr = 0x0035;
  axi_trans.size = size_type_t::BYTE_4;
  axi_trans.len = 3;
  axi_trans.data_width = bus_width_t::BIT_64;
  axi_trans.burst = burst_type_t::INCR;

  tlm_payload_.address = 0x0034;

  builder_.BuildReadResponse(tlm_payload_, axi_trans);

  std::vector<uint8_t> expected_data = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x02, 0x03,
    0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b,
    0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b,
    0x0c, 0x0d, 0x0e, 0x0f, 0x00, 0x00, 0x00, 0x00
  };

  std::vector<uint8_t> expected_be = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, 
    0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00
  };

  for (int i = 0; i < axi_trans.data.size(); ++i) {
    ASSERT_EQ(axi_trans.data[i]&axi_trans.be[i], expected_data[i]&expected_be[i]);
  }
}

TEST_F(DataBuilderTest, UnalignIncrNarrow3) {
  axi_transaction_item axi_trans;
  axi_trans.addr = 0x0035;
  axi_trans.size = size_type_t::BYTE_1;
  axi_trans.len = 1;
  axi_trans.data_width = bus_width_t::BIT_64;
  axi_trans.burst = burst_type_t::INCR;

  tlm_payload_.address = 0x0035;

  builder_.BuildReadResponse(tlm_payload_, axi_trans);

  std::vector<uint8_t> expected_data = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00
  };

  std::vector<uint8_t> expected_be = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x00
  };

  for (int i = 0; i < axi_trans.data.size(); ++i) {
    ASSERT_EQ(axi_trans.data[i]&axi_trans.be[i], expected_data[i]&expected_be[i]);
  }
}

TEST_F(DataBuilderTest, UnalignIncrNarrow4) {
  axi_transaction_item axi_trans;
  axi_trans.addr = 0x0035;
  axi_trans.size = size_type_t::BYTE_2;
  axi_trans.len = 2;
  axi_trans.data_width = bus_width_t::BIT_64;
  axi_trans.burst = burst_type_t::INCR;

  tlm_payload_.address = 0x0034;

  builder_.BuildReadResponse(tlm_payload_, axi_trans);

  std::vector<uint8_t> expected_data = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x02, 0x03,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x02, 0x03,
    0x04, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
  };

  std::vector<uint8_t> expected_be = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff,
    0xff, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
  };

  for (int i = 0; i < axi_trans.data.size(); ++i) {
    ASSERT_EQ(axi_trans.data[i]&axi_trans.be[i], expected_data[i]&expected_be[i]);
  }
}

TEST_F(DataBuilderTest, WriteResponse) {
  axi_transaction_item axi_trans;
  tlm_payload_.response = 1;  // TLM_OK_RESPONSE

  builder_.BuildWriteResponse(tlm_payload_, axi_trans);

  ASSERT_EQ(axi_trans.resp[0], response_type_t::OKAY);  // OKAY
}

}  // namespace ssln::hybrid::axi

int sc_main(int argc, char **argv) {
  ::testing::InitGoogleTest(&argc, argv);
  return RUN_ALL_TESTS();
} 