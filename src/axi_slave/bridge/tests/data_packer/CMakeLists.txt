add_executable(data_packer_test
    src/data_packer_test.cc
)


target_link_directories(data_packer_test
    PRIVATE
        ${PROJECT_ROOT}/thirdparty/fake_install/lib
        $ENV{UMICOM_HOME}/lib
)

target_link_libraries(data_packer_test
    PRIVATE
        GTest::gtest_main
        axi_slave_bridge
        fake_xtor
        systemc
)

include(GoogleTest)
gtest_discover_tests(data_packer_test)

add_dependencies(build_utests data_packer_test)