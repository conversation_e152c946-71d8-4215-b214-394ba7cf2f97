#include <systemc>
#include <gtest/gtest.h>
#include "data_packer.h"
#include "tlm_payload.h"
#include "axi_transaction_item.h"

namespace ssln::hybrid::axi {

using umi_axi::axi_transaction_item;
using umi_axi::burst_type_t;
using umi_axi::size_type_t;
using umi_axi::bus_width_t;
using umi_axi::response_type_t;
using umi_axi::cmd_type_t;
using umi_axi::lock_type_t;
using umi_axi::domain_t;
using umi_axi::barrier_t;
using umi_axi::spec_version_t;

class DataPackerTest : public ::testing::Test {
 protected:
  void SetUp() override {
    test_data_.resize(256);
    for (size_t i = 0; i < test_data_.size(); ++i) {
      test_data_[i] = static_cast<uint8_t>(i);
    }
    test_be_.resize(256, 0xFF);  // All bytes enabled
  }

  void TearDown() override {
    if (tlm_payload_.data != nullptr) {
      delete[] tlm_payload_.data;
      tlm_payload_.data = nullptr;
    }
  }

  std::vector<uint8_t> test_data_;
  std::vector<uint8_t> test_be_;
  TlmPayload tlm_payload_;
  DataPacker packer_;
};

TEST_F(DataPackerTest, IncrModeTest) {
  axi_transaction_item axi_trans;
  axi_trans.reset();
  axi_trans.cmd = cmd_type_t::WRITE;
  axi_trans.addr = 0x1000;
  axi_trans.id = 0;
  axi_trans.burst = burst_type_t::INCR;
  axi_trans.size = size_type_t::BYTE_4;
  axi_trans.len = 3;
  axi_trans.data_width = bus_width_t::BIT_64;
  axi_trans.data = test_data_;
  axi_trans.be = test_be_;
  uint32_t expected_size = (1 << axi_trans.size) * (axi_trans.len + 1);
  tlm_payload_.data = new uint8_t[expected_size * 2]();
  tlm_payload_.data_length = expected_size;
  packer_.PackWriteData(axi_trans, tlm_payload_);

  // Verify data length
  ASSERT_EQ(tlm_payload_.data_length, (1<<axi_trans.size) * (axi_trans.len + 1));
  ASSERT_EQ(tlm_payload_.byte_enable_length, (1<<axi_trans.size) * (axi_trans.len + 1));

  // Verify data contents
  EXPECT_EQ(tlm_payload_.data[0], 0);
  EXPECT_EQ(tlm_payload_.data[1], 1);
  EXPECT_EQ(tlm_payload_.data[2], 2);
  EXPECT_EQ(tlm_payload_.data[3], 3);
  EXPECT_EQ(tlm_payload_.data[4], 12);
  EXPECT_EQ(tlm_payload_.data[5], 13);
  EXPECT_EQ(tlm_payload_.data[6], 14);
  EXPECT_EQ(tlm_payload_.data[7], 15);
  EXPECT_EQ(tlm_payload_.data[8], 16);
  EXPECT_EQ(tlm_payload_.data[9], 17);
  EXPECT_EQ(tlm_payload_.data[10], 18);
  EXPECT_EQ(tlm_payload_.data[11], 19);
  EXPECT_EQ(tlm_payload_.data[12], 28);
  EXPECT_EQ(tlm_payload_.data[13], 29);
  EXPECT_EQ(tlm_payload_.data[14], 30);
  EXPECT_EQ(tlm_payload_.data[15], 31);

  // Verify byte enables
  for (size_t i = 0; i < tlm_payload_.data_length; ++i) {
    EXPECT_EQ(tlm_payload_.data[tlm_payload_.data_length + i], 0xFF);
  }
}

TEST_F(DataPackerTest, IncrModeTest2) {
  axi_transaction_item axi_trans;
  axi_trans.reset();
  axi_trans.cmd = cmd_type_t::WRITE;
  axi_trans.addr = 0x1004;
  axi_trans.id = 0;
  axi_trans.burst = burst_type_t::INCR;
  axi_trans.size = size_type_t::BYTE_4;
  axi_trans.len = 3;
  axi_trans.data_width = bus_width_t::BIT_64;
  axi_trans.data = test_data_;
  axi_trans.be = test_be_;
  uint32_t expected_size = (1 << axi_trans.size) * (axi_trans.len + 1);
  tlm_payload_.data = new uint8_t[expected_size * 2]();
  tlm_payload_.data_length = expected_size;
  packer_.PackWriteData(axi_trans, tlm_payload_);

  // Verify data length
  ASSERT_EQ(tlm_payload_.data_length, (1<<axi_trans.size) * (axi_trans.len + 1));  // 4 beats * 4 bytes
  ASSERT_EQ(tlm_payload_.byte_enable_length, (1<<axi_trans.size) * (axi_trans.len + 1));

  // Verify data contents
  EXPECT_EQ(tlm_payload_.data[0], 4);
  EXPECT_EQ(tlm_payload_.data[1], 5);
  EXPECT_EQ(tlm_payload_.data[2], 6);
  EXPECT_EQ(tlm_payload_.data[3], 7);
  EXPECT_EQ(tlm_payload_.data[4], 8);
  EXPECT_EQ(tlm_payload_.data[5], 9);
  EXPECT_EQ(tlm_payload_.data[6], 10);
  EXPECT_EQ(tlm_payload_.data[7], 11);
  EXPECT_EQ(tlm_payload_.data[8], 20);
  EXPECT_EQ(tlm_payload_.data[9], 21);
  EXPECT_EQ(tlm_payload_.data[10], 22);
  EXPECT_EQ(tlm_payload_.data[11], 23);
  EXPECT_EQ(tlm_payload_.data[12], 24);
  EXPECT_EQ(tlm_payload_.data[13], 25);
  EXPECT_EQ(tlm_payload_.data[14], 26);
  EXPECT_EQ(tlm_payload_.data[15], 27);

  // Verify byte enables
  for (size_t i = 0; i < tlm_payload_.byte_enable_length; ++i) {
    EXPECT_EQ(tlm_payload_.data[tlm_payload_.data_length + i], 0xFF);
  }
}

TEST_F(DataPackerTest, UnalignedIncrTest) {
  axi_transaction_item axi_trans;
  axi_trans.reset();
  axi_trans.cmd = cmd_type_t::WRITE;
  axi_trans.addr = 0x1003;
  axi_trans.id = 0;
  axi_trans.burst = burst_type_t::INCR;
  axi_trans.size = size_type_t::BYTE_4;
  axi_trans.len = 3;
  axi_trans.data_width = bus_width_t::BIT_64;
  axi_trans.data = test_data_;
  axi_trans.be = test_be_;
  uint32_t expected_size = (1 << axi_trans.size) * (axi_trans.len + 1);
  tlm_payload_.data = new uint8_t[expected_size * 2]();
  tlm_payload_.data_length = expected_size;
  packer_.PackWriteData(axi_trans, tlm_payload_);

  // Verify data length
  ASSERT_EQ(tlm_payload_.data_length, (1<<axi_trans.size) * (axi_trans.len + 1));  // 4 beats * 4 bytes
  ASSERT_EQ(tlm_payload_.byte_enable_length, (1<<axi_trans.size) * (axi_trans.len + 1));

  // Verify data contents
  EXPECT_EQ(tlm_payload_.data[0], 0);
  EXPECT_EQ(tlm_payload_.data[1], 0);
  EXPECT_EQ(tlm_payload_.data[2], 0);
  EXPECT_EQ(tlm_payload_.data[3], 3);
  EXPECT_EQ(tlm_payload_.data[4], 12);
  EXPECT_EQ(tlm_payload_.data[5], 13);
  EXPECT_EQ(tlm_payload_.data[6], 14);
  EXPECT_EQ(tlm_payload_.data[7], 15);
  EXPECT_EQ(tlm_payload_.data[8], 16);
  EXPECT_EQ(tlm_payload_.data[9], 17);
  EXPECT_EQ(tlm_payload_.data[10], 18);
  EXPECT_EQ(tlm_payload_.data[11], 19);
  EXPECT_EQ(tlm_payload_.data[12], 28);
  EXPECT_EQ(tlm_payload_.data[13], 29);
  EXPECT_EQ(tlm_payload_.data[14], 30);
  EXPECT_EQ(tlm_payload_.data[15], 31);

  // Verify byte enables
  for (size_t i = 0; i < 3; ++i) {
    EXPECT_EQ(tlm_payload_.data[tlm_payload_.data_length + i], 0x00);
  }
  for (size_t i = 3; i < tlm_payload_.byte_enable_length; ++i) {
    EXPECT_EQ(tlm_payload_.data[tlm_payload_.data_length + i], 0xFF);
  }
}

TEST_F(DataPackerTest, WrapModeTest) {
  axi_transaction_item axi_trans;
  axi_trans.reset();
  axi_trans.cmd = cmd_type_t::WRITE;
  axi_trans.addr = 0x1002;
  axi_trans.id = 0;
  axi_trans.burst = burst_type_t::WRAP;
  axi_trans.size = size_type_t::BYTE_2;
  axi_trans.len = 7;
  axi_trans.data_width = bus_width_t::BIT_64;
  axi_trans.data = test_data_;
  axi_trans.be = test_be_;
  uint32_t expected_size = (1 << axi_trans.size) * (axi_trans.len + 1);
  tlm_payload_.data = new uint8_t[expected_size * 2]();
  tlm_payload_.data_length = expected_size;
  packer_.PackWriteData(axi_trans, tlm_payload_);

  // Verify data length
  ASSERT_EQ(tlm_payload_.data_length, (1<<axi_trans.size) * (axi_trans.len + 1));  // 8 beats * 2 bytes
  ASSERT_EQ(tlm_payload_.byte_enable_length, (1<<axi_trans.size) * (axi_trans.len + 1));

  // Verify data contents
  // EXPECT_EQ(tlm_payload_.data[0], 2);
  // EXPECT_EQ(tlm_payload_.data[1], 3);
  // EXPECT_EQ(tlm_payload_.data[2], 12);
  // EXPECT_EQ(tlm_payload_.data[3], 13);
  // EXPECT_EQ(tlm_payload_.data[4], 22);
  // EXPECT_EQ(tlm_payload_.data[5], 23);
  // EXPECT_EQ(tlm_payload_.data[6], 24);
  // EXPECT_EQ(tlm_payload_.data[7], 25);
  // EXPECT_EQ(tlm_payload_.data[8], 34);
  // EXPECT_EQ(tlm_payload_.data[9], 35);
  // EXPECT_EQ(tlm_payload_.data[10], 44);
  // EXPECT_EQ(tlm_payload_.data[11], 45);
  // EXPECT_EQ(tlm_payload_.data[12], 54);
  // EXPECT_EQ(tlm_payload_.data[13], 55);
  // EXPECT_EQ(tlm_payload_.data[14], 56);
  // EXPECT_EQ(tlm_payload_.data[15], 57);
    EXPECT_EQ(tlm_payload_.data[0], 56);
    EXPECT_EQ(tlm_payload_.data[1], 57);
    EXPECT_EQ(tlm_payload_.data[2], 2);
    EXPECT_EQ(tlm_payload_.data[3], 3);
    EXPECT_EQ(tlm_payload_.data[4], 12);
    EXPECT_EQ(tlm_payload_.data[5], 13);
    EXPECT_EQ(tlm_payload_.data[6], 22);
    EXPECT_EQ(tlm_payload_.data[7], 23);
    EXPECT_EQ(tlm_payload_.data[8], 24);
    EXPECT_EQ(tlm_payload_.data[9], 25);
    EXPECT_EQ(tlm_payload_.data[10], 34);
    EXPECT_EQ(tlm_payload_.data[11], 35);
    EXPECT_EQ(tlm_payload_.data[12], 44);
    EXPECT_EQ(tlm_payload_.data[13], 45);
    EXPECT_EQ(tlm_payload_.data[14], 54);
    EXPECT_EQ(tlm_payload_.data[15], 55);
  // Verify byte enables
  for (size_t i = 0; i < tlm_payload_.byte_enable_length; ++i) {
    EXPECT_EQ(tlm_payload_.data[tlm_payload_.data_length + i], 0xFF);
  }
}

TEST_F(DataPackerTest, UnalignedFixedModeTest) {
  axi_transaction_item axi_trans;
  axi_trans.reset();
  axi_trans.cmd = cmd_type_t::WRITE;
  axi_trans.addr = 0x1003;
  axi_trans.id = 0;
  axi_trans.burst = burst_type_t::FIXED;
  axi_trans.size = size_type_t::BYTE_4;
  axi_trans.len = 15;
  axi_trans.data_width = bus_width_t::BIT_128;
  axi_trans.data = test_data_;
  axi_trans.be = test_be_;
  uint32_t expected_size = (1 << axi_trans.size) * (axi_trans.len + 1);
  tlm_payload_.data = new uint8_t[expected_size * 2]();
  tlm_payload_.data_length = expected_size;
  packer_.PackWriteData(axi_trans, tlm_payload_);

  // Verify data length
  ASSERT_EQ(tlm_payload_.data_length, (1<<axi_trans.size) * (axi_trans.len + 1));  // 16 beats * 4 bytes
  ASSERT_EQ(tlm_payload_.byte_enable_length, (1<<axi_trans.size) * (axi_trans.len + 1));

  // Verify data contents
  EXPECT_EQ(tlm_payload_.data[0], 0);
  EXPECT_EQ(tlm_payload_.data[1], 0);
  EXPECT_EQ(tlm_payload_.data[2], 0);
  EXPECT_EQ(tlm_payload_.data[3], 3);
  EXPECT_EQ(tlm_payload_.data[4], 0);
  EXPECT_EQ(tlm_payload_.data[5], 0);
  EXPECT_EQ(tlm_payload_.data[6], 0);
  EXPECT_EQ(tlm_payload_.data[7], 19);
  // ... continue with more data checks as needed

  // Verify byte enables
  for (size_t i = 0; i < tlm_payload_.byte_enable_length; ++i) {
    if (i % 4 == 3) {
      EXPECT_EQ(tlm_payload_.data[tlm_payload_.data_length + i], 0xFF);
    } else {
      EXPECT_EQ(tlm_payload_.data[tlm_payload_.data_length + i], 0x00);
    }
  }
}

TEST_F(DataPackerTest, UnalignedFixedModeTest2) {
  axi_transaction_item axi_trans;
  axi_trans.reset();
  axi_trans.cmd = cmd_type_t::WRITE;
  axi_trans.addr = 0x1005;
  axi_trans.id = 0;
  axi_trans.burst = burst_type_t::FIXED;
  axi_trans.size = size_type_t::BYTE_2;
  axi_trans.len = 2;
  axi_trans.data_width = bus_width_t::BIT_64;
  axi_trans.data = test_data_;
  axi_trans.be = test_be_;
  uint32_t expected_size = (1 << axi_trans.size) * (axi_trans.len + 1);
  tlm_payload_.data = new uint8_t[expected_size * 2]();
  tlm_payload_.data_length = expected_size;
  packer_.PackWriteData(axi_trans, tlm_payload_);

  // Verify data length
  ASSERT_EQ(tlm_payload_.data_length, (1<<axi_trans.size) * (axi_trans.len + 1));  // 3 beats * 2 bytes
  ASSERT_EQ(tlm_payload_.byte_enable_length, (1<<axi_trans.size) * (axi_trans.len + 1));

  // Verify data contents
  EXPECT_EQ(tlm_payload_.data[0], 0);
  EXPECT_EQ(tlm_payload_.data[1], 5);
  EXPECT_EQ(tlm_payload_.data[2], 0);
  EXPECT_EQ(tlm_payload_.data[3], 13);
  EXPECT_EQ(tlm_payload_.data[4], 0);
  EXPECT_EQ(tlm_payload_.data[5], 21);

  // Verify byte enables
  for (size_t i = 0; i < tlm_payload_.byte_enable_length; ++i) {
    if (i % 2 == 1) {
      EXPECT_EQ(tlm_payload_.data[tlm_payload_.data_length + i], 0xFF);
    } else {
      EXPECT_EQ(tlm_payload_.data[tlm_payload_.data_length + i], 0x00);
    }
  }
}

TEST_F(DataPackerTest, NoByteEnableTest) {
  axi_transaction_item axi_trans;
  axi_trans.reset();
  axi_trans.cmd = cmd_type_t::WRITE;
  axi_trans.addr = 0x1000;
  axi_trans.id = 0;
  axi_trans.burst = burst_type_t::INCR;
  axi_trans.size = size_type_t::BYTE_4;
  axi_trans.len = 3;
  axi_trans.data_width = bus_width_t::BIT_64;
  axi_trans.data = test_data_;
  // Don't set be vector
  uint32_t expected_size = (1 << axi_trans.size) * (axi_trans.len + 1);
  tlm_payload_.data = new uint8_t[expected_size]();
  tlm_payload_.data_length = expected_size;
  packer_.PackWriteData(axi_trans, tlm_payload_);

  // Verify data length
  ASSERT_EQ(tlm_payload_.data_length, (1<<axi_trans.size) * (axi_trans.len + 1));  // 4 beats * 4 bytes
  ASSERT_EQ(tlm_payload_.byte_enable_length, 0);

  // Verify data contents
  EXPECT_EQ(tlm_payload_.data[0], 0);
  EXPECT_EQ(tlm_payload_.data[1], 1);
  EXPECT_EQ(tlm_payload_.data[2], 2);
  EXPECT_EQ(tlm_payload_.data[3], 3);
  EXPECT_EQ(tlm_payload_.data[4], 12);
  EXPECT_EQ(tlm_payload_.data[5], 13);
  EXPECT_EQ(tlm_payload_.data[6], 14);
  EXPECT_EQ(tlm_payload_.data[7], 15);
  EXPECT_EQ(tlm_payload_.data[8], 16);
  EXPECT_EQ(tlm_payload_.data[9], 17);
  EXPECT_EQ(tlm_payload_.data[10], 18);
  EXPECT_EQ(tlm_payload_.data[11], 19);
  EXPECT_EQ(tlm_payload_.data[12], 28);
  EXPECT_EQ(tlm_payload_.data[13], 29);
  EXPECT_EQ(tlm_payload_.data[14], 30);
  EXPECT_EQ(tlm_payload_.data[15], 31);
}

}  // namespace ssln::hybrid::axi

int sc_main(int argc, char **argv) {
  ::testing::InitGoogleTest(&argc, argv);
  return RUN_ALL_TESTS();
}
