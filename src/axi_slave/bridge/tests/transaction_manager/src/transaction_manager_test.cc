#include <systemc.h>
#include <gtest/gtest.h>
#include <vector>
#include "transaction_manager.h"

using namespace std;
using namespace ssln::hybrid::axi;
using namespace umi_axi;

class TransactionManagerTest: public testing::Test{
public:
    void SetUp() override{
        trans_manager = new TransactionManager(3);
    }

    umi_axi::axi_transaction_item CreateTestTransaction(uint32_t addr = 0x1000, 
                                             uint8_t len = 127) {
        axi_transaction_item axi_trans;
        axi_trans.id = 0;
        axi_trans.data = std::vector<uint8_t>(4096, 0);
        axi_trans.data_width = umi_axi::bus_width_t::BIT_256;
        axi_trans.addr = addr;
        axi_trans.len = len;
        axi_trans.cmd = cmd_type_t::WRITE;
        axi_trans.burst = burst_type_t::FIXED;
        axi_trans.size = size_type_t::BYTE_32;
        axi_trans.be = std::vector<uint8_t>(4096, 0xFF);
        for(int i = 0; i < 4096; ++i){
            axi_trans.data[i] = i % 256;
        }
        return axi_trans;
    }

    void TearDown() override{
        delete trans_manager;
    }

    TransactionManager* trans_manager;

};

TEST_F(TransactionManagerTest, RegisterTransaction){
    auto trans = CreateTestTransaction();
    size_t index = trans_manager->RegisterTransaction(trans);
    EXPECT_EQ(0, index);
    EXPECT_EQ(1, trans_manager->GetOutstandingCount());

}

TEST_F(TransactionManagerTest, BufferFullBehavior) {
    for (int i = 0; i < 3; ++i) {
        auto trans = CreateTestTransaction(0x1000 + i);
        trans_manager->RegisterTransaction(trans);
    }
    
    auto extra_trans = CreateTestTransaction();
    EXPECT_THROW(trans_manager->RegisterTransaction(extra_trans), std::runtime_error);
}

TEST_F(TransactionManagerTest, GetNextTransaction) {
    EXPECT_EQ(nullptr, trans_manager->GetNextTransaction());
    
    auto trans = CreateTestTransaction();
    size_t index = trans_manager->RegisterTransaction(trans);
    
    auto* next_trans = trans_manager->GetNextTransaction();
    ASSERT_NE(nullptr, next_trans);
    EXPECT_EQ(trans.addr, next_trans->addr);
    EXPECT_EQ(trans.burst, next_trans->burst);
    EXPECT_EQ(trans.len, next_trans->len);
}

TEST_F(TransactionManagerTest, CompleteTransaction) {
    auto trans = CreateTestTransaction();
    trans_manager->RegisterTransaction(trans);
    EXPECT_EQ(1, trans_manager->GetOutstandingCount());
   
    trans_manager->CompleteTransaction();
    EXPECT_EQ(0, trans_manager->GetOutstandingCount());
    EXPECT_THROW(trans_manager->CompleteTransaction(), std::runtime_error);
}

TEST_F(TransactionManagerTest, RingBufferBehavior) {
    for (int i = 0; i < 3; ++i) {
        auto trans = CreateTestTransaction(0x1000 + i);
        trans_manager->RegisterTransaction(trans);
    }
    
    trans_manager->CompleteTransaction();
    trans_manager->CompleteTransaction();
    EXPECT_EQ(1, trans_manager->GetOutstandingCount());
    
    auto trans1 = CreateTestTransaction(0x2000);
    auto trans2 = CreateTestTransaction(0x2001);
    EXPECT_NO_THROW(trans_manager->RegisterTransaction(trans1));
    EXPECT_NO_THROW(trans_manager->RegisterTransaction(trans2));
    EXPECT_EQ(3, trans_manager->GetOutstandingCount());
} 

int sc_main(int argc, char* argv[]) {
    return 0;
}