# AXI Slave Bridge Design Specification

## Overview

The AXI Slave Bridge is responsible for:
1. Receiving axi_transaction_items from xtor
2. Converting axi_transaction_items to TlmPayload
3. Sending TlmPayload to Proxy
4. Receiving TlmPayload from Proxy
5. Converting TlmPayload to axi_transaction_items
6. Sending axi_transaction_items to xtor

## Key Components

### 1. <PERSON><PERSON><PERSON><PERSON>

Responsible for packing AXI transaction data into TlmPayload format:

```cpp
class DataPacker {
public:
    // Pack AXI write data into TlmPayload
    void PackWriteData(const axi_transaction_item& axi_trans, TlmPayload& tlm_payload);
    
    // Pack AXI read request into TlmPayload
    void PackReadRequest(const axi_transaction_item& axi_trans, TlmPayload& tlm_payload);
};
```

The DataPacker implements the following key features:

1. **Write Data Handling**:
   - Extracts valid data from multi-beat AXI transactions
   - Handles narrow transfers and unaligned addresses
   - Maintains proper byte enables for valid data
   - Creates continuous data stream in TLM payload

2. **Address Alignment**:
   - Uses AxiBurstResolver for address calculations
   - Handles all burst types (FIXED, INCR, WRAP)
   - Supports unaligned first beat with proper data placement

3. **Burst Support**:
   - Processes multiple beats in a single transaction
   - Calculates correct byte lanes for each beat
   - Maintains data continuity across beats

4. **Read Request Handling**:
   - Sets appropriate data length for read operations
   - Calculates total transfer size based on burst length and size

5. **Memory Management**:
   - Allocates appropriate buffer sizes for write data
   - Manages byte enable arrays for write operations
   - Efficient data copying with proper alignment

### 2. DataBuilder

Responsible for building AXI transaction responses from TlmPayload:

```cpp
class DataBuilder {
public:
    // Build AXI write response from TlmPayload (only response status)
    void BuildWriteResponse(const TlmPayload& tlm_payload,
                          axi_transaction_item& axi_trans);
    
    // Build AXI read data response from TlmPayload
    // Handles multi-beat transfers with proper data alignment and byte enables
    void BuildReadResponse(const TlmPayload& tlm_payload,
                          axi_transaction_item& axi_trans);
    
private:
    // Convert TLM response status to AXI response
    uint8_t ConvertTlmResponseToAxi(uint8_t tlm_response);
    
    // Generate AXI response status
    void GenerateAxiResponse(const TlmPayload& tlm_payload,
                           axi_transaction_item& axi_trans);
};
```

The DataBuilder implements the following key features:

1. **Multi-beat Transfer Support**:
   - Handles multiple data beats in a single transaction
   - Correctly sizes output buffers based on burst length and data bus width
   - Maintains proper data alignment across beats

2. **Address Alignment**:
   - Calculates aligned addresses for each beat
   - Handles unaligned first beat with proper data placement
   - Ensures subsequent beats are aligned according to AXI spec

3. **Narrow Transfer Support**:
   - Supports transfers smaller than data bus width
   - Calculates correct byte lanes based on transfer size and address
   - Sets appropriate byte enables for valid data bytes

4. **Burst Type Handling**:
   - FIXED: Maintains same address for all beats
   - INCR: Increments address by transfer size for each beat
   - WRAP: Implements address wrapping at boundary based on burst size

5. **Data Organization**:
   - Places data at correct byte lanes within each beat
   - Zero-fills unused bytes in the data bus
   - Sets byte enables to indicate valid data bytes

6. **Response Generation**:
   - Converts TLM response status to AXI response codes
   - Handles various response types (OKAY, EXOKAY, SLVERR, DECERR)

### 3. TransactionManager

Manages transaction lifecycle and mapping:

```cpp
class TransactionManager {
public:
    /**
     * @brief Register a new transaction
     * 
     * @param axi_trans Original transaction to register
     * @return Index in the ring buffer where transaction is stored
     */
    size_t RegisterTransaction(const axi_transaction_item& axi_trans);
    
    /**
     * @brief Get next transaction to be completed
     * 
     * @return Pointer to the transaction at head of queue
     */
    axi_transaction_item* GetNextTransaction();
    
    /**
     * @brief Complete transaction and cleanup resources
     */
    void CompleteTransaction();

private:
    std::vector<axi_transaction_item> trans_pool_;     ///< Pool of pre-allocated transactions
    std::vector<axi_transaction_item*> transactions_;  ///< Ring buffer of pointers to active transactions
};
```

The TransactionManager implements the following key features:

1. **Memory Efficient Transaction Storage**:
   - Uses a pre-allocated pool of transaction objects
   - Fixed size ring buffer (default 1024 entries)
   - Minimizes memory allocation/deallocation
   - Efficient memory reuse pattern

2. **FIFO Transaction Processing**:
   - Maintains transaction order
   - Ensures responses are processed in order
   - Handles outstanding transactions tracking

3. **Transaction Lifecycle**:
   - Registers new transactions with control information only
   - Maintains original transaction IDs
   - Tracks transaction completion
   - Automatically manages transaction cleanup

4. **Resource Management**:
   - Fixed memory footprint
   - Thread-safe operation
   - Automatic cleanup on completion
   - Efficient memory reuse

5. **Integration Points**:
   - Works with DataBuilder for data buffer management
   - Maintains transaction state for bridge
   - Provides transaction lookup for responses
   - Ensures transaction ordering

### 4. AxiSlaveBridge

The core component that orchestrates the AXI slave bridge functionality:

```cpp
class AxiSlaveBridge {
public:
    /**
     * @brief Construct a new AxiSlaveBridge
     * 
     * @param proxy Pointer to the AXI slave proxy
     * @param req_channel_name Name of the request IPC channel
     * @param resp_channel_name Name of the response IPC channel
     */
    AxiSlaveBridge(umi_axi::axi_slave_proxy* proxy,
                   const std::string& req_channel_name,
                   const std::string& resp_channel_name);

    /**
     * @brief Initialize bridge and start IPC communication
     * 
     * Registers callback with proxy and initializes IPC channels
     */
    void Initialize();

    /**
     * @brief Callback function for handling AXI transactions from proxy
     * 
     * This function is registered with the proxy and called when new
     * AXI transactions arrive from the master.
     * 
     * @param trans Pointer to AXI transaction from master
     */
    void HandleAxiTransaction(void* trans);

    /**
     * @brief Handle response from proxy through IPC
     * 
     * @param tlm_payload TLM response payload from proxy
     * @param trans_id Transaction ID from previous request
     */
    void HandleResponse(const TlmPayload& tlm_payload, uint64_t trans_id);

private:
    DataPacker data_packer_;
    DataBuilder data_builder_;
    TransactionManager trans_manager_;
    umi_axi::axi_slave_proxy* proxy_;  // Owned by external component
    
    ipc::channel req_channel_;   // Channel for sending requests to proxy
    ipc::channel resp_channel_;  // Channel for receiving responses from proxy

    /**
     * @brief Process write request from AXI master
     * 
     * @param axi_trans AXI write transaction from master
     * @return Response transaction ID for later completion
     */
    uint64_t ProcessWriteRequest(const axi_transaction_item& axi_trans);

    /**
     * @brief Process read request from AXI master
     * 
     * @param axi_trans AXI read transaction from master
     * @return Response transaction ID for later completion
     */
    uint64_t ProcessReadRequest(const axi_transaction_item& axi_trans);

    /**
     * @brief Send TLM payload to proxy through IPC
     * 
     * @param tlm_payload Payload to send
     * @throw std::runtime_error if send fails
     */
    void SendToProxy(const TlmPayload& tlm_payload);

    /**
     * @brief Start response receiver thread
     * 
     * Starts a thread to receive responses from proxy through IPC
     */
    void StartResponseReceiver();
};
```

The AxiSlaveBridge implements the following key features:

1. **Proxy Integration**:
   - Registers callback with axi_slave_proxy for transaction handling
   - Receives AXI transactions through callback mechanism
   - Maintains proxy connection state

2. **IPC Communication**:
   - Uses separate channels for requests and responses
   - Implements zero-copy transfer for large payloads
   - Handles channel disconnection and recovery
   - Thread-safe communication handling

3. **Transaction Flow**:
   Write Transaction Flow:
   ```
   1. Master sends write request to proxy
   2. Proxy calls HandleAxiTransaction callback
   3. Bridge processes transaction and sends TLM payload through IPC
   4. Proxy processes request and sends response through IPC
   5. Bridge receives response and updates transaction
   6. Bridge sends response back to master through proxy
   ```

   Read Transaction Flow:
   ```
   1. Master sends read request to proxy
   2. Proxy calls HandleAxiTransaction callback
   3. Bridge processes request and sends TLM payload through IPC
   4. Proxy processes request and sends data through IPC
   5. Bridge receives data and builds response
   6. Bridge sends response back to master through proxy
   ```

4. **Error Handling**:
   - Validates all incoming requests
   - Handles proxy communication errors
   - Reports protocol violations to master
   - Ensures proper error recovery

5. **Resource Management**:
   - Manages transaction lifecycle
   - Handles memory allocation/deallocation
   - Ensures proper cleanup on errors

6. **Performance Considerations**:
   - Minimizes data copying
   - Efficient transaction tracking
   - Optimized response handling
   - Thread-safe operation

7. **Integration Points**:
   - Interfaces with AXI master through transaction items
   - Communicates with proxy through TLM payloads
   - Uses support components for data handling
   - Manages transaction state

8. **Configuration**:
   - Bus parameters from axi_slave_proxy:
     - Data bus width (data_width)
     - Burst length width (len_width)
     - ID width (wid_width, rid_width)
     - Address width (addr_width)
     - User width (user_width)
   - Error reporting levels and destinations
   - Debug logging options

## Transaction Flow

### Write Transaction

1. **Request Path**:
   ```
   xtor -> axi_transaction_item -> DataPacker -> TlmPayload -> Proxy
   ```

2. **Response Path**:
   ```
   Proxy -> TlmPayload -> DataBuilder -> axi_transaction_item -> xtor
   ```

### Read Transaction

1. **Request Path**:
   ```
   xtor -> axi_transaction_item -> DataPacker -> TlmPayload -> Proxy
   ```

2. **Response Path**:
   ```
   Proxy -> TlmPayload -> DataBuilder -> axi_transaction_item -> xtor
   ```

## Narrow Transfer Support

1. **Write Transfers**:
   - Pack partial write data with correct byte enables
   - Handle unaligned addresses

2. **Read Transfers**:
   - Generate correct byte enables for partial reads
   - Handle unaligned addresses
   - Generate the correct response data for narrow reads

## Error Handling

1. **AXI Protocol Errors**:
   - Invalid burst types
   - Unsupported sizes
   - Address unalignment errors for wrap burst

2. **System Errors**:
   - IPC communication failures
   - Memory allocation failures
   - Resource exhaustion

## Performance Optimization

1. **Memory Management**:
   - Reuse transaction objects
   - Minimize data copying
   - Pool allocators for common sizes

2. **Burst Handling**:
   - Efficient burst unpacking
   - Optimized byte enable generation
   - Smart response building

## Testing Requirements

1. **Protocol Compliance**:
   - AXI4 protocol rules
   - Burst support

2. **Error Cases**:
   - Protocol violations
   - System errors
   - Resource limits

3. **Performance**:
   - Throughput measurement
   - Latency tracking
   - Resource usage

## Future Extensions

1. **Protocol Support**:
   - AXI5 features
   - Custom extensions
   - Debug features

2. **Performance**:
   - Zero-copy optimization
   - Smart buffering 