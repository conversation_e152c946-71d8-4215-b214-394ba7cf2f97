# AXI Slave Design Specification

## IPC Design

### IPC Implementation Choice

Based on comprehensive performance testing comparing different IPC mechanisms, we have chosen to use cpp-ipc as our primary IPC implementation for the following reasons:

1. **Lower Latency**: Performance tests show that cpp-ipc consistently provides lower latency compared to boost::interprocess::message_queue.

2. **Smart Memory Management**:
   - For small messages: Uses heap memory allocation
   - For large messages: Automatically uses shared memory pointers
   - This hybrid approach reduces unnecessary data copying, especially beneficial for large AXI transactions

3. **Zero-Copy Support**: 
   - When handling large payloads (like AXI burst transfers), cpp-ipc can return direct shared memory pointers
   - Eliminates need for extra memory copies between processes
   - Particularly efficient for DMA and burst transfers

4. **Flexible Message Sizes**:
   - Supports variable-length data efficiently
   - No need to pre-define maximum message size
   - Automatically handles different payload sizes (64B to 4KB+)

### Channel Design

The IPC system will use two channels:
1. Request Channel: For sending AXI transaction requests
2. Response Channel: For receiving transaction responses

Each channel will be implemented using cpp-ipc's channel mechanism with the following features:
- Lock-free queue for high performance
- Zero-copy data transfer for large payloads
- Automatic memory management

### Message Format

Transaction messages will contain:
```cpp
struct TlmPayload {
    uint64_t id;                // Payload ID
    uint8_t command;           // Command type
    uint64_t address;           // Target address
    uint32_t data_length;       // Length of data
    uint32_t byte_enable_length;// Length of byte enable
    uint32_t streaming_width;   // streaming width
    uint8_t response;
    char data[];               // Variable length data followed by byte enable
};
```

Proxy:
- For READ command, data is null when sent. data is valid in response.
- For WRITE command, data is valid when sent. data is null in response.

Bridge:
- For READ command, data is null when received. data is valid in response.
- For WRITE command, data is valid when received. data is null in response.

### Performance Characteristics

Based on performance testing:
- Latency: Consistently lower than message queue implementation
- Support for variable data sizes (64B - 4KB) without performance degradation
- Efficient handling of both small and large transfers
- Minimal CPU overhead due to zero-copy design

### Error Handling

The IPC implementation will include:
1. Robust error detection and reporting
2. Automatic cleanup of shared resources
3. Graceful handling of process disconnection
4. Recovery mechanisms for communication failures

## Memory Management

### Shared Memory Strategy

- Use cpp-ipc's built-in memory management for optimal performance
- Leverage zero-copy capabilities for large transfers
- Automatic memory pool management for variable-size allocations

### Buffer Management

- Dynamic buffer sizing based on transaction requirements
- Efficient memory reuse through pooling
- Zero-copy transfers when possible
- Smart pointer management for safe memory handling

## Implementation Guidelines

1. Use cpp-ipc's channel API for all IPC communications
2. Implement zero-copy transfers for large payloads
3. Utilize shared memory pointers for data transfers where possible
4. Maintain separate request and response channels
5. Implement proper cleanup and resource management
6. Add comprehensive error handling and recovery mechanisms

## Testing Requirements

1. Performance testing with various payload sizes (64B to 4KB)
2. Stress testing with high-frequency transactions
3. Error handling and recovery testing
4. Memory leak testing
5. Process crash recovery testing

## CPP-IPC Usage Guidelines

### Basic Channel Setup

```cpp
// Producer side
ipc::channel req_channel{channel_name, ipc::sender};
ipc::channel resp_channel{channel_name + "_resp", ipc::receiver};

// Consumer side
ipc::channel req_channel{channel_name, ipc::receiver};
ipc::channel resp_channel{channel_name + "_resp", ipc::sender};
```

### Channel Synchronization

1. **Connection Establishment**:
```cpp
// Wait for consumer/producer to connect
req_channel.wait_for_recv(1);  // Wait for receiver
resp_channel.wait_for_recv(1); // Wait for response channel
```

### Data Transfer

1. **Sending Data**:
```cpp
// Send with size
bool success = channel.send(buffer.data(), buffer_size);

// Receive data
auto recv_data = channel.recv();  // Returns ipc::buff_t
if (!recv_data.empty()) {
    // Process data
    auto* payload = reinterpret_cast<const TlmPayload*>(recv_data.data());
}
```

### Important Considerations

1. **Memory Management**:
   - For small messages (<1KB): Data is allocated on heap
   - For large messages (>1KB): Data is automatically allocated in shared memory
   - Received data pointer is valid until next receive operation

2. **Buffer Handling**:
   - No need to pre-allocate fixed size buffers
   - Received buffer (ipc::buff_t) automatically manages memory
   - Zero-copy is automatically used for large messages

3. **Error Handling**:
   - Check send() return value for success
   - Check received buffer size before accessing
   - Handle disconnect scenarios gracefully

4. **Resource Cleanup**:
```cpp
// Cleanup channels
channel.disconnect();  // Automatically cleans up resources

// Remove shared memory (if needed)
ipc::shm::remove(channel_name);
```

### Best Practices

1. **Channel Naming**:
   - Use unique names for each channel pair
   - Add suffixes for request/response channels
   - Example: "axi_master_0_req", "axi_master_0_resp"

2. **Performance Optimization**:
   - Minimize message sizes for small transfers
   - Use zero-copy for large transfers (happens automatically)
   - Batch small messages when possible

3. **Error Recovery**:
   - Implement reconnection logic for lost connections
   - Handle process crashes gracefully
   - Clean up shared resources on exit

4. **Thread Safety**:
   - Each channel should be accessed by single thread
   - Use separate channels for different threads
   - Avoid sharing channel objects between threads

### Example Usage Pattern

```cpp
// Producer implementation
class Producer {
public:
    void run() {
        ipc::channel req_channel{"axi_master", ipc::sender};
        ipc::channel resp_channel{"axi_master_resp", ipc::receiver};
        
        // Wait for consumer
        req_channel.wait_for_recv(1);
        resp_channel.wait_for_recv(1);
        
        // Send data
        TlmPayload payload{/*...*/};
        if (!req_channel.send(&payload, sizeof(payload) + payload.data_length)) {
            // Handle send error
        }
        
        // Receive response
        auto resp = resp_channel.recv();
        if (!resp.empty()) {
            // Process response
        }
    }
};

// Consumer implementation
class Consumer {
public:
    void run() {
        ipc::channel req_channel{"axi_master", ipc::receiver};
        ipc::channel resp_channel{"axi_master_resp", ipc::sender};
        
        while (running_) {
            // data is valid in this scope only
            auto data = req_channel.recv();
            if (data.empty()) continue;
            
            auto* payload = reinterpret_cast<const TlmPayload*>(data.data());
            // Process payload...
            
            // Send response
            int response = 1;
            resp_channel.send(&response, sizeof(response));
        }
    }
private:
    std::atomic<bool> running_{true};
};
```

### Common Pitfalls to Avoid

1. **Resource Leaks**:
   - Always call disconnect() when done
   - Clean up shared memory if no longer needed
   - Don't forget to remove channels on abnormal exit

2. **Buffer Management**:
   - Don't store received buffer pointers
   - Process received data before next receive
   - Don't assume fixed message sizes

3. **Error Handling**:
   - Don't ignore send() return values
   - Check received buffer size
   - Handle disconnection scenarios

4. **Performance**:
   - Don't copy large messages unnecessarily
   - Don't create/destroy channels frequently
   - Don't share channels between threads

## AXI Slave Proxy Design

### Overview

The AXI Slave Proxy is a TLM Initiator that acts as a bridge between SystemC/TLM(target) and the AXI Slave Bridge process. It implements the TLM backward interface and converts between TlmPayload and tlm_generic_payload formats.

Key responsibilities:
1. Receive TlmPayload from Bridge via IPC
2. Convert TlmPayload to tlm_generic_payload
3. Send tlm_generic_payload to TLM target
4. Receive tlm_generic_payload response from target
5. Convert tlm_generic_payload back to TlmPayload
6. Send TlmPayload response to Bridge via IPC

### Class Structure

```cpp
class AxiSlaveTlmProxy : public sc_core::sc_module,
                        public virtual tlm::tlm_bw_transport_if<> {
public:
    // Constructor takes channel name
    explicit AxiSlaveTlmProxy(sc_core::sc_module_name name,
                             const std::string& channel_name = "axi_slave");

    // TLM initiator socket for sending transactions to target
    tlm::tlm_initiator_socket<> init_socket;

    // TLM backward interface implementation
    void invalidate_direct_mem_ptr(uint64_t start_range,
                                 uint64_t end_range) override;
                                 
    tlm::tlm_sync_enum nb_transport_bw(tlm::tlm_generic_payload& trans,
                                      tlm::tlm_phase& phase,
                                      sc_core::sc_time& delay) override;

private:
    // IPC channels
    ipc::channel req_channel_;   // For receiving requests from bridge
    ipc::channel resp_channel_;  // For sending responses to bridge

    // Transaction management
    std::unique_ptr<TransactionManager> transaction_manager_;
    
    // SPSC queue for passing data from cpp thread to sc thread
    moodycamel::ReaderWriterQueue<std::unique_ptr<TlmPayload>> request_queue_;
    
    // Async event for notifying sc thread
    async_event request_event_;
    
    // Threads
    std::unique_ptr<std::thread> ipc_thread_;  // cpp thread for IPC
    void IpcThread();                          // IPC thread function
    
    // SystemC method process
    void ProcessRequest();                     // SC thread for processing requests
    
    // Conversion utilities
    void ConvertToTlmGenericPayload(const TlmPayload& ipc_payload,
                                   tlm::tlm_generic_payload& tlm_payload);
    void ConvertToTlmPayload(const tlm::tlm_generic_payload& tlm_payload,
                            TlmPayload& ipc_payload);
                            
    // Cleanup and error handling
    void Cleanup();
    void HandleError(const std::string& error_msg);
    
    // Thread control
    std::atomic<bool> running_{false};
};
```

### Transaction Flow

We have two approaches for handling data transfer between the CPP thread (IPC) and SC thread:

#### Approach 1: Ring Buffer with MoodyCamel

This approach uses BlockingReaderWriterCircularBuffer for thread communication:

```cpp
class AxiSlaveTlmProxy {
private:
    // Wrapper for TlmPayload with fixed buffer
    struct PayloadWrapper {
        static constexpr size_t MAX_DATA_SIZE = 4096;
        
        PayloadWrapper() : data(new char[sizeof(TlmPayload) + MAX_DATA_SIZE]) {}
        ~PayloadWrapper() { delete[] data; }
        
        // Non-copyable but movable
        PayloadWrapper(const PayloadWrapper&) = delete;
        PayloadWrapper& operator=(const PayloadWrapper&) = delete;
        PayloadWrapper(PayloadWrapper&&) = default;
        PayloadWrapper& operator=(PayloadWrapper&&) = default;
        
        TlmPayload* payload() { return reinterpret_cast<TlmPayload*>(data); }
        const TlmPayload* payload() const { 
            return reinterpret_cast<const TlmPayload*>(data); 
        }
        
    private:
        char* data;
    };
    
    // Ring buffer for thread communication
    moodycamel::BlockingReaderWriterCircularBuffer<PayloadWrapper> request_queue_;
    async_event request_event_;
    
    // CPP Thread implementation
    void IpcThread() {
        while (running_) {
            auto data = req_channel_.recv();
            if (data.empty()) continue;
            
            PayloadWrapper wrapper;
            auto* payload = wrapper.payload();
            // Copy header
            std::memcpy(payload, data.data(), sizeof(TlmPayload));
            // Copy data if present
            if (payload->data_length > 0 || payload->byte_enable_length > 0) {
                std::memcpy(payload->data, 
                           static_cast<const char*>(data.data()) + sizeof(TlmPayload),
                           payload->data_length + payload->byte_enable_length);
            }
            
            request_queue_.wait_enqueue(std::move(wrapper));
            request_event_.notify();
        }
    }
    
    // SC Thread implementation
    void ProcessRequest() {
        PayloadWrapper wrapper;
        
        while (true) {
            wait(request_event_);
            
            while (request_queue_.try_dequeue(wrapper)) {
                const TlmPayload* payload = wrapper.payload();
                tlm::tlm_generic_payload trans;
                ConvertToTlmGenericPayload(*payload, trans);
                
                // Register transaction
                transaction_manager_->RegisterTransaction(payload->id, &trans);
                
                // Process transaction...
               sc_core::sc_time delay = sc_core::SC_ZERO_TIME;
               init_socket->b_transport(trans, delay);
               
               // Handle response
               TlmPayload response;
               ConvertToTlmPayload(trans, response);
               resp_channel_.send(&response, sizeof(response) + 
                                response.data_length + response.byte_enable_length);
               
               // Cleanup transaction
               transaction_manager_->RemoveTransaction(ipc_payload->id);
            }
        }
    }
};
```

**Advantages**:
1. Decoupled thread communication
2. Can handle burst requests
3. Built-in thread synchronization
4. Memory reuse through ring buffer
5. No need for explicit synchronization between threads

**Disadvantages**:
1. One memory copy required
2. Fixed pre-allocation of maximum size buffers
3. Slightly more complex implementation

#### Approach 2: Direct Notification

This approach uses direct notification between threads:

```cpp
class AxiSlaveTlmProxy {
private:
    // IPC data storage
    const void* current_data_{nullptr};  // Points to IPC received data
    std::atomic<bool> data_processed_{true};
    async_event request_event_;  // For notifying SC thread
    
    // For notifying CPP thread
    std::mutex mutex_;
    std::condition_variable processed_cv_;
    
    // CPP Thread implementation
    void IpcThread() {
        while (running_) {
            auto data = req_channel_.recv();
            if (data.empty()) continue;
            
            // Wait for previous data to be processed
            {
                std::unique_lock<std::mutex> lock(mutex_);
                processed_cv_.wait(lock, [this] {
                    return data_processed_.load(std::memory_order_acquire);
                });
            }
            
            // Set new data and notify SC thread
            current_data_ = data.data();
            data_processed_.store(false, std::memory_order_release);
            request_event_.notify();
            
            // Wait for SC thread to process the data
            {
                std::unique_lock<std::mutex> lock(mutex_);
                processed_cv_.wait(lock, [this] {
                    return data_processed_.load(std::memory_order_acquire);
                });
            }
        }
    }
    
    // SC Thread implementation
    void ProcessRequest() {
        while (true) {
            wait(request_event_);
            
            if (!data_processed_.load(std::memory_order_acquire)) {
                auto* payload = reinterpret_cast<const TlmPayload*>(current_data_);
                tlm::tlm_generic_payload trans;
                ConvertToTlmGenericPayload(*payload, trans);
                
                // Process transaction...
                init_socket->b_transport(trans, delay);
                
                // Mark data as processed and notify CPP thread
                data_processed_.store(true, std::memory_order_release);
                processed_cv_.notify_one();
            }
        }
    }
};
```

Another choice is use double buffer to avoid lock.

```cpp
class AxiSlaveTlmProxy {
private:
    // 使用双缓冲区
    struct DataBuffer {
        const void* data{nullptr};
        std::atomic<bool> processed{true};
    };
    std::array<DataBuffer, 2> buffers_;
    std::atomic<size_t> current_buffer_{0};
    async_event request_event_;

    void IpcThread() {
        while (running_) {
            auto data = req_channel_.recv();
            if (data.empty()) continue;
            
            // 找到一个可用的缓冲区
            size_t buf_idx = current_buffer_.load(std::memory_order_acquire);
            size_t next_buf = (buf_idx + 1) % 2;
            
            // 等待下一个缓冲区可用
            while (!buffers_[next_buf].processed.load(std::memory_order_acquire)) {
                std::this_thread::yield();
            }
            
            // 设置数据并切换缓冲区
            buffers_[next_buf].data = data.data();
            buffers_[next_buf].processed.store(false, std::memory_order_release);
            current_buffer_.store(next_buf, std::memory_order_release);
            
            request_event_.notify();
        }
    }
    
    void ProcessRequest() {
        while (true) {
            wait(request_event_);
            
            size_t buf_idx = current_buffer_.load(std::memory_order_acquire);
            if (!buffers_[buf_idx].processed.load(std::memory_order_acquire)) {
                auto* payload = reinterpret_cast<const TlmPayload*>(
                    buffers_[buf_idx].data);
                tlm::tlm_generic_payload trans;
                ConvertToTlmGenericPayload(*payload, trans);
                
                // Process transaction...
                init_socket->b_transport(trans, delay);
                
                // 标记处理完成
                buffers_[buf_idx].processed.store(true, std::memory_order_release);
            }
        }
    }
};
```


**Advantages**:
1. Zero-copy for IPC data (big data)
2. Simpler implementation
3. Lower memory usage
4. Direct data access

**Disadvantages**:
1. Tighter coupling between threads
2. Cannot handle burst requests
3. CPP thread must wait for SC thread to complete processing

#### Comparison

For the current use case where:
- Maximum payload size is 4KB
- Typical payload size is 64/128 bytes
- SystemC is single-threaded

recommend using **Approach 1 (Ring Buffer)** because:
1. The memory copy overhead for 128-byte payloads is minimal
2. The decoupled design provides better scalability
3. The ring buffer implementation is well-tested (MoodyCamel)
4. Can handle burst requests efficiently
5. Memory pre-allocation is acceptable given the fixed maximum size


3. **Transaction Management**:
   ```cpp
   class TransactionManager {
   public:
       // Register transaction with memory management
       void RegisterTransaction(uint64_t id, tlm::tlm_generic_payload* trans) {
           std::lock_guard<std::mutex> lock(mutex_);
           transactions_[id] = TransactionContext{
               trans,
               trans->get_data_ptr(),
               trans->get_byte_enable_ptr()
           };
       }
       
       // Clean up transaction and its memory
       void RemoveTransaction(uint64_t id) {
           std::lock_guard<std::mutex> lock(mutex_);
           auto it = transactions_.find(id);
           if (it != transactions_.end()) {
               delete[] it->second.data_ptr;
               delete[] it->second.byte_enable_ptr;
               transactions_.erase(it);
           }
       }
       
   private:
       struct TransactionContext {
           tlm::tlm_generic_payload* trans;
           uint8_t* data_ptr;
           uint8_t* byte_enable_ptr;
       };
       
       std::unordered_map<uint64_t, TransactionContext> transactions_;
       std::mutex mutex_;
   };
   ```

### Memory Management

1. **TLM Payload Lifecycle**:
   - Data pointers allocated in ConvertToTlmGenericPayload
   - Tracked by TransactionManager
   - Cleaned up after response is sent
   - RAII principles applied throughout

2. **IPC Payload Handling**:
   - Unique pointers used in queue
   - Automatic cleanup when pointers go out of scope
   - Zero-copy when possible for large transfers

3. **Queue Management**:
   - Fixed-size SPSC queue for predictable memory usage
   - Non-blocking operations for better performance
   - Yield strategy for queue full conditions

### Payload Conversion

1. **TlmPayload to tlm_generic_payload**:
   ```cpp
   void ConvertToTlmGenericPayload(const TlmPayload& src, 
                                  tlm::tlm_generic_payload& dest) {
       // Set command
       dest.set_command(static_cast<tlm::tlm_command>(src.command));
       
       // Set address
       dest.set_address(src.address);
       
       // Allocate and copy data if needed
       if (src.data_length > 0) {
           uint8_t* data = new uint8_t[src.data_length];
           std::memcpy(data, src.data, src.data_length);
           dest.set_data_ptr(data);
           dest.set_data_length(src.data_length);
       }
       
       // Handle byte enables if present
       if (src.byte_enable_length > 0) {
           uint8_t* be = new uint8_t[src.byte_enable_length];
           std::memcpy(be, src.data + src.data_length, 
                      src.byte_enable_length);
           dest.set_byte_enable_ptr(be);
           dest.set_byte_enable_length(src.byte_enable_length);
       }
       
       dest.set_streaming_width(src.streaming_width);
   }
   ```

2. **tlm_generic_payload to TlmPayload**:
   ```cpp
   void ConvertToTlmPayload(const tlm::tlm_generic_payload& src,
                           TlmPayload& dest) {
       // Copy basic fields
       dest.command = static_cast<uint8_t>(src.get_command());
       dest.address = src.get_address();
       dest.data_length = src.get_data_length();
       dest.byte_enable_length = src.get_byte_enable_length();
       dest.streaming_width = src.get_streaming_width();
       dest.response = static_cast<uint8_t>(src.get_response_status());
       
       // Copy data if present
       if (src.get_data_length() > 0) {
           std::memcpy(dest.data, src.get_data_ptr(), 
                      src.get_data_length());
       }
       
       // Copy byte enables if present
       if (src.get_byte_enable_length() > 0) {
           std::memcpy(dest.data + src.get_data_length(),
                      src.get_byte_enable_ptr(),
                      src.get_byte_enable_length());
       }
   }
   ```

### Error Handling

1. **IPC Communication Errors**:
   - Transaction cleanup on failure

2. **TLM Transaction Errors**:
   - Invalid payload parameters
   - Response status handling
   - Memory allocation failures

3. **Resource Management**:
   - RAII for memory allocation
   - Proper cleanup of transaction data
   - Channel cleanup on shutdown

### Performance Optimization

1. **Memory Management**:
   - Zero-copy for large data transfers
   - Memory pool for frequent allocations
   - Smart pointer usage for safety

2. **Transaction Processing**:
   - Efficient payload conversion
   - Minimal data copying
   - Batch processing when possible

### Testing Requirements

1. **Functional Testing**:
   - Payload conversion accuracy
   - Transaction flow correctness
   - Error handling verification

2. **Performance Testing**:
   - Latency measurements
   - Throughput benchmarks
   - Memory usage monitoring

3. **Integration Testing**:
   - End-to-end transaction flow
   - SystemC simulation integration
   - Bridge communication verification

### Implementation Guidelines

1. Follow Google C++ Style Guide
2. Use C++17 features appropriately
3. Implement comprehensive logging
4. Add performance monitoring
5. Maintain thread safety
6. Document public interfaces

---


### Implementation Approaches


## Bridge Implementation

### Overview

The AXI Slave Bridge is responsible for:
1. Receiving axi_transaction_items from xtor
2. convert axi_transaction_items to TlmPayload
3. send TlmPayload to Proxy
4. receive TlmPayload from Proxy
5. convert TlmPayload to axi_transaction_items
6. send axi_transaction_items to xtor


In Order to support narrow transfer, we need one DataPacker to get the valid data from axi_transaction_items, then form the TlmPayload and send to Proxy.
For response, we need one DataBuilder to form the data in axi_transaction_item's requested format, then send to xtor.

