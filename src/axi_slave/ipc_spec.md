# IPC Design Specification

## Overview

The IPC (Inter-Process Communication) system is designed to facilitate communication between the AXI Slave Proxy and Bridge processes. It provides a reliable, efficient, and thread-safe mechanism for transferring TLM payloads between processes.

## Channel Design

1. **Channel Structure**:
   - Request channel: `axi_slave_req` for Bridge -> Proxy communication
   - Response channel: `axi_slave_resp` for Proxy -> Bridge communication
   - Zero-copy support for large transfers
   - Automatic channel cleanup on process exit

2. **Message Format**:
   ```cpp
   struct TlmPayload {
       uint64_t id;                    // Transaction ID
       uint64_t address;               // Target address
       uint8_t command;                // TLM command (read/write)
       uint32_t data_length;           // Length of data
       uint32_t byte_enable_length;    // Length of byte enables
       uint32_t axuser_length;         // Length of axuser
       uint32_t xuser_length;          // Length of xuser
       uint32_t streaming_width;       // Streaming width
       uint8_t response;               // Response status
       uint8_t data[];                 // Flexible array for data and byte enables
   };
   ```

3. **Performance Optimization**:
   - Minimize message sizes for small transfers
   - Use zero-copy for large transfers (happens automatically)
   - Batch small messages when possible

4. **Error Recovery**:
   - Implement reconnection logic for lost connections
   - Handle process crashes gracefully
   - Clean up shared resources on exit

5. **Thread Safety**:
   - Each channel should be accessed by single thread
   - Use separate channels for different threads
   - Avoid sharing channel objects between threads

## Example Usage Pattern

```cpp
// Producer implementation
class Producer {
public:
    void run() {
        ipc::channel req_channel{"axi_slave_req", ipc::sender};
        ipc::channel resp_channel{"axi_slave_resp", ipc::receiver};
        
        // Wait for consumer
        req_channel.wait_for_recv(1);
        resp_channel.wait_for_recv(1);
        
        // Send data
        TlmPayload payload{/*...*/};
        if (!req_channel.send(&payload, sizeof(payload) + payload.data_length)) {
            // Handle send error
        }
        
        // Receive response
        auto resp = resp_channel.recv();
        if (!resp.empty()) {
            // Process response
        }
    }
};

// Consumer implementation
class Consumer {
public:
    void run() {
        ipc::channel req_channel{"axi_slave_req", ipc::receiver};
        ipc::channel resp_channel{"axi_slave_resp", ipc::sender};
        
        while (running_) {
            // data is valid in this scope only
            auto data = req_channel.recv();
            if (data.empty()) continue;
            
            auto* payload = reinterpret_cast<const TlmPayload*>(data.data());
            // Process payload...
            
            // Send response
            TlmPayload response{/*...*/};
            resp_channel.send(&response, sizeof(response));
        }
    }
private:
    std::atomic<bool> running_{true};
};
```

## Common Pitfalls to Avoid

1. **Resource Leaks**:
   - Always call disconnect() when done
   - Clean up shared memory if no longer needed
   - Don't forget to remove channels on abnormal exit

2. **Buffer Management**:
   - Don't store received buffer pointers
   - Process received data before next receive
   - Don't assume fixed message sizes

3. **Error Handling**:
   - Don't ignore send() return values
   - Check received buffer size
   - Handle disconnection scenarios

4. **Performance**:
   - Don't copy large messages unnecessarily
   - Don't create/destroy channels frequently
   - Don't share channels between threads 