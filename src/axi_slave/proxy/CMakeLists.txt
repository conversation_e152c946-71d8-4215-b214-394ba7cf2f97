
# Collect source files
file(GLOB_RECURSE SOURCES src/*.cc)
file(GLOB_RECURSE HEADERS include/*.h)

# Create shared library
add_library(axi_slave_tlm_proxy ${SOURCES})

# Set include directories
target_include_directories(axi_slave_tlm_proxy
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}/include
        $ENV{UMICOM_HOME}/include
        $ENV{UMICOM_HOME}/include/systemc
)

# Add dependencies
target_link_libraries(axi_slave_tlm_proxy
    PUBLIC
        common
        dispatcher
        #axi_common
        sslogger
)

# Install library
install(TARGETS axi_slave_tlm_proxy
    LIBRARY DESTINATION ${CMAKE_INSTALL_PREFIX}/${LIB_VERSION}
    ARCHIVE DESTINATION ${CMAKE_INSTALL_PREFIX}/${LIB_VERSION}
)


# Add tests subdirectory if tests exist
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/tests)
    add_subdirectory(tests EXCLUDE_FROM_ALL)
endif() 