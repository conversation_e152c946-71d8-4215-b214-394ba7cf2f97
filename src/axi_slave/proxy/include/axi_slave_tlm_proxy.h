#ifndef SSLN_HYBRID_AXI_SLAVE_TLM_PROXY_H_
#define SSLN_HYBRID_AXI_SLAVE_TLM_PROXY_H_

#include <atomic>
#include <memory>
#include <string>
#include <vector>

#include <systemc>
#include <tlm>

#include "async_event.h"
#include "readerwritercircularbuffer.h"
#include "tlm_payload.h"
#include "ssln/sslogger.h"
#include "ssln_profiling.h"
#include "quill/std/Vector.h"
#include "dispatcher/include/dispatcher.h"
#include "dispatcher/include/i_endpoint.h"


namespace ssln {
namespace hybrid {
namespace axi {

class AxiSlaveTlmProxy;
/**
 *@uv_class_name AxiSlaveTlmProxy
 */
class AxiSlaveTlmProxy : public sc_core::sc_module,
                        public virtual tlm::tlm_bw_transport_if<>,
                        public IEndpoint {
public:
    // Constructor
    explicit AxiSlaveTlmProxy(sc_core::sc_module_name name,
                             Dispatcher* dispatcher,
                             uint32_t comp_id,
                             uint32_t dest_id);
    
    ~AxiSlaveTlmProxy() override;

    // TLM initiator socket
    tlm::tlm_initiator_socket<32> init_socket;

    // TLM backward-path non-blocking transport implementation
    tlm::tlm_sync_enum nb_transport_bw(
        tlm::tlm_generic_payload& trans,
        tlm::tlm_phase& phase,
        sc_core::sc_time& delay) override;

    void invalidate_direct_mem_ptr(
        sc_dt::uint64 start_range,
        sc_dt::uint64 end_range) override;

    // IEndpoint interface implementation
    bool handle_data(const void* data, size_t size) override;
    uint32_t get_component_id() const override;

private:
    // Ring buffer for thread communication
    moodycamel::BlockingReaderWriterCircularBuffer<std::vector<char>> request_queue_;
    
    // Async event for notifying sc thread
    async_event request_event_;
    
    // SystemC method process
    void ProcessRequest();                     // SC thread for processing requests
    
    // Conversion utilities
    void ConvertToTlmGenericPayload(const TlmPayload& ipc_payload,
                                   tlm::tlm_generic_payload& tlm_payload);
    void ConvertToTlmPayload(const tlm::tlm_generic_payload& tlm_payload,
                            TlmPayload& ipc_payload);
                            
    // Cleanup and error handling
    void HandleError(const std::string& error_msg);
    
    // Dispatcher members
    Dispatcher* dispatcher_;
    uint32_t comp_id_;
    uint32_t dest_id_;

    SslnProfiling* profiling;

    SC_HAS_PROCESS(AxiSlaveTlmProxy);
};

}  // namespace axi
}  // namespace hybrid
}  // namespace ssln

#endif  // SSLN_HYBRID_AXI_SLAVE_TLM_PROXY_H_