#ifndef SSLN_HYBRID_AXI_TRANSACTION_MANAGER_H_
#define SSLN_HYBRID_AXI_TRANSACTION_MANAGER_H_

#include <cstdint>
#include <mutex>
#include <unordered_map>

#include <tlm>

namespace ssln {
namespace hybrid {
namespace axi {

class TransactionManager {
public:
    // Register transaction with memory management
    void RegisterTransaction(uint64_t id, tlm::tlm_generic_payload* trans) {
        std::lock_guard<std::mutex> lock(mutex_);
        transactions_[id] = TransactionContext{
            trans,
            trans->get_data_ptr(),
            trans->get_byte_enable_ptr()
        };
    }
    
    // Clean up transaction and its memory
    void RemoveTransaction(uint64_t id) {
        std::lock_guard<std::mutex> lock(mutex_);
        auto it = transactions_.find(id);
        if (it != transactions_.end()) {
            delete[] it->second.data_ptr;
            delete[] it->second.byte_enable_ptr;
            transactions_.erase(it);
        }
    }
    
    // Get transaction by id
    tlm::tlm_generic_payload* GetTransaction(uint64_t id) {
        std::lock_guard<std::mutex> lock(mutex_);
        auto it = transactions_.find(id);
        return it != transactions_.end() ? it->second.trans : nullptr;
    }
    
private:
    struct TransactionContext {
        tlm::tlm_generic_payload* trans;
        uint8_t* data_ptr;
        uint8_t* byte_enable_ptr;
    };
    
    std::unordered_map<uint64_t, TransactionContext> transactions_;
    std::mutex mutex_;
};

}  // namespace axi
}  // namespace hybrid
}  // namespace ssln

#endif  // SSLN_HYBRID_AXI_TRANSACTION_MANAGER_H_ 