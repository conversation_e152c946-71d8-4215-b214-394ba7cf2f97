#include "axi_slave_tlm_proxy.h"

#include <cstring>
#include <stdexcept>
#include "dispatcher/generated/payload_generated.h"

namespace ssln {
namespace hybrid {
namespace axi {

AxiSlaveTlmProxy::AxiSlaveTlmProxy(sc_core::sc_module_name name,
                                   Dispatcher* dispatcher,
                                   uint32_t comp_id,
                                   uint32_t dest_id)
    : sc_module(name),
      init_socket("init_socket"),
      request_queue_(256),
      dispatcher_(dispatcher),
      comp_id_(comp_id),
      dest_id_(dest_id) {
    
    init_socket.bind(*this);
    SC_THREAD(ProcessRequest);
    profiling = new SslnProfiling(this->name());

    // Register this component with the dispatcher
    dispatcher_->RegisterEndpoint(this);
}

AxiSlaveTlmProxy::~AxiSlaveTlmProxy() {
    delete profiling;
}

void AxiSlaveTlmProxy::HandleError(const std::string& error_msg) {
    SC_REPORT_ERROR("AxiSlaveTlmProxy", error_msg.c_str());
    Cleanup();
}

uint32_t AxiSlaveTlmProxy::GetComponentId() const {
    return comp_id_;
}

bool AxiSlaveTlmProxy::HandleData(const void* data, size_t size) {
    SSLN_LOG_INFO(file_logger, "[{}] Received data size: {}", this->name(), size);
    
    std::vector<char> data_vec(static_cast<const char*>(data), static_cast<const char*>(data) + size);

    if (request_queue_.try_enqueue(std::move(data_vec))) {
        request_event_.notify();
        return true;
    } else {
        HandleError("Failed to enqueue payload");
        return false;
    }
}

void AxiSlaveTlmProxy::ProcessRequest() {
    std::vector<char> data;
    
    while (true) {
        wait(request_event_);
        SSLN_LOG_INFO(file_logger, "[{}], Process request start", this->name());
        while (request_queue_.try_dequeue(data)) {
            try {
                auto* fb_payload = flatbuffers::GetRoot<ssln::hybrid::dispatch::Payload>(data.data());

                // Create a temporary TlmPayload to reuse the existing conversion logic
                TlmPayload temp_payload;
                temp_payload.id = fb_payload->id();
                temp_payload.command = fb_payload->command();
                temp_payload.address = fb_payload->address();
                temp_payload.data_length = fb_payload->data_length();
                temp_payload.byte_enable_length = fb_payload->byte_enable_length();
                temp_payload.streaming_width = fb_payload->streaming_width();
                temp_payload.axuser_length = fb_payload->axuser_length();
                temp_payload.xuser_length = fb_payload->xuser_length();
                temp_payload.data = const_cast<uint8_t*>(fb_payload->variable_data()->data());
                
                tlm::tlm_generic_payload trans;
                profiling->add_start_time("ConvertToTlmGenericPayload");
                ConvertToTlmGenericPayload(temp_payload, trans);
                profiling->add_duration_time("ConvertToTlmGenericPayload");
                
                sc_core::sc_time delay = sc_core::SC_ZERO_TIME;
                profiling->add_start_time("b_transport");
                init_socket->b_transport(trans, delay);
                profiling->add_duration_time("b_transport");
                
                // --- Create and send response via FlatBuffers ---
                flatbuffers::FlatBufferBuilder builder;
                
                std::vector<uint8_t> variable_data;
                XuserExtension* xuser_ext = nullptr;
                trans.get_extension(xuser_ext);
                
                uint32_t resp_xuser_len = 0;

                if (trans.is_read()) {
                    variable_data.insert(variable_data.end(), trans.get_data_ptr(), trans.get_data_ptr() + trans.get_data_length());
                }
                if (xuser_ext) {
                    if (trans.is_read()) {
                        resp_xuser_len = xuser_ext->data_length;
                        variable_data.insert(variable_data.end(), xuser_ext->get_data_ptr(), xuser_ext->get_data_ptr() + resp_xuser_len);
                    } else { // Write
                        resp_xuser_len = sizeof(xuser_ext->response);
                        variable_data.insert(variable_data.end(), reinterpret_cast<uint8_t*>(&xuser_ext->response), reinterpret_cast<uint8_t*>(&xuser_ext->response) + resp_xuser_len);
                    }
                }

                auto fb_variable_data = builder.CreateVector(variable_data);

                auto response_payload = ssln::hybrid::dispatch::CreatePayload(
                    builder,
                    dest_id_,
                    fb_payload->id(),
                    static_cast<uint8_t>(trans.get_command()),
                    trans.get_address(),
                    static_cast<int8_t>(trans.get_response_status()),
                    trans.get_streaming_width(),
                    fb_variable_data,
                    trans.get_data_length(),
                    trans.get_byte_enable_length(),
                    0, // axuser_length is not sent back in response
                    resp_xuser_len);
                
                builder.Finish(response_payload);
                dispatcher_->send(dest_id_, builder.GetBufferPointer(), builder.GetSize());
                
                delete [] trans.get_data_ptr();
                delete [] trans.get_byte_enable_ptr();

            } catch (const std::exception& e) {
                HandleError(std::string("Exception in ProcessRequest: ") + e.what());
            }
        }
        SSLN_LOG_INFO(file_logger, "[{}], Process request end", this->name());
    }
}

void AxiSlaveTlmProxy::ConvertToTlmGenericPayload(
    const TlmPayload& src, tlm::tlm_generic_payload& dest) {
    SSLN_LOG_INFO(file_logger, "[{}], Convert to tlm_generic_payload start", this->name());
    // Set command
    dest.set_command(static_cast<tlm::tlm_command>(src.command));
    
    // Set address
    SSLN_LOG_INFO(file_logger, "[{}], address: {}", this->name(), src.address);
    dest.set_address(src.address);
    
    if (src.data_length > 0) {
        uint8_t* data = new uint8_t[src.data_length];
        if (src.command == static_cast<uint8_t>(tlm::TLM_WRITE_COMMAND)) {
            std::memcpy(data, src.data, src.data_length);
        }
        dest.set_data_ptr(data);
        dest.set_data_length(src.data_length);
    }

    if (src.byte_enable_length > 0 && src.command == static_cast<uint8_t>(tlm::TLM_WRITE_COMMAND)) {
        uint8_t* be = new uint8_t[src.byte_enable_length];
        std::memcpy(be, src.data + src.data_length, src.byte_enable_length);
        dest.set_byte_enable_ptr(be);
        dest.set_byte_enable_length(src.byte_enable_length);
    } else {
        dest.set_byte_enable_ptr(nullptr);
        dest.set_byte_enable_length(0); 
    }
    if (src.axuser_length > 0 || src.xuser_length > 0) {
        XuserExtension* xuser_ext = new XuserExtension(src.xuser_length, src.axuser_length);
        if (src.axuser_length > 0) {
            if (src.command == static_cast<uint8_t>(tlm::TLM_WRITE_COMMAND)) {
                std::memcpy(xuser_ext->get_req_ptr(),
                        src.data + src.data_length + src.byte_enable_length,
                        src.axuser_length);
            } else {
                std::memcpy(xuser_ext->get_req_ptr(),
                        src.data,
                        src.axuser_length);
            }

        }
      
        if (src.xuser_length > 0 && src.command == static_cast<uint8_t>(tlm::TLM_WRITE_COMMAND)) {
            std::memcpy(xuser_ext->get_data_ptr(),
                        src.data + src.data_length + src.byte_enable_length + src.axuser_length,
                        src.xuser_length);
        }
        dest.set_extension(xuser_ext);
    }

    dest.set_streaming_width(src.streaming_width);
    SSLN_LOG_INFO(file_logger, "[{}], Convert to tlm_generic_payload end", this->name());
}

void AxiSlaveTlmProxy::ConvertToTlmPayload(
    const tlm::tlm_generic_payload& src, TlmPayload& dest) {
    SSLN_LOG_INFO(file_logger, "[{}], Convert to TlmPayload start", this->name());
    // Copy basic fields
    dest.command = static_cast<uint8_t>(src.get_command());
    dest.address = src.get_address();
    dest.data_length = src.get_data_length();
    if (src.get_command() == tlm::TLM_WRITE_COMMAND) {
        dest.byte_enable_length = src.get_byte_enable_length();
    }
    dest.streaming_width = src.get_streaming_width();
    dest.response = static_cast<uint8_t>(src.get_response_status());
    XuserExtension* src_xuser_ext = nullptr;
    src.get_extension(src_xuser_ext);
    if (src_xuser_ext != nullptr) {
        dest.axuser_length = 0;
        if (src.get_command() == tlm::TLM_READ_COMMAND) {
            dest.xuser_length = src_xuser_ext->data_length;
        } else {
            dest.xuser_length = sizeof(src_xuser_ext->response);
        }
    }
    unsigned char* data_ptr = src.get_data_ptr();
    unsigned int data_len = src.get_data_length();

    // Only copy data for read command
    if (src.get_command() == tlm::TLM_READ_COMMAND ) {
        if (dest.data_length > 0) {
            std::memcpy(dest.data, src.get_data_ptr(), data_len);
        }
        if (src_xuser_ext != nullptr) {
            if (dest.xuser_length > 0) {
                std::memcpy(dest.data + dest.data_length,
                           src_xuser_ext->get_data_ptr(),
                           dest.xuser_length);
            }
        }

    } else { // For write command
        if (src_xuser_ext != nullptr) {
            if (dest.xuser_length > 0) {
                std::memcpy(dest.data,
                           reinterpret_cast<uint8_t*>(&src_xuser_ext->response),
                           dest.xuser_length);
            }
        }

    }
    SSLN_LOG_INFO(file_logger, "[{}], Convert to TlmPayload end", this->name());
}

// Required implementations for tlm_bw_transport_if
tlm::tlm_sync_enum AxiSlaveTlmProxy::nb_transport_bw(
    tlm::tlm_generic_payload& trans,
    tlm::tlm_phase& phase,
    sc_core::sc_time& delay) {
    return tlm::TLM_ACCEPTED;
}

void AxiSlaveTlmProxy::invalidate_direct_mem_ptr(
    sc_dt::uint64 start_range,
    sc_dt::uint64 end_range) {
}

}  // namespace axi
}  // namespace hybrid
}  // namespace ssln 