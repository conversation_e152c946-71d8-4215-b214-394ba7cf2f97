add_executable(axi_slave_tlm_proxy_test
    src/axi_slave_tlm_proxy_test.cc
)


target_link_directories(axi_slave_tlm_proxy_test
    PRIVATE
        ${PROJECT_ROOT}/thirdparty/fake_install/lib
        $ENV{UMICOM_HOME}/lib
)

target_link_libraries(axi_slave_tlm_proxy_test
    PRIVATE
        GTest::gtest_main
        axi_slave_tlm_proxy
        fake_xtor
        systemc
)

include(GoogleTest)
gtest_discover_tests(axi_slave_tlm_proxy_test)

add_dependencies(build_utests axi_slave_tlm_proxy_test)