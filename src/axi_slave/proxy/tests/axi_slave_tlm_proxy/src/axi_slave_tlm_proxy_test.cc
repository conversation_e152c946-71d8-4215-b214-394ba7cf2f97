#include <systemc.h>
#include <gtest/gtest.h>
#include <vector>
#include <thread>
#include <tlm>
#include "async_event.h"
#include "tlm_utils/simple_target_socket.h"
#include "axi_slave_tlm_proxy.h"
#include "tlm_payload.h"

using namespace std;
using namespace ssln::hybrid::axi;
using namespace ssln::hybrid;

const int sleep_time = 200;

class ScStopModule: public sc_core::sc_module{
public:
    SC_HAS_PROCESS(ScStopModule);
    ScStopModule(sc_core::sc_module_name name): sc_core::sc_module(name){
        SC_THREAD(StopThread);
    }

    void StopThread(){
        while(true){
            wait(stop_event);
            sc_core::sc_stop();
        }
    }

    async_event stop_event;
};

class FakeSlave: public sc_core::sc_module
                , public virtual tlm::tlm_fw_transport_if<>{
public:
    SC_HAS_PROCESS(FakeSlave);
    enum { SIZE = 256 };

    FakeSlave(sc_module_name name) : 
        sc_module(name),
        target_socket("target_socket") {
        // target_socket->register_b_transport(this, &FakeSlave::b_transport);
        target_socket.bind(*this);
        for (size_t i = 0; i < SIZE; i++) {
            mem[i] = static_cast<uint8_t>(i);
        }
    }

    virtual ~FakeSlave() {
        for (auto* trans : response_lists) {
            delete trans;  
        }
        response_lists.clear();
    }
    bool get_direct_mem_ptr(tlm::tlm_generic_payload& trans,
                         tlm::tlm_dmi& dmi_data) override {}

    unsigned int transport_dbg(tlm::tlm_generic_payload& trans) override {}

    tlm::tlm_sync_enum nb_transport_fw(tlm::tlm_generic_payload& trans,
                                    tlm::tlm_phase& phase,
                                    sc_core::sc_time& delay) override {}


    virtual void b_transport(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay) {
        std::cout << "b_transport" << std::endl;
        
        // Add transaction to response list
        // response_lists.push_back(&trans);

        // Get command and relevant information from the transaction
        tlm::tlm_command cmd = trans.get_command();
        uint64_t adr = trans.get_address();
        unsigned char* ptr = trans.get_data_ptr();
        unsigned int len = trans.get_data_length();
        unsigned char* byt = trans.get_byte_enable_ptr();
        unsigned int wid = trans.get_streaming_width();

        if (adr >= SIZE) {
            trans.set_response_status(tlm::TLM_GENERIC_ERROR_RESPONSE);
            return;
        }
        std::cout << "Command: " << cmd << ", Address: " << adr << ", Length: " << len << ", Streaming Width: " << wid << std::endl;

        // Check for read or write commands
        if (cmd == tlm::TLM_READ_COMMAND) {
            // Handle read command, streaming width applies here
            unsigned int remaining = len;
            unsigned int offset = 0;
            while (remaining > 0) {
                // Calculate the transfer size, which is the minimum of remaining data or streaming width
                unsigned int transfer_size = std::min(remaining, wid);
                std::memcpy(ptr + offset, &mem[adr + offset], transfer_size);
                offset += transfer_size;
                remaining -= transfer_size;
            }
        } else if (cmd == tlm::TLM_WRITE_COMMAND) {
            // Handle write command, streaming width applies here
            unsigned int remaining = len;
            unsigned int offset = 0;
            while (remaining > 0) {
                // Calculate the transfer size, which is the minimum of remaining data or streaming width
                unsigned int transfer_size = std::min(remaining, wid);
                std::memcpy(&mem[adr + offset], ptr + offset, transfer_size);
                offset += transfer_size;
                remaining -= transfer_size;
            }
        }

        // Log the transaction data
        std::cout << "Transaction data ptr: " << static_cast<void*>(trans.get_data_ptr()) << std::endl;
        std::cout << "Transaction data: ";
        for (unsigned int i = 0; i < len; ++i) {
            std::cout << std::hex << static_cast<int>(trans.get_data_ptr()[i]) << " ";
        }
        std::cout << std::endl;

        // Set response status
        trans.set_response_status(tlm::TLM_OK_RESPONSE);
        tlm::tlm_generic_payload* new_trans = new tlm::tlm_generic_payload();
        new_trans->deep_copy_from(trans);
        if (!new_trans->get_data_ptr() && trans.get_data_ptr()) {
            uint8_t* copied_data = new uint8_t[trans.get_data_length()];
            std::memcpy(copied_data, trans.get_data_ptr(), trans.get_data_length());
            new_trans->set_data_ptr(copied_data);
        }
        if (!new_trans->get_byte_enable_ptr() && trans.get_byte_enable_ptr()) {
            uint8_t* copied_data = new uint8_t[trans.get_data_length()];
            std::memcpy(copied_data, trans.get_byte_enable_ptr(), trans.get_data_length());
            new_trans->set_byte_enable_ptr(copied_data);
        }
        // new_trans->set_data_ptr(ptr);
        // new_trans->set_byte_enable_ptr(byt);
        response_lists.push_back(new_trans);
    }



    tlm::tlm_target_socket<> target_socket;
    std::vector<tlm::tlm_generic_payload*> response_lists;
    uint8_t mem[SIZE];


};

class AxiSlaveTlmProxyTest : public ::testing::Test {
 protected:
    void SetUp() override {
        std::cout << "create" << endl;
        std::string channel_name = "axi_slave_tlm_proxy_test";
        req_channel_ = new ipc::channel((channel_name + "_req").c_str(), ipc::sender);
        resp_channel_ = new ipc::channel((channel_name + "_resp").c_str(), ipc::receiver);
        axi_slave_tlm_proxy_ = std::make_shared<AxiSlaveTlmProxy>("axi_slave_tlm_proxy", channel_name);
        fake_slave_ = std::make_shared<FakeSlave>("fake_slave");
        axi_slave_tlm_proxy_->init_socket.bind(fake_slave_->target_socket);
        stop_module_ = new ScStopModule("stopmodule");
        sim_thread_ = new std::thread([]() {
                std::cout << "sc before start" << std::endl;
                sc_core::sc_start();
                std::cout << "sc after start" << std::endl;
            });
            
    }

    void TearDown() override {
        stop_module_->stop_event.notify();
        if (sim_thread_->joinable()) {
            sim_thread_->join();
        }
        delete stop_module_;
        delete sim_thread_;
        req_channel_->disconnect();
        resp_channel_->disconnect();
        delete req_channel_;
        delete resp_channel_;
        sc_core::sc_curr_simcontext = new sc_core::sc_simcontext();
        sc_core::sc_default_global_context = nullptr;
        }

    std::shared_ptr<AxiSlaveTlmProxy> axi_slave_tlm_proxy_;
    std::shared_ptr<FakeSlave> fake_slave_;
    ipc::channel* req_channel_;
    ipc::channel* resp_channel_;
    ScStopModule* stop_module_;
    std::thread* sim_thread_;
};

TEST_F(AxiSlaveTlmProxyTest, R_SINGLE) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    uint32_t total_size = sizeof(TlmPayload);
    unsigned char* payload_ptr = new unsigned char[total_size];
    TlmPayload* payload = reinterpret_cast<TlmPayload*>(payload_ptr);
    payload->id = 0;
    payload->command = 0;
    payload->address = 0x01;
    payload->data_length = 0x10;
    payload->byte_enable_length = 0x0;
    payload->axuser_length = 0x0;
    payload->xuser_length = 0x0;
    payload->streaming_width = 0x10;
    payload->response = 0x0;
    payload->data = nullptr;

    req_channel_->send(payload, total_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(fake_slave_->response_lists.size(), 1);
    tlm::tlm_generic_payload& tlm_gp = *((fake_slave_->response_lists)[0]);
    EXPECT_EQ(tlm_gp.get_command(), tlm::TLM_READ_COMMAND);
    EXPECT_EQ(tlm_gp.get_address(), 0x01);
    EXPECT_EQ(tlm_gp.get_data_length(), 0x10);
    EXPECT_EQ(tlm_gp.get_byte_enable_length(), 0x0);
    EXPECT_EQ(tlm_gp.get_streaming_width(), 0x10);
    unsigned char* data_ptr = tlm_gp.get_data_ptr();
    unsigned int data_len = tlm_gp.get_data_length();
    std::cout << "tlm gp data: ";
    for (unsigned int i = 0; i < data_len; ++i) {
        std::cout << std::hex << static_cast<int>(data_ptr[i]) << " ";
    }
    std::cout << std::endl;
    for (int i = 0; i < tlm_gp.get_data_length(); ++i) {
        EXPECT_EQ(static_cast<uint8_t>(tlm_gp.get_data_ptr()[i]), static_cast<uint8_t>(i+payload->address));
    }
    auto recv_data = resp_channel_->recv();
    TlmPayload* recv_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    EXPECT_EQ(recv_payload->command, tlm::TLM_READ_COMMAND);
    EXPECT_EQ(recv_payload->data_length, 0x10);
    EXPECT_EQ(recv_payload->streaming_width, 0x10);
    EXPECT_EQ(recv_payload->axuser_length, 0x0);
    EXPECT_EQ(recv_payload->xuser_length, 0x0);
    EXPECT_EQ(recv_payload->response, 0x1);
    delete[] tlm_gp.get_data_ptr();
    delete[] tlm_gp.get_byte_enable_ptr();
}

TEST_F(AxiSlaveTlmProxyTest, R_MULTI) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    uint32_t total_size = sizeof(TlmPayload);
    unsigned char* payload_ptr = new unsigned char[total_size];
    TlmPayload* payload = reinterpret_cast<TlmPayload*>(payload_ptr);
    payload->id = 0;
    payload->command = 0;
    payload->address = 0x01;
    payload->data_length = 0x40;
    payload->byte_enable_length = 0x0;
    payload->axuser_length = 0x0;
    payload->xuser_length = 0x0;
    payload->streaming_width = 0x10;
    payload->response = 0x0;
    payload->data = nullptr;

    req_channel_->send(payload, total_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(fake_slave_->response_lists.size(), 1);
    tlm::tlm_generic_payload& tlm_gp = *((fake_slave_->response_lists)[0]);
    EXPECT_EQ(tlm_gp.get_command(), tlm::TLM_READ_COMMAND);
    EXPECT_EQ(tlm_gp.get_address(), 0x01);
    EXPECT_EQ(tlm_gp.get_data_length(), 0x40);
    EXPECT_EQ(tlm_gp.get_byte_enable_length(), 0x0);
    EXPECT_EQ(tlm_gp.get_streaming_width(), 0x10);
    unsigned char* data_ptr = tlm_gp.get_data_ptr();
    unsigned int data_len = tlm_gp.get_data_length();
    std::cout << "tlm gp data: ";
    for (unsigned int i = 0; i < data_len; ++i) {
        std::cout << std::hex << static_cast<int>(data_ptr[i]) << " ";
    }
    std::cout << std::endl;
    for (int i = 0; i < tlm_gp.get_data_length(); i += 0x10) {
        for (int j = 0; j < 0x10; ++j) {
            EXPECT_EQ(static_cast<uint8_t>(tlm_gp.get_data_ptr()[i+j]), static_cast<uint8_t>(i+j+payload->address));
        }
        
    }
    auto recv_data = resp_channel_->recv();
    TlmPayload* recv_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    EXPECT_EQ(recv_payload->command, tlm::TLM_READ_COMMAND);
    EXPECT_EQ(recv_payload->data_length, 0x40);
    EXPECT_EQ(recv_payload->streaming_width, 0x10);
    EXPECT_EQ(recv_payload->axuser_length, 0x0);
    EXPECT_EQ(recv_payload->xuser_length, 0x0);
    EXPECT_EQ(recv_payload->response, 0x1);
    delete[] tlm_gp.get_data_ptr();
    delete[] tlm_gp.get_byte_enable_ptr();
}

TEST_F(AxiSlaveTlmProxyTest, R_ERR) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    uint32_t total_size = sizeof(TlmPayload);
    unsigned char* payload_ptr = new unsigned char[total_size];
    TlmPayload* payload = reinterpret_cast<TlmPayload*>(payload_ptr);
    payload->id = 0;
    payload->command = 0;
    payload->address = 0x100;
    payload->data_length = 0x10;
    payload->byte_enable_length = 0x0;
    payload->axuser_length = 0x0;
    payload->xuser_length = 0x0;
    payload->streaming_width = 0x10;
    payload->response = 0x0;
    payload->data = nullptr;

    req_channel_->send(payload, total_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(fake_slave_->response_lists.size(), 0);
    
    auto recv_data = resp_channel_->recv();
    TlmPayload* recv_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    EXPECT_EQ(recv_payload->command, tlm::TLM_READ_COMMAND);
    EXPECT_EQ(recv_payload->data_length, 0x10);
    EXPECT_EQ(recv_payload->streaming_width, 0x10);
    EXPECT_EQ(recv_payload->axuser_length, 0x0);
    EXPECT_EQ(recv_payload->xuser_length, 0x0);
    EXPECT_EQ(recv_payload->response, static_cast<int>(tlm::TLM_GENERIC_ERROR_RESPONSE));
}

TEST_F(AxiSlaveTlmProxyTest, W_SINGLE) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    uint32_t data_size = 0x100;
    uint32_t total_size = sizeof(TlmPayload) + data_size;
    unsigned char* payload_ptr = new unsigned char[total_size];
    TlmPayload* payload = reinterpret_cast<TlmPayload*>(payload_ptr);
    payload->id = 0;
    payload->command = 1;
    payload->address = 0x0;
    payload->data_length = 0x100;
    payload->byte_enable_length = 0x0;
    payload->axuser_length = 0x0;
    payload->xuser_length = 0x0;
    payload->streaming_width = 0x100;
    payload->response = 0x0;
    payload->data = payload_ptr + sizeof(TlmPayload);
    for(int i = 0; i < payload->data_length; i += payload->streaming_width){
        for(int j = 0; j < payload->streaming_width; j++){
            if(i + j >= data_size) break;
            payload->data[i + j] = (payload->address + j) % 256;
            std::cout << std::hex << static_cast<int>(payload->data[i + j]) << " ";
        }
        std::cout << std::endl;
    }

    req_channel_->send(payload, total_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(fake_slave_->response_lists.size(), 1);
    tlm::tlm_generic_payload& tlm_gp = *((fake_slave_->response_lists)[0]);
    EXPECT_EQ(tlm_gp.get_command(), tlm::TLM_WRITE_COMMAND);
    EXPECT_EQ(tlm_gp.get_address(), 0x0);
    EXPECT_EQ(tlm_gp.get_data_length(), 0x100);
    EXPECT_EQ(tlm_gp.get_byte_enable_length(), 0x0);
    EXPECT_EQ(tlm_gp.get_streaming_width(), 0x100);
    for (int i = 0; i < tlm_gp.get_data_length(); ++i) {
        EXPECT_EQ(tlm_gp.get_data_ptr()[i], payload->data[i]);
    }
 
    auto recv_data = resp_channel_->recv();
    TlmPayload* recv_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    EXPECT_EQ(recv_payload->command, tlm::TLM_WRITE_COMMAND);
    EXPECT_EQ(recv_payload->data_length, 0x100);
    EXPECT_EQ(recv_payload->data, nullptr);
    EXPECT_EQ(recv_payload->streaming_width, 0x100);
    EXPECT_EQ(recv_payload->axuser_length, 0x0);
    EXPECT_EQ(recv_payload->xuser_length, 0x0);
    EXPECT_EQ(recv_payload->response, 0x1);
}

TEST_F(AxiSlaveTlmProxyTest, W_MULTI) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    uint32_t data_size = 0x100;
    uint32_t total_size = sizeof(TlmPayload) + data_size;
    unsigned char* payload_ptr = new unsigned char[total_size];
    TlmPayload* payload = reinterpret_cast<TlmPayload*>(payload_ptr);
    payload->id = 0;
    payload->command = 1;
    payload->address = 0x0;
    payload->data_length = 0x40;
    payload->byte_enable_length = 0x0;
    payload->axuser_length = 0x0;
    payload->xuser_length = 0x0;
    payload->streaming_width = 0x10;
    payload->response = 0x0;
    payload->data = payload_ptr + sizeof(TlmPayload);
    for(int i = 0; i < payload->data_length; i += payload->streaming_width){
        for(int j = 0; j < payload->streaming_width; j++){
            if(i + j >= data_size) break;
            payload->data[i + j] = (payload->address + j) % 256;
            std::cout << std::hex << static_cast<int>(payload->data[i + j]) << " ";
        }
        std::cout << std::endl;
    }

    req_channel_->send(payload, total_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(fake_slave_->response_lists.size(), 1);
    tlm::tlm_generic_payload& tlm_gp = *((fake_slave_->response_lists)[0]);
    EXPECT_EQ(tlm_gp.get_command(), tlm::TLM_WRITE_COMMAND);
    EXPECT_EQ(tlm_gp.get_address(), 0x0);
    EXPECT_EQ(tlm_gp.get_data_length(), 0x40);
    EXPECT_EQ(tlm_gp.get_byte_enable_length(), 0x0);
    EXPECT_EQ(tlm_gp.get_streaming_width(), 0x10);
    for (int i = 0; i < tlm_gp.get_data_length(); ++i) {
        EXPECT_EQ(tlm_gp.get_data_ptr()[i], payload->data[i]);
    }
    auto recv_data = resp_channel_->recv();
    TlmPayload* recv_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    EXPECT_EQ(recv_payload->command, tlm::TLM_WRITE_COMMAND);
    EXPECT_EQ(recv_payload->data_length, 0x40);
    EXPECT_EQ(recv_payload->data, nullptr);
    EXPECT_EQ(recv_payload->streaming_width, 0x10);
    EXPECT_EQ(recv_payload->axuser_length, 0x0);
    EXPECT_EQ(recv_payload->xuser_length, 0x0);
    EXPECT_EQ(recv_payload->response, 0x1);
}

TEST_F(AxiSlaveTlmProxyTest, W_ERR) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    uint32_t data_size = 0x100;
    uint32_t total_size = sizeof(TlmPayload) + data_size;
    unsigned char* payload_ptr = new unsigned char[total_size];
    TlmPayload* payload = reinterpret_cast<TlmPayload*>(payload_ptr);
    payload->id = 0;
    payload->command = 1;
    payload->address = 0x100;
    payload->data_length = 0x100;
    payload->byte_enable_length = 0x0;
    payload->axuser_length = 0x0;
    payload->xuser_length = 0x0;
    payload->streaming_width = 0x100;
    payload->response = 0x0;
    payload->data = payload_ptr + sizeof(TlmPayload);
    for(int i = 0; i < payload->data_length; i += payload->streaming_width){
        for(int j = 0; j < payload->streaming_width; j++){
            if(i + j >= data_size) break;
            payload->data[i + j] = (payload->address + j) % 256;
            std::cout << std::hex << static_cast<int>(payload->data[i + j]) << " ";
        }
        std::cout << std::endl;
    }

    req_channel_->send(payload, total_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(fake_slave_->response_lists.size(), 0);
 
    auto recv_data = resp_channel_->recv();
    TlmPayload* recv_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    EXPECT_EQ(recv_payload->command, tlm::TLM_WRITE_COMMAND);
    EXPECT_EQ(recv_payload->data_length, 0x100);
    EXPECT_EQ(recv_payload->data, nullptr);
    EXPECT_EQ(recv_payload->address, 0x100);
    EXPECT_EQ(recv_payload->streaming_width, 0x100);
    EXPECT_EQ(recv_payload->axuser_length, 0x0);
    EXPECT_EQ(recv_payload->xuser_length, 0x0);
    EXPECT_EQ(recv_payload->response, static_cast<int>(tlm::TLM_GENERIC_ERROR_RESPONSE));
}


TEST_F(AxiSlaveTlmProxyTest, W_BE) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    uint32_t data_size = 0x10;
    uint32_t be_size = 0x10;
    uint32_t total_size = sizeof(TlmPayload) + data_size + be_size;
    unsigned char* payload_ptr = new unsigned char[total_size];
    TlmPayload* payload = reinterpret_cast<TlmPayload*>(payload_ptr);
    payload->id = 0;
    payload->command = 1;
    payload->address = 0x0;
    payload->data_length = 0x10;
    payload->byte_enable_length = 0x10;
    payload->axuser_length = 0x0;
    payload->xuser_length = 0x0;
    payload->streaming_width = 0x10;
    payload->response = 0x0;
    payload->data = payload_ptr + sizeof(TlmPayload);
    for(int i = 0; i < payload->data_length; i += payload->streaming_width){
        uint8_t* data_ptr = payload->data + i;
        for(int j = 0; j < payload->streaming_width; j++){
            if(i + j >= data_size) break;
            data_ptr[i + j] = (payload->address + j) % 256;
            std::cout << std::hex << static_cast<int>(payload->data[i + j]) << " ";
        }
        uint8_t* be_ptr = payload->data + payload->data_length + i;
        for(int j = 0; j < payload->streaming_width; j++){
            if(i + j >= be_size) break;
            be_ptr[i + j] = (i + j) % 2 ? 0xff : 0x0;
        }
    }

    req_channel_->send(payload, total_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(fake_slave_->response_lists.size(), 1);
    tlm::tlm_generic_payload& tlm_gp = *((fake_slave_->response_lists)[0]);
    EXPECT_EQ(tlm_gp.get_command(), tlm::TLM_WRITE_COMMAND);
    EXPECT_EQ(tlm_gp.get_address(), 0x0);
    EXPECT_EQ(tlm_gp.get_data_length(), 0x10);
    EXPECT_EQ(tlm_gp.get_byte_enable_length(), 0x10);
    EXPECT_EQ(tlm_gp.get_streaming_width(), 0x10);
 
    auto recv_data = resp_channel_->recv();
    TlmPayload* recv_payload = reinterpret_cast<TlmPayload*>(recv_data.data());
    EXPECT_EQ(recv_payload->command, tlm::TLM_WRITE_COMMAND);
    EXPECT_EQ(recv_payload->data_length, 0x10);
    EXPECT_EQ(recv_payload->data, nullptr);
    EXPECT_EQ(recv_payload->streaming_width, 0x10);
    EXPECT_EQ(recv_payload->byte_enable_length, 0x10);
    EXPECT_EQ(recv_payload->axuser_length, 0x0);
    EXPECT_EQ(recv_payload->xuser_length, 0x0);
    EXPECT_EQ(recv_payload->response, 0x1);
}

int sc_main(int argc, char* argv[]) {
    return 0;
}