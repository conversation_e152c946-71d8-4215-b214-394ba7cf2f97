add_executable(slave_porxy_transaction_manager
    src/transaction_manager_test.cc
)


target_link_directories(slave_porxy_transaction_manager
    PRIVATE
        ${PROJECT_ROOT}/thirdparty/fake_install/lib
        $ENV{UMICOM_HOME}/lib
)

target_link_libraries(slave_porxy_transaction_manager
    PRIVATE
        GTest::gtest_main
        axi_slave_tlm_proxy
        fake_xtor
        systemc
)

include(GoogleTest)
gtest_discover_tests(slave_porxy_transaction_manager)

add_dependencies(build_utests slave_porxy_transaction_manager)