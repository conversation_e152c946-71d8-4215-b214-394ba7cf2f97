#include <systemc.h>
#include <gtest/gtest.h>
#include <vector>
#include <thread>
#include "transaction_manager.h"

using namespace std;
using namespace ssln::hybrid::axi;

class TransactionManagerTest : public ::testing::Test {
protected:
    void SetUp() override {
        manager_ = new TransactionManager();
    }

    void TearDown() override {
        delete manager_;
    }

    tlm::tlm_generic_payload* CreateTestTransaction(size_t data_length = 8, size_t be_length = 8) {
        auto* trans = new tlm::tlm_generic_payload();
        uint8_t* data = new uint8_t[data_length];
        uint8_t* byte_enable = new uint8_t[be_length];
        
        // Initialize test data
        for (size_t i = 0; i < data_length; i++) {
            data[i] = static_cast<uint8_t>(i);
        }
        for (size_t i = 0; i < be_length; i++) {
            byte_enable[i] = 0xFF;
        }

        trans->set_data_ptr(data);
        trans->set_data_length(data_length);
        trans->set_byte_enable_ptr(byte_enable);
        trans->set_byte_enable_length(be_length);
        
        return trans;
    }

    TransactionManager* manager_;
};

TEST_F(TransactionManagerTest, RegisterAndGetTransaction) {
    uint64_t id = 123;
    auto* trans = CreateTestTransaction();
    
    // Register transaction
    manager_->RegisterTransaction(id, trans);
    
    // Get transaction and verify
    auto* retrieved_trans = manager_->GetTransaction(id);
    ASSERT_NE(retrieved_trans, nullptr);
    EXPECT_EQ(retrieved_trans, trans);
    EXPECT_EQ(retrieved_trans->get_data_length(), 8);
    EXPECT_EQ(retrieved_trans->get_byte_enable_length(), 8);
    
    // Verify data contents
    uint8_t* data = retrieved_trans->get_data_ptr();
    for (size_t i = 0; i < 8; i++) {
        EXPECT_EQ(data[i], i);
    }
}

TEST_F(TransactionManagerTest, GetNonExistentTransaction) {
    uint64_t id = 456;
    auto* trans = manager_->GetTransaction(id);
    EXPECT_EQ(trans, nullptr);
}

TEST_F(TransactionManagerTest, RemoveTransaction) {
    uint64_t id = 789;
    auto* trans = CreateTestTransaction();
    
    // Register and then remove transaction
    manager_->RegisterTransaction(id, trans);
    manager_->RemoveTransaction(id);
    
    // Verify transaction is removed
    auto* retrieved_trans = manager_->GetTransaction(id);
    EXPECT_EQ(retrieved_trans, nullptr);
}

TEST_F(TransactionManagerTest, MultipleTransactions) {
    std::vector<uint64_t> ids = {1, 2, 3, 4, 5};
    std::vector<tlm::tlm_generic_payload*> transactions;
    
    // Register multiple transactions
    for (uint64_t id : ids) {
        auto* trans = CreateTestTransaction();
        transactions.push_back(trans);
        manager_->RegisterTransaction(id, trans);
    }
    
    // Verify all transactions
    for (size_t i = 0; i < ids.size(); i++) {
        auto* retrieved_trans = manager_->GetTransaction(ids[i]);
        ASSERT_NE(retrieved_trans, nullptr);
        EXPECT_EQ(retrieved_trans, transactions[i]);
    }
    
    // Remove all transactions
    for (uint64_t id : ids) {
        manager_->RemoveTransaction(id);
    }
    
    // Verify all transactions are removed
    for (uint64_t id : ids) {
        auto* retrieved_trans = manager_->GetTransaction(id);
        EXPECT_EQ(retrieved_trans, nullptr);
    }
}

TEST_F(TransactionManagerTest, ConcurrentAccess) {
    const int num_threads = 4;
    const int ops_per_thread = 100;
    std::vector<std::thread> threads;
    
    for (int i = 0; i < num_threads; i++) {
        threads.emplace_back([this, i, ops_per_thread]() {
            for (int j = 0; j < ops_per_thread; j++) {
                uint64_t id = i * ops_per_thread + j;
                auto* trans = CreateTestTransaction();
                
                manager_->RegisterTransaction(id, trans);
                auto* retrieved_trans = manager_->GetTransaction(id);
                EXPECT_EQ(retrieved_trans, trans);
                manager_->RemoveTransaction(id);
            }
        });
    }
    
    for (auto& thread : threads) {
        thread.join();
    }
}

int sc_main(int argc, char* argv[]) {
    return 0;
}