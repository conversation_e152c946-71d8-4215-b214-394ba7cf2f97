# AXI Slave Proxy Design Specification

## Overview

The AXI Slave Proxy is a TLM Initiator that acts as a bridge between SystemC/TLM(target) and the AXI Slave Bridge process. It implements the TLM interface and converts between TlmPayload and tlm_generic_payload formats.

## Key Responsibilities

1. Receive TlmPayload from Bridge via IPC
2. Convert TlmPayload to tlm_generic_payload
3. Send tlm_generic_payload to TLM target
4. Receive tlm_generic_payload response from target
5. Convert tlm_generic_payload back to TlmPayload
6. Send TlmPayload response to Bridge via IPC

## Class Structure

```cpp
class AxiSlaveTlmProxy : public sc_core::sc_module {
public:
    // Constructor takes channel name
    explicit AxiSlaveTlmProxy(sc_core::sc_module_name name,
                             const std::string& channel_name = "axi_slave");
    
    // TLM initiator socket
    tlm::tlm_initiator_socket<> init_socket;

private:
    // Wrapper for TlmPayload with fixed buffer
    struct PayloadWrapper {
        static constexpr size_t MAX_DATA_SIZE = 4096;
        PayloadWrapper() : data(new char[sizeof(TlmPayload) + MAX_DATA_SIZE]) {}
        // ... memory management and access methods
    };

    // IPC channels
    ipc::channel req_channel_;   // For receiving requests from bridge
    ipc::channel resp_channel_;  // For sending responses to bridge

    // Transaction management
    std::unique_ptr<TransactionManager> transaction_manager_;
    
    // Ring buffer for thread communication
    moodycamel::BlockingReaderWriterCircularBuffer<PayloadWrapper> request_queue_;
    
    // Async event for notifying sc thread
    async_event request_event_;
    
    // Threads and processing
    std::unique_ptr<std::thread> ipc_thread_;
    void IpcThread();
    void ProcessRequest();
};
```

## Implementation Approaches

### 1. Ring Buffer with MoodyCamel

This approach uses BlockingReaderWriterCircularBuffer for thread communication:
```cpp
    // CPP Thread implementation
    void IpcThread() {
        while (running_) {
            auto data = req_channel_.recv();
            if (data.empty()) continue;
            
            PayloadWrapper wrapper;
            auto* payload = wrapper.payload();
            // Copy header
            std::memcpy(payload, data.data(), sizeof(TlmPayload));
            // Copy data if present
            if (payload->data_length > 0 || payload->byte_enable_length > 0) {
                std::memcpy(payload->data, 
                           static_cast<const char*>(data.data()) + sizeof(TlmPayload),
                           payload->data_length + payload->byte_enable_length);
            }
            
            request_queue_.wait_enqueue(std::move(wrapper));
            request_event_.notify();
        }
    }
    
    // SC Thread implementation
    void ProcessRequest() {
        PayloadWrapper wrapper;
        
        while (true) {
            wait(request_event_);
            
            while (request_queue_.try_dequeue(wrapper)) {
                const TlmPayload* payload = wrapper.payload();
                tlm::tlm_generic_payload trans;
                ConvertToTlmGenericPayload(*payload, trans);
                
                // Register transaction
                transaction_manager_->RegisterTransaction(payload->id, &trans);
                
                // Process transaction...
               sc_core::sc_time delay = sc_core::SC_ZERO_TIME;
               init_socket->b_transport(trans, delay);
               
               // Handle response
               TlmPayload response;
               ConvertToTlmPayload(trans, response);
               resp_channel_.send(&response, sizeof(response) + 
                                response.data_length + response.byte_enable_length);
               
               // Cleanup transaction
               transaction_manager_->RemoveTransaction(ipc_payload->id);
            }
        }
    }
};
```

**Advantages**:
1. Decoupled thread communication
2. Can handle burst requests
3. Built-in thread synchronization
4. Memory reuse through ring buffer
5. No need for explicit synchronization between threads

**Disadvantages**:
1. One memory copy required
2. Fixed pre-allocation of maximum size buffers
3. Slightly more complex implementation

### 2. Double Buffer

This approach uses double buffering for thread communication:

```cpp
private:
    // 使用双缓冲区
    struct DataBuffer {
        const void* data{nullptr};
        std::atomic<bool> processed{true};
    };
    std::array<DataBuffer, 2> buffers_;
    std::atomic<size_t> current_buffer_{0};
    async_event request_event_;

    void IpcThread() {
        while (running_) {
            auto data = req_channel_.recv();
            if (data.empty()) continue;
            
            // 找到一个可用的缓冲区
            size_t buf_idx = current_buffer_.load(std::memory_order_acquire);
            size_t next_buf = (buf_idx + 1) % 2;
            
            // 等待下一个缓冲区可用
            while (!buffers_[next_buf].processed.load(std::memory_order_acquire)) {
                std::this_thread::yield();
            }
            
            // 设置数据并切换缓冲区
            buffers_[next_buf].data = data.data();
            buffers_[next_buf].processed.store(false, std::memory_order_release);
            current_buffer_.store(next_buf, std::memory_order_release);
            
            request_event_.notify();
        }
    }
    
    void ProcessRequest() {
        while (true) {
            wait(request_event_);
            
            size_t buf_idx = current_buffer_.load(std::memory_order_acquire);
            if (!buffers_[buf_idx].processed.load(std::memory_order_acquire)) {
                auto* payload = reinterpret_cast<const TlmPayload*>(
                    buffers_[buf_idx].data);
                tlm::tlm_generic_payload trans;
                ConvertToTlmGenericPayload(*payload, trans);
                
                // Process transaction...
                init_socket->b_transport(trans, delay);
                
                // 标记处理完成
                buffers_[buf_idx].processed.store(true, std::memory_order_release);
            }
        }
    }
};
```

**Advantages**:
1. Zero-copy for IPC data (big data)
2. Simpler implementation
3. Lower memory usage
4. Direct data access

**Disadvantages**:
1. Tighter coupling between threads
2. Cannot handle burst requests
3. CPP thread must wait for SC thread to complete processing

## Transaction Flow

1. **IPC Thread**:
   - Receives TlmPayload from Bridge
   - Copies data to PayloadWrapper
   - Enqueues to request queue
   - Notifies SC thread

2. **SC Thread**:
   - Waits for request event
   - Dequeues PayloadWrapper
   - Converts to tlm_generic_payload
   - Sends to TLM target
   - Converts response
   - Sends response to Bridge

## Memory Management

1. **PayloadWrapper**:
   - Fixed size buffer for common case (4KB)
   - RAII design for memory safety
   - Move-only semantics

2. **Transaction Manager**:
   - Tracks active transactions
   - Manages memory for tlm_generic_payload
   - Thread-safe implementation

### Transaction Manager
```cpp
   class TransactionManager {
   public:
       // Register transaction with memory management
       void RegisterTransaction(uint64_t id, tlm::tlm_generic_payload* trans) {
           std::lock_guard<std::mutex> lock(mutex_);
           transactions_[id] = TransactionContext{
               trans,
               trans->get_data_ptr(),
               trans->get_byte_enable_ptr()
           };
       }
       
       // Clean up transaction and its memory
       void RemoveTransaction(uint64_t id) {
           std::lock_guard<std::mutex> lock(mutex_);
           auto it = transactions_.find(id);
           if (it != transactions_.end()) {
               delete[] it->second.data_ptr;
               delete[] it->second.byte_enable_ptr;
               transactions_.erase(it);
           }
       }
       
   private:
       struct TransactionContext {
           tlm::tlm_generic_payload* trans;
           uint8_t* data_ptr;
           uint8_t* byte_enable_ptr;
       };
       
       std::unordered_map<uint64_t, TransactionContext> transactions_;
       std::mutex mutex_;
   };
```

## Performance Considerations

1. **Ring Buffer Size**:
   - Set to 256 entries
   - Balances memory usage and burst handling
   - Configurable at runtime

2. **Memory Copies**:
   - Only copy data for write commands in request path
   - Only copy data for read commands in response path
   - Skip byte_enable copying in response path

3. **Thread Communication**:
   - Non-blocking operations where possible
   - Efficient event notification
   - Batch processing in SC thread

## Error Handling

1. **IPC Errors**:
   - Reconnection support
   - Error reporting to SystemC
   - Graceful cleanup

2. **Memory Errors**:
   - Exception handling
   - Resource cleanup
   - Error reporting

## Testing Strategy

1. **Unit Tests**:
   - Payload conversion
   - Memory management
   - Thread synchronization

2. **Integration Tests**:
   - End-to-end transaction flow
   - Error handling
   - Performance metrics

## Future Enhancements

1. **Performance**:
   - Zero-copy optimization for large transfers
   - Memory pool for payload wrappers
   - Configurable queue sizes

2. **Functionality**:
   - DMI support
   - Debug transport
   - Custom extensions 