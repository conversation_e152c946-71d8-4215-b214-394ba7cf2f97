
add_executable(axi_slave_ST_test
    src/axi_slave_ST_test.cpp
)

target_link_directories(axi_slave_ST_test
    PRIVATE
        $ENV{UMICOM_HOME}/lib
        ${PROJECT_ROOT}/thirdparty/fake_install/lib
)

target_link_libraries(axi_slave_ST_test
    PRIVATE
        GTest::gtest_main
        fake_xtor
        axi_slave_bridge
        axi_slave_tlm_proxy
        systemc
)

include(GoogleTest)
gtest_discover_tests(axi_slave_ST_test)

add_dependencies(build_utests axi_slave_ST_test)