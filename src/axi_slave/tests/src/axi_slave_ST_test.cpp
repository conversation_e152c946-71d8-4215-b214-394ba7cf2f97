#include <systemc.h>
#include "axi_slave_proxy.h"
#include "axi_slave_bridge.h"
#include "axi_slave_tlm_proxy.h"
#include <gtest/gtest.h>
#include "tlm_payload.h"
#include "axi_transaction_item.h"
#include <thread>
#include <systemc>
#include <tlm>
#include "async_event.h"

namespace ssln::hybrid::axi {

using namespace umi_axi;

const int sleep_time = 200;

class ScStopModule: public sc_core::sc_module{
public:
    SC_HAS_PROCESS(ScStopModule);
    ScStopModule(sc_core::sc_module_name name): sc_core::sc_module(name){

        SC_THREAD(StopThread);
    }

    void StopThread(){
        while(true){
            wait(stop_event);
            sc_core::sc_stop();
        }
    }

    async_event stop_event;
};

class FakeSlave: public sc_core::sc_module{
public:
    SC_HAS_PROCESS(FakeSlave);
    FakeSlave(sc_core::sc_module_name name, std::shared_ptr<axi_slave_proxy> axi_proxy)
            :sc_module(name),
             xtor_slave_proxy_(std::move(axi_proxy)){
        send_seq = new async_event("send_seq");
        SC_THREAD(send_seq_function);
    }
    
    ~FakeSlave(){
        delete send_seq;
    }

    void send_seq_function(){
        while(true){
            std::cout << "send_seq_function wait start" << std::endl;
            wait(*send_seq);
            std::cout << "send_seq_function wait end" << std::endl;
            std::cout << "seq.size: " << seq.size() << std::endl; 
            for(auto axi_trans: seq){
                xtor_slave_proxy_->triggerEvent("AFTER_RECV_TRANS", axi_trans);
            }
        }
    }
    
    std::shared_ptr<axi_slave_proxy> xtor_slave_proxy_;
    vector<axi_transaction_item*> seq;
    async_event* send_seq;

};

class FakeVbuilder: public sc_core::sc_module
                , public virtual tlm::tlm_fw_transport_if<>{
public:
    SC_HAS_PROCESS(FakeVbuilder);
    enum { SIZE = 256 };

    FakeVbuilder(sc_module_name name) : 
        sc_module(name),
        target_socket("target_socket") {
        target_socket.bind(*this);
        for (size_t i = 0; i < SIZE; i++) {
            mem[i] = static_cast<uint8_t>(i);
        }
        for (size_t i = 0; i < SIZE; i++) {
            ext_mem[i] = static_cast<uint8_t>(0xB0 + i);
        }
    }

    virtual ~FakeVbuilder() {
        for (auto* trans : response_lists) {
            delete trans;  
        }
        response_lists.clear();
    }
    bool get_direct_mem_ptr(tlm::tlm_generic_payload& trans,
                         tlm::tlm_dmi& dmi_data) override {}

    unsigned int transport_dbg(tlm::tlm_generic_payload& trans) override {}

    tlm::tlm_sync_enum nb_transport_fw(tlm::tlm_generic_payload& trans,
                                    tlm::tlm_phase& phase,
                                    sc_core::sc_time& delay) override {}


    virtual void b_transport(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay) {
        std::cout << "b_transport" << std::endl;

        // Get command and relevant information from the transaction
        tlm::tlm_command cmd = trans.get_command();
        uint64_t adr = trans.get_address();
        unsigned char* ptr = trans.get_data_ptr();
        unsigned int len = trans.get_data_length();
        unsigned char* byt = trans.get_byte_enable_ptr();
        unsigned int wid = trans.get_streaming_width();

        if (adr >= SIZE) {
            trans.set_response_status(tlm::TLM_GENERIC_ERROR_RESPONSE);
            return;
        }
        std::cout << "Command: " << cmd << ", Address: " << adr << ", Length: " << len << ", Streaming Width: " << wid << std::endl;
        XuserExtension* resp_ext = nullptr;
        trans.get_extension(resp_ext);

        // Check for read or write commands
        if (cmd == tlm::TLM_READ_COMMAND) {
            // Handle read command, streaming width applies here
            unsigned int remaining = len;
            unsigned int offset = 0;

            // If streaming_width is 0, we just transfer the entire length in one go
            if (wid == 0) {
                std::memcpy(ptr, &mem[adr], remaining);
                if (resp_ext != nullptr && resp_ext->data_length > 0) {
                    uint8_t* ruser_ptr = resp_ext->get_data_ptr();
                    for (unsigned int beat = 0; beat < resp_ext->data_length; beat++) {
                        ruser_ptr[beat] = ext_mem[beat];
                    }
                }
            } else {
                while (remaining > 0) {
                    // Calculate the transfer size, which is the minimum of remaining data or streaming width
                    unsigned int transfer_size = std::min(remaining, wid);
                    std::memcpy(ptr + offset, &mem[adr + offset], transfer_size);
                    if (resp_ext != nullptr && resp_ext->data_length > 0) {
                        uint8_t* ruser_ptr = resp_ext->get_data_ptr();
                        unsigned int beat = offset / wid;
                        ruser_ptr[beat] = ext_mem[beat];
                    }
                    offset += transfer_size;
                    remaining -= transfer_size;
                }
            }
        } else if (cmd == tlm::TLM_WRITE_COMMAND) {
            if (resp_ext != nullptr) {
                memcpy(&ext_mem[0], resp_ext->get_data_ptr(), resp_ext->data_length);
                std::cout << "resp ext: ";
                std::cout << "xuser extension: ";
                for (unsigned int i = 0; i < resp_ext->data_length; ++i) {
                    std::cout << std::hex << static_cast<int>(resp_ext->get_data_ptr()[i]) << " ";
                }
                std::cout << std::endl;
                std::cout << "axuser extension: ";
                for (unsigned int i = 0; i < resp_ext->req_length; ++i) {
                    std::cout << std::hex << static_cast<int>(resp_ext->get_req_ptr()[i]) << " ";
                }
                std::cout << std::endl;
                resp_ext->response = 0x1; // B_user
                std::cout << "Ext Mem data: ";
                for (unsigned int i = 0; i < 12; ++i) {
                    std::cout << std::hex << static_cast<int>(ext_mem[i]) << " ";
                }
                std::cout << std::endl;
            }

            // Handle write command, streaming width applies here
            unsigned int remaining = len;
            unsigned int offset = 0;

            // If streaming_width is 0, we just transfer the entire length in one go
            if (wid == 0) {
                std::memcpy(&mem[adr], ptr, remaining);
            } else {
                while (remaining > 0) {
                    // Calculate the transfer size, which is the minimum of remaining data or streaming width
                    unsigned int transfer_size = std::min(remaining, wid);
                    std::memcpy(&mem[adr + offset], ptr + offset, transfer_size);
                    offset += transfer_size;
                    remaining -= transfer_size;
                }
            }
        }

        // Set response extension
        if (resp_ext != nullptr) {
            trans.set_extension(resp_ext);
            std::cout << "  Copied data: ";
            for (uint32_t i = 0; i < resp_ext->data_length; ++i) {
                std::cout << std::hex << static_cast<int>(resp_ext->get_data_ptr()[i]) << " ";
            }
            std::cout << std::dec << std::endl;
        }
 
        // Log the transaction data
        std::cout << "Mem data: ";
        for (unsigned int i = 0; i < len; ++i) {
            std::cout << std::hex << static_cast<int>(mem[i]) << " ";
        }
        std::cout << std::endl;


        // Set response status
        trans.set_response_status(tlm::TLM_OK_RESPONSE);
        tlm::tlm_generic_payload* new_trans = new tlm::tlm_generic_payload();
        new_trans->deep_copy_from(trans);
        if (!new_trans->get_data_ptr() && trans.get_data_ptr()) {
            uint8_t* copied_data = new uint8_t[trans.get_data_length()];
            std::memcpy(copied_data, trans.get_data_ptr(), trans.get_data_length());
            new_trans->set_data_ptr(copied_data);
        }
        if (!new_trans->get_byte_enable_ptr() && trans.get_byte_enable_ptr()) {
            uint8_t* copied_data = new uint8_t[trans.get_data_length()];
            std::memcpy(copied_data, trans.get_byte_enable_ptr(), trans.get_data_length());
            new_trans->set_byte_enable_ptr(copied_data);
        }
        // new_trans->set_data_ptr(ptr);
        // new_trans->set_byte_enable_ptr(byt);
        response_lists.push_back(new_trans);
    }

    tlm::tlm_target_socket<> target_socket;
    std::vector<tlm::tlm_generic_payload*> response_lists;
    uint8_t mem[SIZE];
    uint8_t ext_mem[SIZE];


};

class AxiSlaveTest : public ::testing::Test {
 protected:
    void SetUp() override {
        std::cout << "create" << endl;
        std::string channel_name = "axi_slave_test";
        xtor_slave_proxy_ = std::make_shared<axi_slave_proxy>(
                256,  
                spec_version_t::AMBA4
            );
        
        axi_slave_tlm_proxy_ = std::make_shared<AxiSlaveTlmProxy>("axi_slave_tlm_proxy", channel_name);
        axi_slave_bridge_ = std::make_shared<AxiSlaveBridge>("axi_slave_bridge", channel_name, xtor_slave_proxy_);
        fake_slave_ = std::make_shared<FakeSlave>("fake_slave", xtor_slave_proxy_);
        fake_vbuilder_ = std::make_shared<FakeVbuilder>("fake_vbuilder");
        axi_slave_tlm_proxy_->init_socket.bind(fake_vbuilder_->target_socket);
        stop_module_ = new ScStopModule("stopmodule");
        sim_thread_ = new std::thread([]() {
                std::cout << "sc before start" << std::endl;
                sc_core::sc_start();
                std::cout << "sc after start" << std::endl;
            });
            
    }

    void TearDown() override {
        stop_module_->stop_event.notify();
        if (sim_thread_->joinable()) {
            sim_thread_->join();
        }
        delete stop_module_;
        delete sim_thread_;
        sc_core::sc_curr_simcontext = new sc_core::sc_simcontext();
        sc_core::sc_default_global_context = nullptr;
        }

    void initialize_write_transaction(axi_transaction_item &trans, uint32_t data_size) {
        trans.data = std::vector<uint8_t>(data_size, 0);
        trans.be = std::vector<uint8_t>(data_size, 0xFF);  

        trans.cmd = umi_axi::WRITE;  
        trans.id = 0x123;            
        trans.addr = 0x1000;         
        trans.size = umi_axi::BYTE_4;  
        trans.len = 5;              
        trans.burst = umi_axi::INCR;  
        trans.data_width = umi_axi::BIT_32;  

        for (int i = 0; i < data_size; ++i) {
            trans.data[i] = static_cast<uint8_t>(i);  
        }

        std::cout << "Write Transaction initialized with " << data_size << " bytes." << std::endl;
}

    std::shared_ptr<axi_slave_proxy> xtor_slave_proxy_;
    std::shared_ptr<AxiSlaveBridge> axi_slave_bridge_;
    std::shared_ptr<AxiSlaveTlmProxy> axi_slave_tlm_proxy_;
    std::shared_ptr<FakeSlave> fake_slave_;
    std::shared_ptr<FakeVbuilder> fake_vbuilder_;
    ScStopModule* stop_module_;
    std::thread* sim_thread_;
};

TEST_F(AxiSlaveTest, R_INCR) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    trans.cmd = umi_axi::READ;
    trans.id = 0x123;
    trans.addr = 0x0;
    trans.size = size_type_t::BYTE_4;
    trans.len = 5;  
    trans.burst = umi_axi::burst_type_t::INCR;
    trans.data_width = umi_axi::bus_width_t::BIT_32;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);
  
    fake_slave_->seq.push_back(&trans);
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(fake_vbuilder_->response_lists.size(), 1);
    tlm::tlm_generic_payload& tlm_gp = *((fake_vbuilder_->response_lists)[0]);
    EXPECT_EQ(tlm_gp.get_command(), tlm::TLM_READ_COMMAND);
    EXPECT_EQ(tlm_gp.get_address(), 0x0);
    EXPECT_EQ(tlm_gp.get_data_length(), data_size);
    EXPECT_EQ(tlm_gp.get_byte_enable_length(), 0x0);
    EXPECT_EQ(tlm_gp.get_streaming_width(), 0x0);
    unsigned char* data_ptr = tlm_gp.get_data_ptr();
    unsigned int data_len = tlm_gp.get_data_length();
    for (int i = 0; i < data_len; ++i) {
            EXPECT_EQ(static_cast<uint8_t>(tlm_gp.get_data_ptr()[i]), static_cast<uint8_t>(i+tlm_gp.get_address()));
    }

    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x0);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 5);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::INCR);
    EXPECT_EQ(item.cmd, cmd_type_t::READ);
}

TEST_F(AxiSlaveTest, R_INCR_NARROW) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    trans.cmd = umi_axi::READ;
    trans.id = 0x123;
    trans.addr = 0x00;
    trans.size = size_type_t::BYTE_4;
    trans.len = 4;  
    trans.burst = umi_axi::burst_type_t::INCR;
    trans.data_width = umi_axi::bus_width_t::BIT_64;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);
  
    fake_slave_->seq.push_back(&trans);
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(fake_vbuilder_->response_lists.size(), 1);
    tlm::tlm_generic_payload& tlm_gp = *((fake_vbuilder_->response_lists)[0]);
    EXPECT_EQ(tlm_gp.get_command(), tlm::TLM_READ_COMMAND);
    EXPECT_EQ(tlm_gp.get_address(), 0x00);
    EXPECT_EQ(tlm_gp.get_data_length(), data_size);
    EXPECT_EQ(tlm_gp.get_byte_enable_length(), 0x0);
    EXPECT_EQ(tlm_gp.get_streaming_width(), 0x0);
    unsigned char* data_ptr = tlm_gp.get_data_ptr();
    unsigned int data_len = tlm_gp.get_data_length();
    for (int i = 0; i < data_len; ++i) {
            EXPECT_EQ(static_cast<uint8_t>(tlm_gp.get_data_ptr()[i]), static_cast<uint8_t>(i+tlm_gp.get_address()));
    }
    std::vector<uint8_t> expected_data = {
        0x00, 0x01, 0x02, 0x03, 0x00, 0x00, 0x00, 0x00, 
        0x04, 0x05, 0x06, 0x07, 0x04, 0x05, 0x06, 0x07,
        0x08, 0x09, 0x0a, 0x0b, 0x08, 0x09, 0x0a, 0x0b,
        0x0c, 0x0d, 0x0e, 0x0f, 0x0c, 0x0d, 0x0e, 0x0f,
        0x10, 0x11, 0x12, 0x13, 0x10, 0x11, 0x12, 0x13
    };

    std::vector<uint8_t> expected_be = {
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
    };
    
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x00);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 4);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::INCR);
    EXPECT_EQ(item.cmd, cmd_type_t::READ);
    for (int i = 0; i < item.data.size(); ++i) {
        EXPECT_EQ(item.data[i]&item.be[i], expected_data[i]&expected_be[i]);
    }
}

TEST_F(AxiSlaveTest, R_INCR_UNALIGN) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    trans.cmd = umi_axi::READ;
    trans.id = 0x123;
    trans.addr = 0x003;
    trans.size = size_type_t::BYTE_4;
    trans.len = 4;  
    trans.burst = umi_axi::burst_type_t::INCR;
    trans.data_width = umi_axi::bus_width_t::BIT_32;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);
  
    fake_slave_->seq.push_back(&trans);
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(fake_vbuilder_->response_lists.size(), 1);
    tlm::tlm_generic_payload& tlm_gp = *((fake_vbuilder_->response_lists)[0]);
    EXPECT_EQ(tlm_gp.get_command(), tlm::TLM_READ_COMMAND);
    EXPECT_EQ(tlm_gp.get_address(), 0x00);
    EXPECT_EQ(tlm_gp.get_data_length(), data_size);
    EXPECT_EQ(tlm_gp.get_byte_enable_length(), 0x0);
    EXPECT_EQ(tlm_gp.get_streaming_width(), 0x0);
    for (int i = 0; i < tlm_gp.get_data_length(); ++i) {
        EXPECT_EQ(static_cast<uint8_t>(tlm_gp.get_data_ptr()[i]), static_cast<uint8_t>(i+tlm_gp.get_address()));
    }

    std::vector<uint8_t> expected_data = {
        0x00, 0x01, 0x02, 0x03,
        0x04, 0x05, 0x06, 0x07, 
        0x08, 0x09, 0x0a, 0x0b,
        0x0c, 0x0d, 0x0e, 0x0f, 
        0x10, 0x11, 0x12, 0x13,
    };

    std::vector<uint8_t> expected_be = {
        0x00, 0x00, 0x00, 0xFF, 
        0xFF, 0xFF, 0xFF, 0xFF, 
        0xFF, 0xFF, 0xFF, 0xFF, 
        0xFF, 0xFF, 0xFF, 0xFF, 
        0xFF, 0xFF, 0xFF, 0xFF, 
    };
    
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x003);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 4);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::INCR);
    EXPECT_EQ(item.cmd, cmd_type_t::READ);
    for (int i = 0; i < item.data.size(); ++i) {
        EXPECT_EQ(item.data[i]&item.be[i], expected_data[i]&expected_be[i]);
    }
}

TEST_F(AxiSlaveTest, R_INCR_UNALIGN_NARROW) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    trans.cmd = umi_axi::READ;
    trans.id = 0x123;
    trans.addr = 0x007;
    trans.size = size_type_t::BYTE_4;
    trans.len = 4;  
    trans.burst = umi_axi::burst_type_t::INCR;
    trans.data_width = umi_axi::bus_width_t::BIT_64;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);
  
    fake_slave_->seq.push_back(&trans);
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(fake_vbuilder_->response_lists.size(), 1);
    tlm::tlm_generic_payload& tlm_gp = *((fake_vbuilder_->response_lists)[0]);
    EXPECT_EQ(tlm_gp.get_command(), tlm::TLM_READ_COMMAND);
    EXPECT_EQ(tlm_gp.get_address(), 0x04);
    EXPECT_EQ(tlm_gp.get_data_length(), data_size);
    EXPECT_EQ(tlm_gp.get_byte_enable_length(), 0x0);
    EXPECT_EQ(tlm_gp.get_streaming_width(), 0x0);
    for (int i = 0; i < tlm_gp.get_data_length(); ++i) {
            EXPECT_EQ(static_cast<uint8_t>(tlm_gp.get_data_ptr()[i]), static_cast<uint8_t>(i+tlm_gp.get_address()));
    }
    std::vector<uint8_t> expected_data = {
        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 
        0x08, 0x09, 0x0a, 0x0b, 0x08, 0x09, 0x0a, 0x0b,
        0x0c, 0x0d, 0x0e, 0x0f, 0x0c, 0x0d, 0x0e, 0x0f,
        0x10, 0x11, 0x12, 0x13, 0x10, 0x11, 0x12, 0x13,
        0x14, 0x15, 0x16, 0x17, 0x14, 0x15, 0x16, 0x17,
    };

    std::vector<uint8_t> expected_be = {
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF,
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF
    };
    
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x007);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 4);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::INCR);
    EXPECT_EQ(item.cmd, cmd_type_t::READ);
    for (int i = 0; i < item.data.size(); ++i) {
        EXPECT_EQ(item.data[i]&item.be[i], expected_data[i]&expected_be[i]);
    }
}

TEST_F(AxiSlaveTest, R_INCR_MULTI) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    std::vector<axi_transaction_item> transactions;
    const int MULTI_SIZE = 3;
    const int data_size = 12;
    for (int i = 0; i < MULTI_SIZE; ++i) {
        axi_transaction_item trans;
        trans.cmd = umi_axi::READ;
        trans.id = i;
        trans.addr = i * 0x08;
        trans.size = umi_axi::BYTE_4;
        trans.len = 2;
        trans.burst = umi_axi::INCR;
        trans.data_width = umi_axi::bus_width_t::BIT_32;
        transactions.push_back(trans);
    }
    
    
    for (size_t i = 0; i < transactions.size(); ++i) {
        auto& trans = transactions[i];
        fake_slave_->seq.push_back(&trans);
    }
    std::cout << "Send axi item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "Send axi item notify end" << std::endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(fake_vbuilder_->response_lists.size(), MULTI_SIZE);
    for (size_t i = 0; i < MULTI_SIZE; ++i) {
        tlm::tlm_generic_payload& tlm_gp = *((fake_vbuilder_->response_lists)[i]);
        std::cout << "TLM GP Address[" << i << "]: " << &fake_vbuilder_->response_lists[i] << std::endl;
        EXPECT_EQ(tlm_gp.get_command(), tlm::TLM_READ_COMMAND);
        // EXPECT_EQ(tlm_gp.get_address(), 0x08 * i);  only reserve last trans
        EXPECT_EQ(tlm_gp.get_data_length(), data_size);
        EXPECT_EQ(tlm_gp.get_byte_enable_length(), 0x0);
        EXPECT_EQ(tlm_gp.get_streaming_width(), 0x0);
        for (int j = 0; j < tlm_gp.get_data_length(); ++j) {
                EXPECT_EQ(static_cast<uint8_t>(tlm_gp.get_data_ptr()[j]), static_cast<uint8_t>(j+tlm_gp.get_address()));
        }
    } 

    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), MULTI_SIZE);
    for (int i = 0; i < xtor_slave_proxy_->response_lists_.size(); ++i) {
        axi_transaction_item& item = xtor_slave_proxy_->response_lists_[i];
        EXPECT_EQ(item.addr, i * 0x8);
        EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, 2);
        EXPECT_EQ(item.size, size_type_t::BYTE_4);
        EXPECT_EQ(item.burst, burst_type_t::INCR);
        EXPECT_EQ(item.cmd, cmd_type_t::READ);
    }
}

TEST_F(AxiSlaveTest, R_FIXED) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    trans.cmd = umi_axi::READ;
    trans.id = 0x123;
    trans.addr = 0x0;
    trans.size = size_type_t::BYTE_4;
    trans.len = 5;  
    trans.data_width = umi_axi::bus_width_t::BIT_32;
    trans.burst = umi_axi::burst_type_t::FIXED;
    trans.req_user_width = 0;
    trans.data_r_user_width = 0;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);
  
  
    fake_slave_->seq.push_back(&trans);
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(fake_vbuilder_->response_lists.size(), 1);
    tlm::tlm_generic_payload& tlm_gp = *((fake_vbuilder_->response_lists)[0]);
    EXPECT_EQ(tlm_gp.get_command(), tlm::TLM_READ_COMMAND);
    EXPECT_EQ(tlm_gp.get_address(), 0x0);
    EXPECT_EQ(tlm_gp.get_data_length(), data_size);
    EXPECT_EQ(tlm_gp.get_byte_enable_length(), 0x0);
    EXPECT_EQ(tlm_gp.get_streaming_width(), 1 << trans.size);
    for (int i = 0; i < tlm_gp.get_data_length(); ++i) {
        EXPECT_EQ(static_cast<uint8_t>(tlm_gp.get_data_ptr()[i]), static_cast<uint8_t>(i+tlm_gp.get_address()));
    }
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x0);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 5);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::FIXED);
    EXPECT_EQ(item.cmd, cmd_type_t::READ);
}

TEST_F(AxiSlaveTest, R_FIXED_UNALIGN) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    trans.cmd = umi_axi::READ;
    trans.id = 0x123;
    trans.addr = 0x003;
    trans.size = size_type_t::BYTE_4;
    trans.len = 4;  
    trans.burst = umi_axi::burst_type_t::FIXED;
    trans.data_width = umi_axi::bus_width_t::BIT_32;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);
  
    fake_slave_->seq.push_back(&trans);
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(fake_vbuilder_->response_lists.size(), 1);
    tlm::tlm_generic_payload& tlm_gp = *((fake_vbuilder_->response_lists)[0]);
    EXPECT_EQ(tlm_gp.get_command(), tlm::TLM_READ_COMMAND);
    EXPECT_EQ(tlm_gp.get_address(), 0x0);
    EXPECT_EQ(tlm_gp.get_data_length(), data_size);
    EXPECT_EQ(tlm_gp.get_byte_enable_length(), 0x0);
    EXPECT_EQ(tlm_gp.get_streaming_width(), 1 << trans.size);
    for (int i = 0; i < tlm_gp.get_data_length(); ++i) {
        EXPECT_EQ(static_cast<uint8_t>(tlm_gp.get_data_ptr()[i]), static_cast<uint8_t>(i+tlm_gp.get_address()));
    }
    std::vector<uint8_t> expected_data = {
        0x00, 0x01, 0x02, 0x03,
        0x04, 0x05, 0x06, 0x07, 
        0x08, 0x09, 0x0a, 0x0b,
        0x0c, 0x0d, 0x0e, 0x0f, 
        0x10, 0x11, 0x12, 0x13,
    };

    std::vector<uint8_t> expected_be = {
        0x00, 0x00, 0x00, 0xFF, 
        0x00, 0x00, 0x00, 0xFF, 
        0x00, 0x00, 0x00, 0xFF, 
        0x00, 0x00, 0x00, 0xFF, 
        0x00, 0x00, 0x00, 0xFF, 
    };
    
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x003);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 4);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::FIXED);
    EXPECT_EQ(item.cmd, cmd_type_t::READ);
    for (int i = 0; i < item.data.size(); ++i) {
        EXPECT_EQ(item.data[i]&item.be[i], expected_data[i]&expected_be[i]);
    }

}

TEST_F(AxiSlaveTest, R_FIXED_NARROW) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    trans.cmd = umi_axi::READ;
    trans.id = 0x123;
    trans.addr = 0x0004;
    trans.size = size_type_t::BYTE_4;
    trans.len = 4;  
    trans.burst = umi_axi::burst_type_t::FIXED;
    trans.data_width = umi_axi::bus_width_t::BIT_64;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);
  
    fake_slave_->seq.push_back(&trans);
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(fake_vbuilder_->response_lists.size(), 1);
    tlm::tlm_generic_payload& tlm_gp = *((fake_vbuilder_->response_lists)[0]);
    EXPECT_EQ(tlm_gp.get_command(), tlm::TLM_READ_COMMAND);
    EXPECT_EQ(tlm_gp.get_address(), 0x04);
    EXPECT_EQ(tlm_gp.get_data_length(), data_size);
    EXPECT_EQ(tlm_gp.get_byte_enable_length(), 0x0);
    EXPECT_EQ(tlm_gp.get_streaming_width(), 1 << trans.size);
    for (int i = 0; i < tlm_gp.get_data_length(); ++i) {
        EXPECT_EQ(static_cast<uint8_t>(tlm_gp.get_data_ptr()[i]), static_cast<uint8_t>(i+tlm_gp.get_address()));
    }
    std::vector<uint8_t> expected_data = {
        0x04, 0x05, 0x06, 0x07, 0x04, 0x05, 0x06, 0x07,
        0x08, 0x09, 0x0a, 0x0b, 0x08, 0x09, 0x0a, 0x0b,
        0x0c, 0x0d, 0x0e, 0x0f, 0x0c, 0x0d, 0x0e, 0x0f, 
        0x10, 0x11, 0x12, 0x13, 0x10, 0x11, 0x12, 0x13,
        0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
    };

    std::vector<uint8_t> expected_be = {
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
    };
    
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x0004);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 4);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::FIXED);
    EXPECT_EQ(item.cmd, cmd_type_t::READ);
    for (int i = 0; i < item.data.size(); ++i) {
        EXPECT_EQ(item.data[i]&item.be[i], expected_data[i]&expected_be[i]);
    }
}

TEST_F(AxiSlaveTest, R_FIXED_UNALIGN_NARROW) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    trans.cmd = umi_axi::READ;
    trans.id = 0x123;
    trans.addr = 0x05;
    trans.size = size_type_t::BYTE_4;
    trans.len = 4;  
    trans.burst = umi_axi::burst_type_t::FIXED;
    trans.data_width = umi_axi::bus_width_t::BIT_64;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);
  
    fake_slave_->seq.push_back(&trans);
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(fake_vbuilder_->response_lists.size(), 1);
    tlm::tlm_generic_payload& tlm_gp = *((fake_vbuilder_->response_lists)[0]);
    EXPECT_EQ(tlm_gp.get_command(), tlm::TLM_READ_COMMAND);
    EXPECT_EQ(tlm_gp.get_address(), 0x04);
    EXPECT_EQ(tlm_gp.get_data_length(), data_size);
    EXPECT_EQ(tlm_gp.get_byte_enable_length(), 0x0);
    EXPECT_EQ(tlm_gp.get_streaming_width(), 1 << trans.size);
    for (int i = 0; i < tlm_gp.get_data_length(); ++i) {
        EXPECT_EQ(static_cast<uint8_t>(tlm_gp.get_data_ptr()[i]), static_cast<uint8_t>(i+tlm_gp.get_address()));
    }
    std::vector<uint8_t> expected_data = {
        0x04, 0x05, 0x06, 0x07, 0x04, 0x05, 0x06, 0x07,
        0x08, 0x09, 0x0a, 0x0b, 0x08, 0x09, 0x0a, 0x0b,
        0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,  
        0x10, 0x11, 0x12, 0x13, 0x10, 0x11, 0x12, 0x13,
        0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
    };

    std::vector<uint8_t> expected_be = {
        0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF,
        0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF,
        0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF,
        0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF,
        0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF,
    };
    
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x05);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 4);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::FIXED);
    EXPECT_EQ(item.cmd, cmd_type_t::READ);
    for (int i = 0; i < item.data.size(); ++i) {
        EXPECT_EQ(item.data[i]&item.be[i], expected_data[i]&expected_be[i]);
    }
}

TEST_F(AxiSlaveTest, R_FIXED_MULTI) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    std::vector<axi_transaction_item> transactions;
    const int MULTI_SIZE = 3;
    const int data_size = 12;
    for (int i = 0; i < MULTI_SIZE; ++i) {
        axi_transaction_item trans;
        trans.cmd = umi_axi::READ;
        trans.id = i;
        trans.addr = i * 0x08;
        trans.size = umi_axi::BYTE_4;
        trans.len = 2;
        trans.burst = umi_axi::FIXED;
        trans.data_width = umi_axi::bus_width_t::BIT_32;
        transactions.push_back(trans);
    }
    
    std::vector<uint32_t> expected_sizes(MULTI_SIZE, 4096);
    for (size_t i = 0; i < transactions.size(); ++i) {
        auto& trans = transactions[i];
        fake_slave_->seq.push_back(&trans);
    }
    std::cout << "Send axi item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "Send axi item notify end" << std::endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(fake_vbuilder_->response_lists.size(), MULTI_SIZE);
    for (size_t i = 0; i < MULTI_SIZE; ++i) {
        tlm::tlm_generic_payload& tlm_gp = *((fake_vbuilder_->response_lists)[i]);
        EXPECT_EQ(tlm_gp.get_command(), tlm::TLM_READ_COMMAND);
        EXPECT_EQ(tlm_gp.get_data_length(), data_size);
        EXPECT_EQ(tlm_gp.get_byte_enable_length(), 0x0);
        EXPECT_EQ(tlm_gp.get_streaming_width(), 4);
        for (int j = 0; j < tlm_gp.get_data_length(); ++j) {
            EXPECT_EQ(static_cast<uint8_t>(tlm_gp.get_data_ptr()[j]), static_cast<uint8_t>(j+tlm_gp.get_address()));
        } 
    }

    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), MULTI_SIZE);
    for (int i = 0; i < xtor_slave_proxy_->response_lists_.size(); ++i) {
        axi_transaction_item& item = xtor_slave_proxy_->response_lists_[i];
        EXPECT_EQ(item.addr, i * 0x08);
        EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, 2);
        EXPECT_EQ(item.size, size_type_t::BYTE_4);
        EXPECT_EQ(item.burst, burst_type_t::FIXED);
        EXPECT_EQ(item.cmd, cmd_type_t::READ);
    }
}

TEST_F(AxiSlaveTest, R_WRAP) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    trans.cmd = umi_axi::READ;
    trans.id = 0x123;
    trans.addr = 0x034;
    trans.size = size_type_t::BYTE_4;
    trans.len = 5;  
    trans.data_width = umi_axi::bus_width_t::BIT_32;
    trans.burst = umi_axi::burst_type_t::WRAP;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);
  
    fake_slave_->seq.push_back(&trans);
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(fake_vbuilder_->response_lists.size(), 1);
    tlm::tlm_generic_payload& tlm_gp = *((fake_vbuilder_->response_lists)[0]);
    EXPECT_EQ(tlm_gp.get_command(), tlm::TLM_READ_COMMAND);
    EXPECT_EQ(tlm_gp.get_address(), 0x030);
    EXPECT_EQ(tlm_gp.get_data_length(), data_size);
    EXPECT_EQ(tlm_gp.get_byte_enable_length(), 0x0);
    EXPECT_EQ(tlm_gp.get_streaming_width(), 0x0);
    for (int i = 0; i < tlm_gp.get_data_length(); ++i) {
        EXPECT_EQ(static_cast<uint8_t>(tlm_gp.get_data_ptr()[i]), static_cast<uint8_t>(i+tlm_gp.get_address()));
    }
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x0034);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 5);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::WRAP);
    EXPECT_EQ(item.cmd, cmd_type_t::READ);
}

TEST_F(AxiSlaveTest, R_WRAP_NARROW) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    trans.cmd = umi_axi::READ;
    trans.id = 0x123;
    trans.addr = 0x0034;
    trans.size = size_type_t::BYTE_4;
    trans.len = 5;  
    trans.burst = umi_axi::burst_type_t::WRAP;
    trans.data_width = umi_axi::bus_width_t::BIT_64;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);
  
    fake_slave_->seq.push_back(&trans);
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(fake_vbuilder_->response_lists.size(), 1);
    tlm::tlm_generic_payload& tlm_gp = *((fake_vbuilder_->response_lists)[0]);
    EXPECT_EQ(tlm_gp.get_command(), tlm::TLM_READ_COMMAND);
    EXPECT_EQ(tlm_gp.get_address(), 0x030);
    EXPECT_EQ(tlm_gp.get_data_length(), data_size);
    EXPECT_EQ(tlm_gp.get_byte_enable_length(), 0x0);
    EXPECT_EQ(tlm_gp.get_streaming_width(), 0x0);
    for (int i = 0; i < tlm_gp.get_data_length(); ++i) {
        EXPECT_EQ(static_cast<uint8_t>(tlm_gp.get_data_ptr()[i]), static_cast<uint8_t>(i+tlm_gp.get_address()));
    }
    std::vector<uint8_t> expected_data = {
        0x00, 0x00, 0x00, 0x00, 0x34, 0x35, 0x36, 0x37, 
        0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x08, 0x09, 0x0a, 
        0x04, 0x05, 0x06, 0x07, 0x3c, 0x3d, 0x3e, 0x3f, 
        0x40, 0x41, 0x42, 0x43, 0x10, 0x11, 0x12, 0x13,
        0x0c, 0x0d, 0x0e, 0x0f, 0x44, 0x45, 0x46, 0x47, 
        0x30, 0x31, 0x32, 0x33, 0x00, 0x00, 0x00, 0x00
    };

    std::vector<uint8_t> expected_be = {
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
    };
    
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x0034);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 5);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::WRAP);
    EXPECT_EQ(item.cmd, cmd_type_t::READ);
    for (int i = 0; i < item.data.size(); ++i) {
        EXPECT_EQ(item.data[i]&item.be[i], expected_data[i]&expected_be[i]);
    }
}

TEST_F(AxiSlaveTest, R_WRAP_MULTI) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    std::vector<axi_transaction_item> transactions;
    const int MULTI_SIZE = 5;
    const int data_size = 12;
    for (int i = 0; i < MULTI_SIZE; ++i) {
        axi_transaction_item trans;
        trans.cmd = umi_axi::READ;
        trans.id = i;
        trans.addr = i * 0x08;
        trans.size = umi_axi::BYTE_4;
        trans.len = 2;
        trans.burst = umi_axi::WRAP;
        trans.data_width = umi_axi::bus_width_t::BIT_32;
        transactions.push_back(trans);
    }
    
    for (size_t i = 0; i < transactions.size(); ++i) {
        auto& trans = transactions[i];
        fake_slave_->seq.push_back(&trans);
    }
    std::cout << "Send axi item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "Send axi item notify end" << std::endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(fake_vbuilder_->response_lists.size(), MULTI_SIZE);
    for (size_t i = 0; i < MULTI_SIZE; ++i) {
        tlm::tlm_generic_payload& tlm_gp = *((fake_vbuilder_->response_lists)[i]);
        EXPECT_EQ(tlm_gp.get_command(), tlm::TLM_READ_COMMAND);
        EXPECT_EQ(tlm_gp.get_data_length(), data_size);
        EXPECT_EQ(tlm_gp.get_byte_enable_length(), 0x0);
        EXPECT_EQ(tlm_gp.get_streaming_width(), 0x0);
        for (int j = 0; j < tlm_gp.get_data_length(); ++j) {
            EXPECT_EQ(static_cast<uint8_t>(tlm_gp.get_data_ptr()[j]), static_cast<uint8_t>(j+tlm_gp.get_address()));
        }
    }

    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), MULTI_SIZE);
    for (int i = 0; i < xtor_slave_proxy_->response_lists_.size(); ++i) {
        axi_transaction_item& item = xtor_slave_proxy_->response_lists_[i];
        EXPECT_EQ(item.addr, i * 0x008);
        EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, 2);
        EXPECT_EQ(item.size, size_type_t::BYTE_4);
        EXPECT_EQ(item.burst, burst_type_t::WRAP);
        EXPECT_EQ(item.cmd, cmd_type_t::READ);
    }
}

TEST_F(AxiSlaveTest, W_INCR) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    trans.cmd = umi_axi::WRITE;
    trans.id = 0x123;
    trans.addr = 0x0;
    trans.size = size_type_t::BYTE_4;
    trans.len = 0x2;  
    trans.burst = umi_axi::burst_type_t::INCR;
    trans.data_width = umi_axi::bus_width_t::BIT_32;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);
    trans.data = std::vector<uint8_t>(data_size, 0);
    trans.be = std::vector<uint8_t>(data_size, 0xFF);
    for (int i = 0; i < data_size; ++i) {
        trans.data[i] = i % 256;
    }  
  
    fake_slave_->seq.push_back(&trans);
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(fake_vbuilder_->response_lists.size(), 1);
    tlm::tlm_generic_payload& tlm_gp = *((fake_vbuilder_->response_lists)[0]);
    EXPECT_EQ(tlm_gp.get_command(), tlm::TLM_WRITE_COMMAND);
    EXPECT_EQ(tlm_gp.get_address(), 0x0);
    EXPECT_EQ(tlm_gp.get_data_length(), data_size);
    EXPECT_EQ(tlm_gp.get_byte_enable_length(), data_size);
    EXPECT_EQ(tlm_gp.get_streaming_width(), 0x0);
    for (int i = 0; i < tlm_gp.get_data_length(); ++i) {
        EXPECT_EQ(static_cast<uint8_t>(tlm_gp.get_data_ptr()[i]), i % 256);
    }
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x0);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 2);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::INCR);
    EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
}

TEST_F(AxiSlaveTest, W_INCR_NO_BE) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    trans.cmd = umi_axi::WRITE;
    trans.id = 0x123;
    trans.addr = 0x00;
    trans.size = size_type_t::BYTE_4;
    trans.len = 2;  
    trans.burst = umi_axi::burst_type_t::INCR;
    trans.data_width = umi_axi::bus_width_t::BIT_32;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);
    trans.data = std::vector<uint8_t>(data_size, 0);
    for (int i = 0; i < data_size; ++i) {
        trans.data[i] = i % 256;
    }  
  
    fake_slave_->seq.push_back(&trans);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(fake_vbuilder_->response_lists.size(), 1);
    tlm::tlm_generic_payload& tlm_gp = *((fake_vbuilder_->response_lists)[0]);
    EXPECT_EQ(tlm_gp.get_command(), tlm::TLM_WRITE_COMMAND);
    EXPECT_EQ(tlm_gp.get_address(), 0x0);
    EXPECT_EQ(tlm_gp.get_data_length(), data_size);
    EXPECT_EQ(tlm_gp.get_byte_enable_length(), 0);
    EXPECT_EQ(tlm_gp.get_streaming_width(), 0x0);
    for (int i = 0; i < tlm_gp.get_data_length(); ++i) {
        EXPECT_EQ(static_cast<uint8_t>(tlm_gp.get_data_ptr()[i]), i % 256);
    }
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x0);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 2);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::INCR);
    EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
}

TEST_F(AxiSlaveTest, W_INCR_NARROW) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    initialize_write_transaction(trans, 256);
    trans.cmd = umi_axi::WRITE;
    trans.id = 0x123;
    trans.addr = 0x0;
    trans.size = size_type_t::BYTE_4;
    trans.len = 4;  
    trans.burst = umi_axi::burst_type_t::INCR;
    trans.data_width = umi_axi::bus_width_t::BIT_64;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);
  
    fake_slave_->seq.push_back(&trans);
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
  
    std::vector<uint8_t> expected_data = {
        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
        0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F,
        0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
        0x18, 0x19, 0x1A, 0x1B, 0x1C, 0x1D, 0x1E, 0x1F,
        0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27
    };

    std::vector<uint8_t> expected_be = {
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
    };

    std::vector<uint8_t> expected_result = {
        0x00, 0x01, 0x02, 0x03,
        0x0c, 0x0d, 0x0e, 0x0f, 
        0x10, 0x11, 0x12, 0x13,
        0x1c, 0x1d, 0x1e, 0x1f,
        0x20, 0x21, 0x22, 0x23,
    };

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(fake_vbuilder_->response_lists.size(), 1);
    tlm::tlm_generic_payload& tlm_gp = *((fake_vbuilder_->response_lists)[0]);
    EXPECT_EQ(tlm_gp.get_command(), tlm::TLM_WRITE_COMMAND);
    EXPECT_EQ(tlm_gp.get_address(), 0x0);
    EXPECT_EQ(tlm_gp.get_data_length(), data_size);
    EXPECT_EQ(tlm_gp.get_byte_enable_length(), data_size);
    EXPECT_EQ(tlm_gp.get_streaming_width(), 0x0);
    for (int i = 0; i < tlm_gp.get_data_length(); ++i) {
        EXPECT_EQ(static_cast<uint8_t>(tlm_gp.get_data_ptr()[i]), expected_result[i]);
    }
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x0);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 4);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::INCR);
    EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
}

TEST_F(AxiSlaveTest, W_INCR_UNALIGN) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    initialize_write_transaction(trans, 256);
    trans.cmd = umi_axi::WRITE;
    trans.id = 0x123;
    trans.addr = 0x01;
    trans.size = size_type_t::BYTE_4;
    trans.len = 4;  
    trans.burst = umi_axi::burst_type_t::INCR;
    trans.data_width = umi_axi::bus_width_t::BIT_32;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);
  
    fake_slave_->seq.push_back(&trans);
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;

    std::vector<uint8_t> expected_result = {
        0x00, 0x01, 0x02, 0x03,
        0x04, 0x05, 0x06, 0x07,
        0x08, 0x09, 0x0A, 0x0B,
        0x0C, 0x0D, 0x0E, 0x0F,
        0x10, 0x11, 0x12, 0x13,
    };

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(fake_vbuilder_->response_lists.size(), 1);
    tlm::tlm_generic_payload& tlm_gp = *((fake_vbuilder_->response_lists)[0]);
    EXPECT_EQ(tlm_gp.get_command(), tlm::TLM_WRITE_COMMAND);
    EXPECT_EQ(tlm_gp.get_address(), 0x0);
    EXPECT_EQ(tlm_gp.get_data_length(), data_size);
    EXPECT_EQ(tlm_gp.get_byte_enable_length(), data_size);
    EXPECT_EQ(tlm_gp.get_streaming_width(), 0x0);
    for (int i = 0; i < tlm_gp.get_data_length(); ++i) {
        EXPECT_EQ(static_cast<uint8_t>(tlm_gp.get_data_ptr()[i]), expected_result[i]);
    }
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x1);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 4);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::INCR);
    EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
}

TEST_F(AxiSlaveTest, W_INCR_UNALIGN_NARROW) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    initialize_write_transaction(trans, 256);
    trans.cmd = umi_axi::WRITE;
    trans.id = 0x123;
    trans.addr = 0x03;
    trans.size = size_type_t::BYTE_4;
    trans.len = 4;  
    trans.burst = umi_axi::burst_type_t::INCR;
    trans.data_width = umi_axi::bus_width_t::BIT_64;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);
  
    fake_slave_->seq.push_back(&trans);
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    std::vector<uint8_t> expected_data = {
        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
        0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F,
        0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
        0x18, 0x19, 0x1A, 0x1B, 0x1C, 0x1D, 0x1E, 0x1F,
        0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27
    };

    std::vector<uint8_t> expected_be = {
        0x00, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
    };

    std::vector<uint8_t> expected_result = {
        0x00, 0x00, 0x00, 0x03,
        0x0c, 0x0d, 0x0e, 0x0f, 
        0x10, 0x11, 0x12, 0x13,
        0x1c, 0x1d, 0x1e, 0x1f,
        0x20, 0x21, 0x22, 0x23,
    };

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(fake_vbuilder_->response_lists.size(), 1);
    tlm::tlm_generic_payload& tlm_gp = *((fake_vbuilder_->response_lists)[0]);
    EXPECT_EQ(tlm_gp.get_command(), tlm::TLM_WRITE_COMMAND);
    EXPECT_EQ(tlm_gp.get_address(), 0x0);
    EXPECT_EQ(tlm_gp.get_data_length(), data_size);
    EXPECT_EQ(tlm_gp.get_byte_enable_length(), data_size);
    EXPECT_EQ(tlm_gp.get_streaming_width(), 0x0);
    for (int i = 0; i < tlm_gp.get_data_length(); ++i) {
        EXPECT_EQ(static_cast<uint8_t>(tlm_gp.get_data_ptr()[i]), expected_result[i]);
    }
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x03);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 4);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::INCR);
    EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
}

TEST_F(AxiSlaveTest, W_INCR_MULTI) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    std::vector<axi_transaction_item> transactions;
    const int MULTI_SIZE = 5;
    const int DATA_SIZE = 12;
    for (int i = 0; i < MULTI_SIZE; ++i) {
        axi_transaction_item trans;
        trans.data = std::vector<uint8_t>(DATA_SIZE, 0);
        trans.be = std::vector<uint8_t>(DATA_SIZE, 0xFF);
        trans.cmd = umi_axi::WRITE;
        trans.id = i;
        trans.addr = i * 0x10;
        trans.size = umi_axi::BYTE_4;
        trans.len = 2;
        trans.burst = umi_axi::INCR;
        trans.data_width = umi_axi::bus_width_t::BIT_32;
        for (int j = 0; j < DATA_SIZE; ++j) {
            trans.data[j] = j % 256;
        }  
        transactions.push_back(trans);
    }
    
    for (size_t i = 0; i < transactions.size(); ++i) {
        auto& trans = transactions[i];
        fake_slave_->seq.push_back(&trans);
    }
    std::cout << "Send axi item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "Send axi item notify end" << std::endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(fake_vbuilder_->response_lists.size(), MULTI_SIZE);
    for (size_t i = 0; i < MULTI_SIZE; ++i) {
        std::cout << "Receive burst transaction " << i << " of " << transactions.size() << std::endl;
        // Verify received data
        tlm::tlm_generic_payload& tlm_gp = *((fake_vbuilder_->response_lists)[0]);
        EXPECT_EQ(tlm_gp.get_command(), tlm::TLM_WRITE_COMMAND);
        EXPECT_EQ(tlm_gp.get_data_length(), DATA_SIZE);
        EXPECT_EQ(tlm_gp.get_byte_enable_length(), DATA_SIZE);
        EXPECT_EQ(tlm_gp.get_streaming_width(), 0x0);
        for (int j = 0; j < tlm_gp.get_data_length(); ++j) {
            EXPECT_EQ(static_cast<uint8_t>(tlm_gp.get_data_ptr()[j]), j % 256);
        }
    }

    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), MULTI_SIZE);
    for (int i = 0; i < xtor_slave_proxy_->response_lists_.size(); ++i) {
        axi_transaction_item& item = xtor_slave_proxy_->response_lists_[i];
        EXPECT_EQ(item.addr, i * 0x10);
        EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, 2);
        EXPECT_EQ(item.size, size_type_t::BYTE_4);
        EXPECT_EQ(item.burst, burst_type_t::INCR);
        EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
    }
}

TEST_F(AxiSlaveTest, W_FIXED) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    trans.cmd = umi_axi::WRITE;
    trans.id = 0x123;
    trans.addr = 0x0;
    trans.size = size_type_t::BYTE_4;
    trans.len = 2;  
    trans.burst = umi_axi::burst_type_t::FIXED;
    trans.data_width = umi_axi::bus_width_t::BIT_32;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);
    trans.data = std::vector<uint8_t>(data_size, 0);
    trans.be = std::vector<uint8_t>(data_size, 0xFF);
    for (int i = 0; i < data_size; ++i) {
        trans.data[i] = i % 256;
    }  
  
    fake_slave_->seq.push_back(&trans);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(fake_vbuilder_->response_lists.size(), 1);
    tlm::tlm_generic_payload& tlm_gp = *((fake_vbuilder_->response_lists)[0]);
    EXPECT_EQ(tlm_gp.get_command(), tlm::TLM_WRITE_COMMAND);
    EXPECT_EQ(tlm_gp.get_address(), 0x0);
    EXPECT_EQ(tlm_gp.get_data_length(), data_size);
    EXPECT_EQ(tlm_gp.get_byte_enable_length(), data_size);
    EXPECT_EQ(tlm_gp.get_streaming_width(), 1 << trans.size);
    for (int i = 0; i < tlm_gp.get_data_length(); ++i) {
        EXPECT_EQ(static_cast<uint8_t>(tlm_gp.get_data_ptr()[i]), i % 256);
    }
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x0);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 2);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::FIXED);
    EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
}

TEST_F(AxiSlaveTest, W_FIXED_NARROW) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    initialize_write_transaction(trans, 256);
    trans.cmd = umi_axi::WRITE;
    trans.id = 0x123;
    trans.addr = 0x0;
    trans.size = size_type_t::BYTE_4;
    trans.len = 4;  
    trans.burst = umi_axi::burst_type_t::FIXED;
    trans.data_width = umi_axi::bus_width_t::BIT_64;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);
  
    fake_slave_->seq.push_back(&trans);
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    std::vector<uint8_t> expected_data = {
        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
        0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F,
        0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
        0x18, 0x19, 0x1A, 0x1B, 0x1C, 0x1D, 0x1E, 0x1F,
        0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27
    };

    std::vector<uint8_t> expected_be = {
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
    };

    std::vector<uint8_t> expected_result = {
        0x00, 0x01, 0x02, 0x03,
        0x08, 0x09, 0x0A, 0x0B, 
        0x10, 0x11, 0x12, 0x13,
        0x18, 0x19, 0x1A, 0x1B,
        0x20, 0x21, 0x22, 0x23,
    };

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(fake_vbuilder_->response_lists.size(), 1);
    tlm::tlm_generic_payload& tlm_gp = *((fake_vbuilder_->response_lists)[0]);
    EXPECT_EQ(tlm_gp.get_command(), tlm::TLM_WRITE_COMMAND);
    EXPECT_EQ(tlm_gp.get_address(), 0x0);
    EXPECT_EQ(tlm_gp.get_data_length(), data_size);
    EXPECT_EQ(tlm_gp.get_byte_enable_length(), data_size);
    EXPECT_EQ(tlm_gp.get_streaming_width(), 1 << trans.size);
    for (int i = 0; i < tlm_gp.get_data_length(); ++i) {
        EXPECT_EQ(static_cast<uint8_t>(tlm_gp.get_data_ptr()[i]), expected_result[i]);
    }
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x00);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 4);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::FIXED);
    EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
}

TEST_F(AxiSlaveTest, W_FIXED_UNALIGN) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    initialize_write_transaction(trans, 256);
    trans.cmd = umi_axi::WRITE;
    trans.id = 0x123;
    trans.addr = 0x1;
    trans.size = size_type_t::BYTE_4;
    trans.len = 4;  
    trans.burst = umi_axi::burst_type_t::FIXED;
    trans.data_width = umi_axi::bus_width_t::BIT_32;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);
  
    fake_slave_->seq.push_back(&trans);
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;

    std::vector<uint8_t> expected_result = {
        0x00, 0x01, 0x02, 0x03,
        0x00, 0x05, 0x06, 0x07,
        0x00, 0x09, 0x0A, 0x0B,
        0x00, 0x0D, 0x0E, 0x0F,
        0x00, 0x11, 0x12, 0x13,
    };

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(fake_vbuilder_->response_lists.size(), 1);
    tlm::tlm_generic_payload& tlm_gp = *((fake_vbuilder_->response_lists)[0]);
    EXPECT_EQ(tlm_gp.get_command(), tlm::TLM_WRITE_COMMAND);
    EXPECT_EQ(tlm_gp.get_address(), 0x0);
    EXPECT_EQ(tlm_gp.get_data_length(), data_size);
    EXPECT_EQ(tlm_gp.get_byte_enable_length(), data_size);
    EXPECT_EQ(tlm_gp.get_streaming_width(), 1 << trans.size);
    for (int i = 0; i < tlm_gp.get_data_length(); ++i) {
        EXPECT_EQ(static_cast<uint8_t>(tlm_gp.get_data_ptr()[i]), expected_result[i]);
    }
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x1);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 4);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::FIXED);
    EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
}

TEST_F(AxiSlaveTest, W_FIXED_UNALIGN_NARROW) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    initialize_write_transaction(trans, 256);
    trans.cmd = umi_axi::WRITE;
    trans.id = 0x123;
    trans.addr = 0x3;
    trans.size = size_type_t::BYTE_4;
    trans.len = 4;  
    trans.burst = umi_axi::burst_type_t::FIXED;
    trans.data_width = umi_axi::bus_width_t::BIT_64;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);
  
    fake_slave_->seq.push_back(&trans);
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    std::vector<uint8_t> expected_data = {
        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
        0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F,
        0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
        0x18, 0x19, 0x1A, 0x1B, 0x1C, 0x1D, 0x1E, 0x1F,
        0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27
    };

    std::vector<uint8_t> expected_be = {
        0x00, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00, 0x00,
    };

    std::vector<uint8_t> expected_result = {
        0x00, 0x00, 0x00, 0x03,
        0x00, 0x00, 0x00, 0x0B, 
        0x00, 0x00, 0x00, 0x13,
        0x00, 0x00, 0x00, 0x1B,
        0x00, 0x00, 0x00, 0x23,
    };

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(fake_vbuilder_->response_lists.size(), 1);
    tlm::tlm_generic_payload& tlm_gp = *((fake_vbuilder_->response_lists)[0]);
    EXPECT_EQ(tlm_gp.get_command(), tlm::TLM_WRITE_COMMAND);
    EXPECT_EQ(tlm_gp.get_address(), 0x00);
    EXPECT_EQ(tlm_gp.get_data_length(), data_size);
    EXPECT_EQ(tlm_gp.get_byte_enable_length(), data_size);
    EXPECT_EQ(tlm_gp.get_streaming_width(), 1 << trans.size);
    for (int i = 0; i < tlm_gp.get_data_length(); ++i) {
        EXPECT_EQ(static_cast<uint8_t>(tlm_gp.get_data_ptr()[i]), expected_result[i]);
    }
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x3);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 4);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::FIXED);
    EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
}

TEST_F(AxiSlaveTest, W_FIXED_MULTI) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    std::vector<axi_transaction_item> transactions;
    const int MULTI_SIZE = 5;
    const int DATA_SIZE = 12;
    for (int i = 0; i < MULTI_SIZE; ++i) {
        axi_transaction_item trans;
        trans.data = std::vector<uint8_t>(DATA_SIZE, 0);
        trans.be = std::vector<uint8_t>(DATA_SIZE, 0xFF);
        trans.cmd = umi_axi::WRITE;
        trans.id = i;
        trans.addr = i * 0x10;
        trans.size = umi_axi::BYTE_4;
        trans.len = 2;
        trans.burst = umi_axi::FIXED;
        trans.data_width = umi_axi::bus_width_t::BIT_32;
        for (int j = 0; j < DATA_SIZE; ++j) {
            trans.data[j] = j % 256;
        }  
        transactions.push_back(trans);
    }
    
    for (size_t i = 0; i < transactions.size(); ++i) {
        auto& trans = transactions[i];
        fake_slave_->seq.push_back(&trans);
    }
    std::cout << "Send axi item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "Send axi item notify end" << std::endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(fake_vbuilder_->response_lists.size(), MULTI_SIZE);
    for (size_t i = 0; i < MULTI_SIZE; ++i) {
        std::cout << "Receive burst transaction " << i << " of " << transactions.size() << std::endl;
        // Verify received data
        tlm::tlm_generic_payload& tlm_gp = *((fake_vbuilder_->response_lists)[0]);
        EXPECT_EQ(tlm_gp.get_command(), tlm::TLM_WRITE_COMMAND);
        EXPECT_EQ(tlm_gp.get_data_length(), DATA_SIZE);
        EXPECT_EQ(tlm_gp.get_byte_enable_length(), DATA_SIZE);
        EXPECT_EQ(tlm_gp.get_streaming_width(), 4);
        for (int j = 0; j < tlm_gp.get_data_length(); ++j) {
            EXPECT_EQ(static_cast<uint8_t>(tlm_gp.get_data_ptr()[j]), j % 256);
        }
        }

    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), MULTI_SIZE);
    for (int i = 0; i < xtor_slave_proxy_->response_lists_.size(); ++i) {
        axi_transaction_item& item = xtor_slave_proxy_->response_lists_[i];
        EXPECT_EQ(item.addr, i * 0x10);
        EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, 2);
        EXPECT_EQ(item.size, size_type_t::BYTE_4);
        EXPECT_EQ(item.burst, burst_type_t::FIXED);
        EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
    }
}

TEST_F(AxiSlaveTest, W_WRAP) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    initialize_write_transaction(trans, 256);
    trans.id = 0x123;
    trans.addr = 0x4;
    trans.size = size_type_t::BYTE_4;
    trans.len = 5;  
    trans.burst = umi_axi::burst_type_t::WRAP;
    trans.data_width = umi_axi::bus_width_t::BIT_32;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);
  
    fake_slave_->seq.push_back(&trans);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    std::vector<uint8_t> expected_result = {
        0x14, 0x15, 0x16, 0x17,
        0x00, 0x01, 0x02, 0x03, 
        0x04, 0x05, 0x06, 0x07,
        0x08, 0x09, 0x0A, 0x0B, 
        0x0C, 0x0D, 0x0E, 0x0F,
        0x10, 0x11, 0x12, 0x13,
    };
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(fake_vbuilder_->response_lists.size(), 1);
    tlm::tlm_generic_payload& tlm_gp = *((fake_vbuilder_->response_lists)[0]);
    EXPECT_EQ(tlm_gp.get_command(), tlm::TLM_WRITE_COMMAND);
    EXPECT_EQ(tlm_gp.get_address(), 0x0);
    EXPECT_EQ(tlm_gp.get_data_length(), data_size);
    EXPECT_EQ(tlm_gp.get_byte_enable_length(), data_size);
    EXPECT_EQ(tlm_gp.get_streaming_width(), 0x0);
    for (int i = 0; i < tlm_gp.get_data_length(); ++i) {
        EXPECT_EQ(static_cast<uint8_t>(tlm_gp.get_data_ptr()[i]), expected_result[i]);
    }
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x04);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 5);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::WRAP);
    EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
}

TEST_F(AxiSlaveTest, W_WRAP_NARROW) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    initialize_write_transaction(trans, 256);
    trans.cmd = umi_axi::WRITE;
    trans.id = 0x123;
    trans.addr = 0x04;
    trans.size = size_type_t::BYTE_4;
    trans.len = 5;  
    trans.burst = umi_axi::burst_type_t::WRAP;
    trans.data_width = umi_axi::bus_width_t::BIT_64;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);
  
    fake_slave_->seq.push_back(&trans);
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
  
    std::vector<uint8_t> expected_data = {
        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
        0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F,
        0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
        0x18, 0x19, 0x1A, 0x1B, 0x1C, 0x1D, 0x1E, 0x1F,
        0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27,
        0x28, 0x29, 0x2A, 0x2B, 0x2C, 0x2D, 0x2E, 0x2F,
    };

    std::vector<uint8_t> expected_be = {
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
    };

    std::vector<uint8_t> expected_result = {
        0x28, 0x29, 0x2A, 0x2B,
        0x04, 0x05, 0x06, 0x07,
        0x08, 0x09, 0x0A, 0x0B,
        0x14, 0x15, 0x16, 0x17, 
        0x18, 0x19, 0x1A, 0x1B,
        0x24, 0x25, 0x26, 0x27,
    };

    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(fake_vbuilder_->response_lists.size(), 1);
    tlm::tlm_generic_payload& tlm_gp = *((fake_vbuilder_->response_lists)[0]);
    EXPECT_EQ(tlm_gp.get_command(), tlm::TLM_WRITE_COMMAND);
    EXPECT_EQ(tlm_gp.get_address(), 0x00);
    EXPECT_EQ(tlm_gp.get_data_length(), data_size);
    EXPECT_EQ(tlm_gp.get_byte_enable_length(), data_size);
    EXPECT_EQ(tlm_gp.get_streaming_width(), 0x0);
    for (int i = 0; i < tlm_gp.get_data_length(); ++i) {
        EXPECT_EQ(static_cast<uint8_t>(tlm_gp.get_data_ptr()[i]), expected_result[i]);
    }
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x04);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 5);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::WRAP);
    EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
}

TEST_F(AxiSlaveTest, W_WRAP_MULTI) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    std::vector<axi_transaction_item> transactions;
    const int MULTI_SIZE = 5;
    const int DATA_SIZE = 12;
    for (int i = 0; i < MULTI_SIZE; ++i) {
        axi_transaction_item trans;
        trans.data = std::vector<uint8_t>(DATA_SIZE, 0);
        trans.be = std::vector<uint8_t>(DATA_SIZE, 0xFF);
        trans.cmd = umi_axi::WRITE;
        trans.id = i;
        trans.addr = i * 0x10;
        trans.size = umi_axi::BYTE_4;
        trans.len = 2;
        trans.burst = umi_axi::WRAP;
        trans.data_width = umi_axi::bus_width_t::BIT_32;
        for (int j = 0; j < DATA_SIZE; ++j) {
            trans.data[j] = j % 256;
        }  
        transactions.push_back(trans);
    }
    
    
    for (size_t i = 0; i < transactions.size(); ++i) {
        auto& trans = transactions[i];
        fake_slave_->seq.push_back(&trans);
    }
    std::cout << "Send axi item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "Send axi item notify end" << std::endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(fake_vbuilder_->response_lists.size(), MULTI_SIZE);
    for (size_t i = 0; i < MULTI_SIZE; ++i) {
        std::cout << "Receive burst transaction " << i << " of " << transactions.size() << std::endl;
        // Verify received data
        tlm::tlm_generic_payload& tlm_gp = *((fake_vbuilder_->response_lists)[0]);
        EXPECT_EQ(tlm_gp.get_command(), tlm::TLM_WRITE_COMMAND);
        EXPECT_EQ(tlm_gp.get_data_length(), DATA_SIZE);
        EXPECT_EQ(tlm_gp.get_byte_enable_length(), DATA_SIZE);
        EXPECT_EQ(tlm_gp.get_streaming_width(), 0x0);
        for (int j = 0; j < tlm_gp.get_data_length(); ++j) {
            EXPECT_EQ(static_cast<uint8_t>(tlm_gp.get_data_ptr()[j]), j % 256);
        }
    }

    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), MULTI_SIZE);
    for (int i = 0; i < xtor_slave_proxy_->response_lists_.size(); ++i) {
        axi_transaction_item& item = xtor_slave_proxy_->response_lists_[i];
        EXPECT_EQ(item.addr, i * 0x10);
        EXPECT_EQ(item.id, i);
        EXPECT_EQ(item.len, 2);
        EXPECT_EQ(item.size, size_type_t::BYTE_4);
        EXPECT_EQ(item.burst, burst_type_t::WRAP);
        EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
    }
}


TEST_F(AxiSlaveTest, R_USER) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    trans.cmd = umi_axi::READ;
    trans.id = 0x123;
    trans.addr = 0x0;
    trans.size = size_type_t::BYTE_4;
    trans.len = 5;  
    trans.burst = umi_axi::burst_type_t::INCR;
    trans.data_width = umi_axi::bus_width_t::BIT_32;
    uint32_t data_size = (trans.len + 1) * (1 << trans.size);

    trans.req_user_width = 8;
    trans.data_r_user_width = 16;
    trans.req_user.resize(1);
    trans.req_user[0] = 0xA5;
  
    fake_slave_->seq.push_back(&trans);
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(fake_vbuilder_->response_lists.size(), 1);
    tlm::tlm_generic_payload& tlm_gp = *(fake_vbuilder_->response_lists[0]);
    EXPECT_EQ(tlm_gp.get_command(), tlm::TLM_READ_COMMAND);
    EXPECT_EQ(tlm_gp.get_address(), 0x0);
    EXPECT_EQ(tlm_gp.get_data_length(), data_size);
    EXPECT_EQ(tlm_gp.get_byte_enable_length(), 0x0);
    EXPECT_EQ(tlm_gp.get_streaming_width(), 0x0);
    unsigned char* data_ptr = tlm_gp.get_data_ptr();
    ASSERT_NE(data_ptr, nullptr) << "data_ptr is null";
    auto* xuser_ext = tlm_gp.get_extension<XuserExtension>();
    ASSERT_NE(xuser_ext, nullptr);
    EXPECT_EQ(xuser_ext->req_length, 1);
    EXPECT_EQ(xuser_ext->get_req_ptr()[0], 0xA5);
    ASSERT_NE(xuser_ext->get_data_ptr(), nullptr) << "xuser data_ptr is null";
    for (uint32_t i = 0; i < xuser_ext->data_length; ++i) {
        EXPECT_EQ(static_cast<int>(xuser_ext->get_data_ptr()[i]), 
                    static_cast<uint8_t>(0xB0 + i));
    }
    unsigned int data_len = tlm_gp.get_data_length();
    for (int i = 0; i < data_len; ++i) {
        EXPECT_EQ(static_cast<int>(data_ptr[i]), 
                    static_cast<uint8_t>(i+tlm_gp.get_address()));
    }

    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x0);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 5);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::INCR);
    EXPECT_EQ(item.cmd, cmd_type_t::READ);
    const uint32_t resp_user_bytes = (item.data_r_user_width + 7) / 8;
    const uint32_t total_beats = item.len + 1;
    EXPECT_EQ(item.data_user.size(), resp_user_bytes);
    for (uint32_t i = 0; i < item.data_user.size(); ++i) {
        EXPECT_EQ(item.data_user[i], 
                    static_cast<uint8_t>(0xB0 + i));
    }
}

TEST_F(AxiSlaveTest, W_USER) {
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    axi_transaction_item trans;
    trans.cmd = umi_axi::WRITE;
    trans.id = 0x123;
    trans.addr = 0x0;
    trans.size = size_type_t::BYTE_4;
    trans.len = 5;  
    trans.burst = umi_axi::burst_type_t::INCR;
    trans.data_width = umi_axi::bus_width_t::BIT_32;
    
    trans.req_user_width = 8;     
    trans.data_w_user_width = 16; 
    trans.resp_w_user_width = 8;  

    trans.req_user.resize(1);  
    trans.req_user[0] = 0xA5; 

    const uint32_t total_beats = trans.len + 1;
    const uint32_t wuser_bytes = (trans.data_w_user_width + 7) / 8;
    trans.data_user.resize(wuser_bytes);
    for (uint32_t beat = 0; beat < wuser_bytes; ++beat) {
        trans.data_user[beat] = static_cast<uint8_t>(0xB0 + beat); 
    }

    // Setup data and byte enables
    uint32_t data_size = total_beats * (1 << trans.size);
    trans.data.resize(data_size);
    trans.be.resize(data_size, 0xFF);
    for (uint32_t i = 0; i < data_size; ++i) {
        trans.data[i] = static_cast<uint8_t>(i & 0xFF);
    }
  
    fake_slave_->seq.push_back(&trans);
    std::cout << "send axi_item notify start" << std::endl;
    fake_slave_->send_seq->notify();
    std::cout << "send axi_item notify end" << std::endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(fake_vbuilder_->response_lists.size(), 1);
    tlm::tlm_generic_payload& tlm_gp = *((fake_vbuilder_->response_lists)[0]);
    EXPECT_EQ(tlm_gp.get_command(), tlm::TLM_WRITE_COMMAND);
    EXPECT_EQ(tlm_gp.get_address(), 0x0);
    EXPECT_EQ(tlm_gp.get_data_length(), data_size);
    EXPECT_EQ(tlm_gp.get_byte_enable_length(), data_size);
    EXPECT_EQ(tlm_gp.get_streaming_width(), 0x0);
    unsigned char* data_ptr = tlm_gp.get_data_ptr();
    for (uint32_t i = 0; i < data_size; ++i) {
        EXPECT_EQ(data_ptr[i], static_cast<uint8_t>(i & 0xFF));
    }
    unsigned char* be_ptr = tlm_gp.get_byte_enable_ptr();
    for (uint32_t i = 0; i < data_size; ++i) {
        EXPECT_EQ(be_ptr[i], 0xFF);
    }
    XuserExtension* xuser_ext = nullptr;
    tlm_gp.get_extension(xuser_ext);
    ASSERT_NE(xuser_ext, nullptr);
    EXPECT_EQ(xuser_ext->req_length, 1);
    EXPECT_EQ(xuser_ext->get_req_ptr()[0], 0xA5);
    EXPECT_EQ(xuser_ext->data_length, wuser_bytes);
    for (uint32_t beat = 0; beat < xuser_ext->data_length; ++beat) {
        EXPECT_EQ(xuser_ext->get_data_ptr()[beat], static_cast<uint8_t>(0xB0 + beat));
    }
    EXPECT_EQ(xtor_slave_proxy_->response_lists_.size(), 1);
    axi_transaction_item& item = xtor_slave_proxy_->response_lists_[0];
    EXPECT_EQ(item.addr, 0x0);
    EXPECT_EQ(item.id, 0x123);
    EXPECT_EQ(item.len, 5);
    EXPECT_EQ(item.size, size_type_t::BYTE_4);
    EXPECT_EQ(item.burst, burst_type_t::INCR);
    EXPECT_EQ(item.cmd, cmd_type_t::WRITE);
    EXPECT_EQ(item.resp[0], umi_axi::response_type_t::OKAY);
    EXPECT_EQ(item.resp_user[0], 0x1);
}

} // namespace ssln::hybrid::axi

int sc_main(int argc, char* argv[]) {
    return 0;
}
