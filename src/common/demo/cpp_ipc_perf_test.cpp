#include <iostream>
#include <chrono>
#include <thread>
#include <vector>
#include <algorithm>
#include <libipc/ipc.h>
#include <libipc/shm.h>
#include <cstring>
#include <iomanip>
#include <numeric>
#include <csignal>
#include <atomic>
#include <random>
#include <array>

const char* REQ_CHANNEL = "tlm_benchmark_req";
const char* RESP_CHANNEL = "tlm_benchmark_resp";
const int WARMUP_COUNT = 1000;
const int TEST_COUNT = 10000;

// TLM-like payload structure
struct TlmPayload {
    uint64_t command;           // Command type
    uint64_t address;           // Target address
    uint32_t data_length;       // Length of data
    uint32_t byte_enable_length;// Length of byte_enable
    uint64_t timestamp;         // Timestamp for latency measurement
    char data[0];              // Variable length data followed by byte_enable
};

// Test configuration
struct TestConfig {
    size_t data_size;
    size_t byte_enable_size;
    size_t total_size;  // Including header and data
    size_t max_msg_size;  // Maximum possible message size
};

// Performance statistics
struct PerfStats {
    double min_latency_us;
    double max_latency_us;
    double avg_latency_us;
    double p50_latency_us;
    double p90_latency_us;
    double p99_latency_us;
    double throughput_mbps;
    size_t message_size;
    size_t sample_count;
};

// Global flag for graceful shutdown
std::atomic<bool> g_running{true};

// Add exit command definition
const uint64_t EXIT_COMMAND = 0xFFFFFFFFFFFFFFFF;  // Special command for exit

// Available data sizes for random selection
const std::array<size_t, 7> DATA_SIZES = {64, 128, 256, 512, 1024, 2048, 4096};

void signal_handler(int) {
    g_running = false;
}

void print_stats(const PerfStats& stats) {
    std::cout << "\nPerformance Test Results:\n";
    std::cout << std::string(80, '-') << '\n';
    std::cout << std::fixed << std::setprecision(2);
    std::cout << "Message Size: " << stats.message_size << " bytes\n";
    std::cout << "Sample Count: " << stats.sample_count << "\n\n";
    
    std::cout << "Latency (microseconds):\n";
    std::cout << "  Min:     " << stats.min_latency_us << "\n";
    std::cout << "  P50:     " << stats.p50_latency_us << "\n";
    std::cout << "  P90:     " << stats.p90_latency_us << "\n";
    std::cout << "  P99:     " << stats.p99_latency_us << "\n";
    std::cout << "  Max:     " << stats.max_latency_us << "\n";
    std::cout << "  Average: " << stats.avg_latency_us << "\n\n";
    
    std::cout << "Throughput: " << stats.throughput_mbps << " MB/s\n";
    std::cout << std::string(80, '-') << '\n';
}

void run_producer(const TestConfig& config) {
    signal(SIGINT, signal_handler);

    ipc::shm::remove(REQ_CHANNEL);
    ipc::shm::remove(RESP_CHANNEL);
    
    // Create channels for sending requests and receiving responses
    ipc::channel req_channel{REQ_CHANNEL, ipc::sender};
    ipc::channel resp_channel{RESP_CHANNEL, ipc::receiver};
    
    std::cout << "Waiting for consumer...\n";
    req_channel.wait_for_recv(1);
    resp_channel.wait_for_recv(1);
    
    // Prepare test data - use max_msg_size for buffer allocation
    std::vector<char> send_buffer(config.max_msg_size);
    auto* payload = reinterpret_cast<TlmPayload*>(send_buffer.data());
    payload->command = 1;
    payload->address = 0x1000;
    payload->data_length = config.data_size;
    payload->byte_enable_length = config.byte_enable_size;
    
    // Initialize random number generators
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<unsigned char> data_dist(0, 255);  // For random data
    std::uniform_int_distribution<size_t> size_dist(0, DATA_SIZES.size() - 1);  // For random size

    // Warmup phase
    std::cout << "Starting warmup...\n";
    for (int i = 0; i < WARMUP_COUNT && g_running; i++) {
        if (!req_channel.send(send_buffer.data(), config.total_size)) {
            std::cerr << "Warmup send failed\n";
            return;
        }
        
        auto recv_data = resp_channel.recv();
        if (recv_data.empty() || recv_data.size() != sizeof(int)) {
            std::cerr << "Warmup receive failed\n";
            return;
        }
        
        if ((i + 1) % 100 == 0) {
            std::cout << "Warmup progress: " << (i + 1) << "/" << WARMUP_COUNT << "\r" << std::flush;
        }
    }
    std::cout << "\nWarmup completed\n";

    // Performance test phase
    std::vector<double> latencies;
    latencies.reserve(TEST_COUNT);

    std::cout << "Starting performance test...\n";
    for (int i = 0; i < TEST_COUNT && g_running; i++) {
        // Select random data size
        size_t current_data_size = DATA_SIZES[size_dist(gen)];
        
        // Update payload size information
        payload->data_length = current_data_size;
        payload->byte_enable_length = current_data_size;

        // Fill with random data
        char* data = payload->data;
        for (size_t j = 0; j < current_data_size; ++j) {
            data[j] = static_cast<char>(data_dist(gen));
        }
        for (size_t j = 0; j < current_data_size; ++j) {
            data[current_data_size + j] = 0xff;
            //static_cast<char>(data_dist(gen));
        }

        // Get current timestamp
        payload->timestamp = std::chrono::duration_cast<std::chrono::nanoseconds>(
            std::chrono::high_resolution_clock::now().time_since_epoch()).count();

        // Send and measure latency
        auto start = std::chrono::high_resolution_clock::now();
        if (!req_channel.send(send_buffer.data(), sizeof(TlmPayload) + 2 * current_data_size)) {
            std::cerr << "Send failed at iteration " << i << "\n";
            return;
        }
        
        auto recv_data = resp_channel.recv();
        if (recv_data.empty() || recv_data.size() != sizeof(int)) {
            std::cerr << "Receive failed at iteration " << i << "\n";
            return;
        }
        auto end = std::chrono::high_resolution_clock::now();

        double latency = std::chrono::duration<double, std::micro>(end - start).count();
        latencies.push_back(latency);

        if ((i + 1) % 1000 == 0) {
            std::cout << "Progress: " << (i + 1) << "/" << TEST_COUNT << "\r" << std::flush;
        }
    }
    std::cout << "\nTest completed\n";

    // Calculate statistics
    PerfStats stats{};
    stats.message_size = config.total_size;
    stats.sample_count = latencies.size();
    
    if (!latencies.empty()) {
        std::sort(latencies.begin(), latencies.end());
        stats.min_latency_us = latencies.front();
        stats.max_latency_us = latencies.back();
        stats.p50_latency_us = latencies[latencies.size() * 50 / 100];
        stats.p90_latency_us = latencies[latencies.size() * 90 / 100];
        stats.p99_latency_us = latencies[latencies.size() * 99 / 100];
        stats.avg_latency_us = std::accumulate(latencies.begin(), latencies.end(), 0.0) / latencies.size();
        stats.throughput_mbps = (config.total_size * latencies.size()) / (stats.avg_latency_us);
    }
    
    print_stats(stats);
    
    // Send exit command to consumer
    std::cout << "Sending exit command to consumer...\n";
    payload->command = EXIT_COMMAND;
    if (!req_channel.send(send_buffer.data(), config.total_size)) {
        std::cerr << "Failed to send exit command\n";
    }
    
    // Clean up - disconnect will clean up the channels
    req_channel.disconnect();
    resp_channel.disconnect();
}

void run_consumer(const TestConfig& config) {
    signal(SIGINT, signal_handler);
    
    // Create channels for receiving requests and sending responses
    ipc::channel req_channel{REQ_CHANNEL, ipc::receiver};
    ipc::channel resp_channel{RESP_CHANNEL, ipc::sender};
    
    std::cout << "Consumer ready. Waiting for producer...\n";
    
    // Wait for producer to connect
    req_channel.wait_for_recv(1);
    resp_channel.wait_for_recv(1);
    
    int response = 1;  // Simple integer response
    size_t messages_received = 0;
    
    std::cout << "Connected to producer. Processing messages...\n";
    
    // Process messages
    while (g_running) {
        // Receive request
        auto recv_data = req_channel.recv();
        
        if (recv_data.empty() || recv_data.size() < sizeof(TlmPayload)) {
            if (g_running) {  // Only report error if not shutting down
                std::cerr << "Receive failed, size=" << recv_data.size() << "\n";
            }
            break;
        }
        
        // Get payload and check for exit command
        auto* payload = reinterpret_cast<const TlmPayload*>(recv_data.data());
        if (payload->command == EXIT_COMMAND) {
            std::cout << "\nReceived exit command\n";
            break;
        }
        
        // Send response immediately - just an integer
        if (!resp_channel.send(&response, sizeof(response))) {
            if (g_running) {  // Only report error if not shutting down
                std::cerr << "Send failed\n";
            }
            break;
        }
        
        messages_received++;
        if (messages_received % 1000 == 0) {
            std::cout << "Processed " << messages_received << " messages\r" << std::flush;
        }
    }
    
    std::cout << "\nConsumer processed " << messages_received << " messages\n";
    
    // Clean up - disconnect will clean up the channels
    req_channel.disconnect();
    resp_channel.disconnect();
}

void print_usage(const char* program) {
    std::cerr << "Usage: " << program << " [producer|consumer]\n"
              << "Example:\n"
              << "  Terminal 1: " << program << " consumer\n"
              << "  Terminal 2: " << program << " producer\n";
}

int main(int argc, char* argv[]) {
    if (argc != 2) {
        print_usage(argv[0]);
        return 1;
    }

    std::string mode(argv[1]);
    
    TestConfig config{
        4096,                    // data_size
        4096,                    // byte_enable_size
        sizeof(TlmPayload) + 2 * 4096,  // total_size (header + data + byte_enable)
        sizeof(TlmPayload) + 2 * 4096  // max_msg_size (使用最大可能的数据大小)
    };
    
    std::cout << "Configuration:\n"
              << "  Mode: " << mode << "\n"
              << "  Data size: " << config.data_size << " bytes\n"
              << "  Byte enable size: " << config.byte_enable_size << " bytes\n"
              << "  Total message size: " << config.total_size << " bytes\n\n";
    
    try {
        if (mode == "producer") {
            run_producer(config);
        }
        else if (mode == "consumer") {
            run_consumer(config);
        }
        else {
            print_usage(argv[0]);
            return 1;
        }
    }
    catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
} 