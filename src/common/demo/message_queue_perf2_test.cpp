#include <boost/interprocess/ipc/message_queue.hpp>
#include <iostream>
#include <thread>
#include <chrono>
#include <vector>
#include <algorithm>
#include <iomanip>
#include <numeric>
#include <csignal>
#include <atomic>
#include <random>
#include <array>

using namespace boost::interprocess;

const char* REQ_QUEUE = "tlm_perf_req";
const char* RESP_QUEUE = "tlm_perf_resp";
const int WARMUP_COUNT = 1000;
const int TEST_COUNT = 10000;
const int MAX_MESSAGES = 128;  // Queue capacity

// Available data sizes for random selection
const std::array<size_t, 8> DATA_SIZES = {32, 64, 128, 256, 512, 1024, 2048, 4096};

// TLM-like payload structure with fixed size
struct TlmPayload {
    uint64_t command;           // Command type
    uint64_t address;           // Target address
    uint32_t data_length;       // Length of data
    uint32_t byte_enable_length;// Length of byte_enable
    uint64_t timestamp;         // Timestamp for latency measurement
    char data[0];              // Variable length data followed by byte_enable
};

// Performance statistics
struct PerfStats {
    double min_latency_us;
    double max_latency_us;
    double avg_latency_us;
    double p50_latency_us;
    double p90_latency_us;
    double p99_latency_us;
    double throughput_mbps;
    size_t message_size;
    size_t sample_count;
};

// Global flag for graceful shutdown
std::atomic<bool> g_running{true};

void signal_handler(int) {
    g_running = false;
}

void print_stats(const PerfStats& stats) {
    std::cout << "\nPerformance Test Results:\n";
    std::cout << std::string(80, '-') << '\n';
    std::cout << std::fixed << std::setprecision(2);
    std::cout << "Message Size: " << stats.message_size << " bytes\n";
    std::cout << "Sample Count: " << stats.sample_count << "\n\n";
    
    std::cout << "Latency (microseconds):\n";
    std::cout << "  Min:     " << stats.min_latency_us << "\n";
    std::cout << "  P50:     " << stats.p50_latency_us << "\n";
    std::cout << "  P90:     " << stats.p90_latency_us << "\n";
    std::cout << "  P99:     " << stats.p99_latency_us << "\n";
    std::cout << "  Max:     " << stats.max_latency_us << "\n";
    std::cout << "  Average: " << stats.avg_latency_us << "\n\n";
    
    std::cout << "Throughput: " << stats.throughput_mbps << " MB/s\n";
    std::cout << std::string(80, '-') << '\n';
}

void cleanup_queues() {
    message_queue::remove(REQ_QUEUE);
    message_queue::remove(RESP_QUEUE);
}

void run_consumer(size_t msg_size) {
    signal(SIGINT, signal_handler);
    cleanup_queues();
    
    try {
        // Create message queues
        message_queue req_queue(create_only, REQ_QUEUE, MAX_MESSAGES, msg_size);
        message_queue resp_queue(create_only, RESP_QUEUE, MAX_MESSAGES, sizeof(uint64_t));
        
        std::cout << "Consumer ready. Processing messages...\n";
        
        // Pre-allocate buffer
        std::vector<char> recv_buffer(msg_size);
        uint64_t response;
        size_t messages_received = 0;
        
        while (g_running) {
            size_t recvd_size;
            unsigned int priority;
            
            // Receive request
            req_queue.receive(recv_buffer.data(), msg_size, recvd_size, priority);
            
            // Get timestamp and check for exit command
            auto* payload = reinterpret_cast<const TlmPayload*>(recv_buffer.data());
            if (payload->command == 0xFFFFFFFFFFFFFFFF) {
                std::cout << "\nReceived exit command\n";
                break;
            }
            
            // Send response immediately
            response = payload->timestamp;
            resp_queue.send(&response, sizeof(response), 0);
            
            messages_received++;
            if (messages_received % 1000 == 0) {
                std::cout << "Processed " << messages_received << " messages\r" << std::flush;
            }
        }
        
        std::cout << "\nConsumer processed " << messages_received << " messages\n";
    }
    catch (interprocess_exception& ex) {
        std::cerr << "Consumer error: " << ex.what() << std::endl;
    }
    
    cleanup_queues();
}

void run_producer(size_t data_size) {
    signal(SIGINT, signal_handler);
    
    try {
        size_t msg_size = sizeof(TlmPayload) + 2 * data_size;  // data + byte_enable
        
        // Wait for consumer to create queues
        std::cout << "Waiting for consumer...\n";
        message_queue* req_queue = nullptr;
        message_queue* resp_queue = nullptr;
        
        while (g_running && (!req_queue || !resp_queue)) {
            try {
                if (!req_queue) req_queue = new message_queue(open_only, REQ_QUEUE);
                if (!resp_queue) resp_queue = new message_queue(open_only, RESP_QUEUE);
            }
            catch (interprocess_exception&) {
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
                std::cout << "." << std::flush;
            }
        }
        
        if (!g_running) {
            delete req_queue;
            delete resp_queue;
            return;
        }
        
        std::cout << "\nConnected to consumer\n";
        
        // Prepare test data
        std::vector<char> send_buffer(msg_size);
        auto* payload = reinterpret_cast<TlmPayload*>(send_buffer.data());
        payload->command = 1;
        payload->address = 0x1000;
        payload->data_length = data_size;
        payload->byte_enable_length = data_size;
        
        // Initialize random number generators
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<unsigned char> data_dist(0, 255);  // For random data
        std::uniform_int_distribution<size_t> size_dist(0, DATA_SIZES.size() - 1);  // For random size
        
        uint64_t response;
        
        // Warmup phase
        std::cout << "Starting warmup...\n";
        for (int i = 0; i < WARMUP_COUNT && g_running; i++) {
            // Select random data size
            size_t current_data_size = DATA_SIZES[size_dist(gen)];
            
            // Update payload size information
            payload->data_length = current_data_size;
            payload->byte_enable_length = current_data_size;

            // Fill with random data and fixed byte_enable
            char* data = payload->data;
            for (size_t j = 0; j < current_data_size; ++j) {
                data[j] = static_cast<char>(data_dist(gen));
            }
            for (size_t j = 0; j < current_data_size; ++j) {
                data[current_data_size + j] = 0xff;  // Fixed byte_enable value
            }
            
            req_queue->send(send_buffer.data(), sizeof(TlmPayload) + 2 * current_data_size, 0);
            
            size_t recvd_size;
            unsigned int priority;
            resp_queue->receive(&response, sizeof(response), recvd_size, priority);
            
            if ((i + 1) % 100 == 0) {
                std::cout << "Warmup progress: " << (i + 1) << "/" << WARMUP_COUNT << "\r" << std::flush;
            }
        }
        
        if (!g_running) {
            delete req_queue;
            delete resp_queue;
            return;
        }
        
        std::cout << "\nWarmup completed\n";
        
        // Performance test phase
        std::vector<double> latencies;
        latencies.reserve(TEST_COUNT);
        
        std::cout << "Starting performance test...\n";
        for (int i = 0; i < TEST_COUNT && g_running; i++) {
            // Select random data size
            size_t current_data_size = DATA_SIZES[size_dist(gen)];
            
            // Update payload size information
            payload->data_length = current_data_size;
            payload->byte_enable_length = current_data_size;

            // Fill with random data and fixed byte_enable
            char* data = payload->data;
            for (size_t j = 0; j < current_data_size; ++j) {
                data[j] = static_cast<char>(data_dist(gen));
            }
            for (size_t j = 0; j < current_data_size; ++j) {
                data[current_data_size + j] = 0xff;  // Fixed byte_enable value
            }
            
            // Get timestamp before timing measurement
            payload->timestamp = std::chrono::duration_cast<std::chrono::nanoseconds>(
                std::chrono::high_resolution_clock::now().time_since_epoch()).count();
            
            auto start = std::chrono::high_resolution_clock::now();
            req_queue->send(send_buffer.data(), sizeof(TlmPayload) + 2 * current_data_size, 0);
            
            size_t recvd_size;
            unsigned int priority;
            resp_queue->receive(&response, sizeof(response), recvd_size, priority);
            auto end = std::chrono::high_resolution_clock::now();

            double latency = std::chrono::duration<double, std::micro>(end - start).count();
            latencies.push_back(latency);
            
            if ((i + 1) % 1000 == 0) {
                std::cout << "Progress: " << (i + 1) << "/" << TEST_COUNT << "\r" << std::flush;
            }
        }
        
        if (!g_running) {
            delete req_queue;
            delete resp_queue;
            return;
        }
        
        std::cout << "\nTest completed\n";
        
        // Calculate statistics
        PerfStats stats{};
        stats.message_size = msg_size;
        stats.sample_count = latencies.size();
        
        if (!latencies.empty()) {
            std::sort(latencies.begin(), latencies.end());
            stats.min_latency_us = latencies.front();
            stats.max_latency_us = latencies.back();
            stats.p50_latency_us = latencies[latencies.size() * 50 / 100];
            stats.p90_latency_us = latencies[latencies.size() * 90 / 100];
            stats.p99_latency_us = latencies[latencies.size() * 99 / 100];
            stats.avg_latency_us = std::accumulate(latencies.begin(), latencies.end(), 0.0) / latencies.size();
            stats.throughput_mbps = (msg_size * latencies.size()) / (stats.avg_latency_us);
        }
        
        print_stats(stats);
        
        // Send exit command
        std::cout << "Sending exit command to consumer...\n";
        payload->command = 0xFFFFFFFFFFFFFFFF;
        req_queue->send(send_buffer.data(), msg_size, 0);
        
        delete req_queue;
        delete resp_queue;
    }
    catch (interprocess_exception& ex) {
        std::cerr << "Producer error: " << ex.what() << std::endl;
        cleanup_queues();
    }
}

void print_usage(const char* program) {
    std::cerr << "Usage: " << program << " [producer|consumer]\n"
              << "Example:\n"
              << "  Terminal 1: " << program << " consumer\n"
              << "  Terminal 2: " << program << " producer\n";
}


int main(int argc, char* argv[]) {
    if (argc != 2) {
        print_usage(argv[0]);
        return 1;
    }
    
    std::string mode(argv[1]);
    
    try {
        if (mode == "producer") {
            run_producer(4096);  // Use max size for buffer allocation
        }
        else if (mode == "consumer") {
            size_t max_msg_size = sizeof(TlmPayload) + 2 * 4096;  // Use max possible message size
            run_consumer(max_msg_size);
        }
        else {
            print_usage(argv[0]);
            return 1;
        }
    }
    catch (std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
} 
