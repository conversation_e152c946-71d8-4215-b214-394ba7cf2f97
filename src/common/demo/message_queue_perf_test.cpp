#include <boost/interprocess/ipc/message_queue.hpp>
#include <chrono>
#include <iostream>
#include <vector>
#include <thread>
#include <atomic>
#include <numeric>
#include <iomanip>
#include <csignal>
#include <random>

using namespace boost::interprocess;

// TLM-like payload structure
struct TlmPayload {
    uint64_t command;           // Command type
    uint64_t address;           // Target address
    uint32_t data_length;       // Length of data
    uint32_t byte_enable_length;// Length of byte_enable
    uint64_t timestamp;         // Timestamp for latency measurement
    char data[0];              // Variable length data followed by byte_enable
};

// Test scenarios
enum class TestMode {
    EXACT_SIZE,    // Message queue size matches the payload size
    FIXED_SIZE     // Fixed message queue size (1024 bytes)
};

// Single test case configuration
struct TestConfig {
    size_t data_size;
    size_t byte_enable_size;
    int iterations;
    size_t queue_size;
    TestMode mode;
    size_t max_msg_size;  // Only used in FIXED_SIZE mode
};

// Performance statistics
struct PerfStats {
    double min_latency_us;
    double max_latency_us;
    double avg_latency_us;
    double median_latency_us;  // Add median (50th percentile)
    double throughput_mbps;
    size_t message_size;
};

// Global flag for graceful shutdown
std::atomic<bool> g_running{true};

void signal_handler(int) {
    g_running = false;
}

class Producer {
public:
    explicit Producer(const std::string& name) 
        : queue_name_(name)
        , response_queue_name_(name + "_response") {
        cleanup();
    }

    ~Producer() {
        cleanup();
    }

    void cleanup() {
        message_queue::remove(queue_name_.c_str());
        message_queue::remove(response_queue_name_.c_str());
        // Give time for the consumer to notice the queues are gone
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
    }

    PerfStats run_test(const TestConfig& config) {
        cleanup();  // Ensure clean state before each test
        
        size_t msg_size = sizeof(TlmPayload) + config.data_size + config.byte_enable_size;
        if (config.mode == TestMode::FIXED_SIZE && msg_size > config.max_msg_size) {
            throw std::runtime_error("Message size exceeds maximum allowed size");
        }
        
        size_t queue_msg_size = (config.mode == TestMode::FIXED_SIZE) ? 
                               config.max_msg_size : msg_size;
        
        // Variables for message handling
        std::vector<char> send_buffer(queue_msg_size);
        uint64_t response;
        message_queue::size_type recvd_size;
        unsigned int priority;
        
        // Create message queues
        message_queue mq(
            create_only,
            queue_name_.c_str(),
            config.queue_size,
            queue_msg_size);

        message_queue response_mq(
            create_only,
            response_queue_name_.c_str(),
            config.queue_size,
            sizeof(uint64_t));

        // Wait for consumer to connect (maximum 5 seconds)
        std::cout << "Waiting for consumer to connect..." << std::flush;
        bool consumer_ready = false;
        auto start_wait = std::chrono::steady_clock::now();

        while (!consumer_ready && g_running) {
            try {
                // Send test message
                mq.send(send_buffer.data(), msg_size, 0);
                // Wait for response
                if (response_mq.try_receive(&response, sizeof(response), recvd_size, priority)) {
                    consumer_ready = true;
                    std::cout << "connected\n" << std::flush;
                    break;
                }
            }
            catch (...) {
                // Ignore errors during test send
            }

            auto now = std::chrono::steady_clock::now();
            if (std::chrono::duration_cast<std::chrono::seconds>(now - start_wait).count() > 5) {
                throw std::runtime_error("Timeout waiting for consumer");
            }
            
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            std::cout << "." << std::flush;
        }

        if (!consumer_ready) {
            throw std::runtime_error("Consumer not connected");
        }

        // Initialize payload
        auto* payload = reinterpret_cast<TlmPayload*>(send_buffer.data());
        payload->command = 1;
        payload->address = 0x1000;
        payload->data_length = config.data_size;
        payload->byte_enable_length = config.byte_enable_size;

        // Initialize random number generator
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<unsigned char> dist(0, 255);

        // Test phase
        std::vector<double> latencies;
        latencies.reserve(config.iterations);

        for (int i = 0; i < config.iterations && g_running; ++i) {
            // Fill with random data
            char* data = payload->data;
            for (size_t j = 0; j < config.data_size; ++j) {
                data[j] = static_cast<char>(dist(gen));
            }
            for (size_t j = 0; j < config.byte_enable_size; ++j) {
                data[config.data_size + j] = static_cast<char>(dist(gen));
            }
            
            // Only measure send and receive time
            auto start = std::chrono::high_resolution_clock::now();
            mq.send(send_buffer.data(), msg_size, 0);
            response_mq.receive(&response, sizeof(response), recvd_size, priority);
            auto end = std::chrono::high_resolution_clock::now();
            
            double latency = std::chrono::duration<double, std::micro>(end - start).count();
            latencies.push_back(latency);
            
            if (i % 1000 == 0) {
                std::cout << "." << std::flush;
            }
        }
        std::cout << "\n";

        // Calculate statistics
        PerfStats stats{};
        stats.message_size = msg_size;
        if (!latencies.empty()) {
            // Sort latencies to calculate median
            std::vector<double> sorted_latencies = latencies;  // Make a copy
            std::sort(sorted_latencies.begin(), sorted_latencies.end());
            
            stats.min_latency_us = sorted_latencies.front();
            stats.max_latency_us = sorted_latencies.back();
            stats.median_latency_us = sorted_latencies[sorted_latencies.size() / 2];
            stats.avg_latency_us = std::accumulate(latencies.begin(), latencies.end(), 0.0) / latencies.size();
            stats.throughput_mbps = (msg_size * latencies.size()) / (stats.avg_latency_us);
        }

        return stats;
    }

private:
    std::string queue_name_;
    std::string response_queue_name_;
};

class Consumer {
public:
    explicit Consumer(const std::string& name) 
        : queue_name_(name)
        , response_queue_name_(name + "_response") {}

    void run(size_t max_msg_size) {
        std::cout << "Waiting for producer to create queues..." << std::flush;
        
        message_queue* mq = nullptr;
        message_queue* response_mq = nullptr;
        
        // Wait for queues to be created (maximum 30 seconds)
        auto start_wait = std::chrono::steady_clock::now();
        while (g_running) {
            try {
                if (!mq) {
                    mq = new message_queue(open_only, queue_name_.c_str());
                    std::cout << "\nMain queue connected..." << std::flush;
                }
                if (!response_mq) {
                    response_mq = new message_queue(open_only, response_queue_name_.c_str());
                    std::cout << "\nResponse queue connected..." << std::flush;
                }
                if (mq && response_mq) {
                    std::cout << "\nBoth queues connected. Starting consumer...\n" << std::flush;
                    break;
                }
            }
            catch (const interprocess_exception&) {
                auto now = std::chrono::steady_clock::now();
                if (std::chrono::duration_cast<std::chrono::seconds>(now - start_wait).count() > 30) {
                    std::cerr << "\nTimeout waiting for producer\n";
                    delete mq;
                    delete response_mq;
                    return;
                }
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
                std::cout << "." << std::flush;
            }
        }

        if (!g_running) {
            std::cout << "\nReceived shutdown signal while waiting\n";
            delete mq;
            delete response_mq;
            return;
        }

        try {
            std::vector<char> recv_buffer(max_msg_size);
            message_queue::size_type recvd_size;
            unsigned int priority;

            std::cout << "Consumer ready. Press Ctrl+C to stop.\n" << std::flush;

            while (g_running) {
                if (mq->try_receive(recv_buffer.data(), max_msg_size, recvd_size, priority)) {
                    // Immediately send response without any processing
                    auto* payload = reinterpret_cast<TlmPayload*>(recv_buffer.data());
                    response_mq->send(&payload->timestamp, sizeof(payload->timestamp), 0);
                    
                    messages_received_++;
                    if (messages_received_ % 10000 == 0) {
                        std::cout << "Received " << messages_received_ << " messages\r" << std::flush;
                    }
                } else {
                    std::this_thread::sleep_for(std::chrono::microseconds(100));
                }
            }

            std::cout << "\nConsumer received " << messages_received_ << " messages\n";
        }
        catch (const interprocess_exception& ex) {
            std::cerr << "Consumer error: " << ex.what() << std::endl;
        }

        delete mq;
        delete response_mq;
    }

private:
    std::string queue_name_;
    std::string response_queue_name_;
    size_t messages_received_{0};
};

void print_header(TestMode mode) {
    std::cout << "\nPerformance Test Results - "
              << (mode == TestMode::EXACT_SIZE ? "Exact Size" : "Fixed Size (1024B)")
              << " Mode:\n";
    std::cout << std::string(100, '-') << '\n';
    std::cout << std::setw(10) << "Data Size" 
              << std::setw(10) << "BE Size"
              << std::setw(12) << "Msg Size"
              << std::setw(12) << "Min Lat"
              << std::setw(12) << "Med Lat"  // Add median latency column
              << std::setw(12) << "Avg Lat"
              << std::setw(12) << "Max Lat"
              << std::setw(12) << "MB/s"
              << '\n';
    std::cout << std::string(100, '-') << '\n';
}

void print_result(size_t data_size, size_t be_size, const PerfStats& stats) {
    std::cout << std::fixed << std::setprecision(2)
              << std::setw(10) << data_size
              << std::setw(10) << be_size
              << std::setw(12) << stats.message_size
              << std::setw(12) << stats.min_latency_us
              << std::setw(12) << stats.median_latency_us  // Add median latency
              << std::setw(12) << stats.avg_latency_us
              << std::setw(12) << stats.max_latency_us
              << std::setw(12) << stats.throughput_mbps
              << '\n';
}

void run_consumer(size_t max_msg_size) {
    Consumer consumer("tlm_perf_test");
    consumer.run(max_msg_size);
}

int main(int argc, char* argv[]) {
    if (argc != 3) {
        std::cerr << "Usage: " << argv[0] << " [producer|consumer] <data_size>\n";
        std::cerr << "Example: " << argv[0] << " producer 64\n";
        return 1;
    }

    std::string mode(argv[1]);
    size_t data_size = std::stoul(argv[2]);
    signal(SIGINT, signal_handler);

    try {
        const int iterations = 10000;
        const size_t queue_size = 128;  // Match with perf2 test
        size_t msg_size = sizeof(TlmPayload) + 2 * data_size;  // data + byte_enable

        if (mode == "producer") {
            Producer producer("tlm_perf_test");
            print_header(TestMode::EXACT_SIZE);
            
            TestConfig config{
                data_size,      // data_size
                data_size,      // byte_enable_size
                iterations,
                queue_size,
                TestMode::EXACT_SIZE,
                msg_size
            };

            auto stats = producer.run_test(config);
            print_result(data_size, data_size, stats);
        }
        else if (mode == "consumer") {
            run_consumer(msg_size);
        }
        else {
            std::cerr << "Invalid mode. Use 'producer' or 'consumer'\n";
            return 1;
        }
    }
    catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
} 