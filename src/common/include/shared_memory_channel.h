#ifndef SHARED_MEMORY_CHANNEL_H_
#define SHARED_MEMORY_CHANNEL_H_

#include <boost/interprocess/managed_shared_memory.hpp>
#include <boost/interprocess/sync/interprocess_semaphore.hpp>
#include <boost/container/vector.hpp>
#include "readerwriterqueue.h"

namespace ssln {
namespace hybrid {

// Shared memory role configuration
enum class ShmRole {
    CREATOR,    // Create shared memory
    OPENER      // Open existing shared memory
};

// Shared memory configuration
struct ShmConfiguration {
    std::string shm_name;        // Shared memory name
    size_t shm_size;            // Shared memory size
    size_t queue_size;          // Queue size
    ShmRole role;               // Role (creator/opener)
    
    ShmConfiguration(
        const std::string& name = "shared_memory",
        size_t mem_size = 65536,
        size_t q_size = 1024,
        ShmRole r = ShmRole::CREATOR)
        : shm_name(name)
        , shm_size(mem_size)
        , queue_size(q_size)
        , role(r)
    {}
};

// Memory block header
struct BlockHeader {
    size_t size;          // Total size including header
    bool in_use;          // Whether this block is in use
    BlockHeader* next;    // Next block in the free list
};

// Memory pool for variable size allocations in shared memory
class MemoryPool {
public:
    explicit MemoryPool(void* base, size_t size)
        : base_(static_cast<char*>(base))
        , size_(size)
        , free_list_(nullptr) {
        // Initialize with a single free block
        BlockHeader* initial = reinterpret_cast<BlockHeader*>(base_);
        initial->size = size;
        initial->in_use = false;
        initial->next = nullptr;
        free_list_ = initial;
    }

    void* allocate(size_t size) {
        size_t total_size = size + sizeof(BlockHeader);
        BlockHeader* current = free_list_;
        BlockHeader* prev = nullptr;

        // Find a suitable free block
        while (current) {
            if (!current->in_use && current->size >= total_size) {
                // Split block if it's too large
                if (current->size > total_size + sizeof(BlockHeader) + 64) {
                    BlockHeader* new_block = reinterpret_cast<BlockHeader*>(
                        reinterpret_cast<char*>(current) + total_size);
                    new_block->size = current->size - total_size;
                    new_block->in_use = false;
                    new_block->next = current->next;
                    current->size = total_size;
                    current->next = new_block;
                }
                current->in_use = true;
                if (prev) {
                    prev->next = current->next;
                } else {
                    free_list_ = current->next;
                }
                return reinterpret_cast<char*>(current) + sizeof(BlockHeader);
            }
            prev = current;
            current = current->next;
        }
        return nullptr;  // No suitable block found
    }

    void deallocate(void* ptr) {
        if (!ptr) return;
        
        BlockHeader* header = reinterpret_cast<BlockHeader*>(
            static_cast<char*>(ptr) - sizeof(BlockHeader));
        header->in_use = false;

        // Add to free list and try to merge adjacent blocks
        BlockHeader* current = free_list_;
        BlockHeader* prev = nullptr;

        while (current && current < header) {
            prev = current;
            current = current->next;
        }

        if (prev) {
            // Try to merge with previous block
            if (reinterpret_cast<char*>(prev) + prev->size == 
                reinterpret_cast<char*>(header)) {
                prev->size += header->size;
                header = prev;
            } else {
                prev->next = header;
            }
        } else {
            free_list_ = header;
        }

        header->next = current;
        // Try to merge with next block
        if (current && reinterpret_cast<char*>(header) + header->size == 
            reinterpret_cast<char*>(current)) {
            header->size += current->size;
            header->next = current->next;
        }
    }

private:
    char* base_;
    size_t size_;
    BlockHeader* free_list_;
};

// Shared memory channel for producer-consumer communication
template<typename MetaData>
class SharedMemoryChannel {
public:
    struct Payload {
        MetaData metadata;
        size_t data_offset;     // Offset from pool base to data
        size_t data_size;
        size_t aux_offset;      // Offset from pool base to auxiliary data
        size_t aux_size;
    };

    explicit SharedMemoryChannel(const ShmConfiguration& config)
        : segment_(boost::interprocess::open_or_create, 
                  config.shm_name.c_str(), 
                  config.shm_size)
        , pool_(segment_.get_address(), config.shm_size)
        , queue_(config.queue_size)
        , producer_sem_(0)
        , consumer_sem_(config.queue_size) {
    }

    // Producer interface - Zero copy version
    template<typename DataCallback>
    bool send(const MetaData& metadata, 
             size_t data_size,
             size_t aux_size,
             const DataCallback& callback) {
        if (!consumer_sem_.try_wait()) {
            return false;
        }

        // Allocate memory for data
        void* data_ptr = nullptr;
        void* aux_ptr = nullptr;
        
        if (data_size > 0) {
            data_ptr = pool_.allocate(data_size);
            if (!data_ptr) {
                consumer_sem_.post();
                return false;
            }
        }
        
        if (aux_size > 0) {
            aux_ptr = pool_.allocate(aux_size);
            if (!aux_ptr) {
                if (data_ptr) pool_.deallocate(data_ptr);
                consumer_sem_.post();
                return false;
            }
        }

        // Let caller fill the allocated memory
        if (!callback(data_ptr, aux_ptr)) {
            if (data_ptr) pool_.deallocate(data_ptr);
            if (aux_ptr) pool_.deallocate(aux_ptr);
            consumer_sem_.post();
            return false;
        }

        // Create and enqueue payload
        Payload payload;
        payload.metadata = metadata;
        payload.data_offset = data_ptr ? 
            static_cast<char*>(data_ptr) - static_cast<char*>(segment_.get_address()) : 0;
        payload.data_size = data_size;
        payload.aux_offset = aux_ptr ? 
            static_cast<char*>(aux_ptr) - static_cast<char*>(segment_.get_address()) : 0;
        payload.aux_size = aux_size;

        if (!queue_.try_enqueue(payload)) {
            if (data_ptr) pool_.deallocate(data_ptr);
            if (aux_ptr) pool_.deallocate(aux_ptr);
            consumer_sem_.post();
            return false;
        }

        producer_sem_.post();
        return true;
    }

    // Consumer interface - Zero copy version
    template<typename DataCallback>
    bool receive(MetaData& metadata, const DataCallback& callback) {
        if (!producer_sem_.try_wait()) {
            return false;
        }

        Payload payload;
        if (!queue_.try_dequeue(payload)) {
            producer_sem_.post();
            return false;
        }

        metadata = payload.metadata;
        
        void* data_ptr = payload.data_size > 0 ? 
            static_cast<char*>(segment_.get_address()) + payload.data_offset : nullptr;
        void* aux_ptr = payload.aux_size > 0 ? 
            static_cast<char*>(segment_.get_address()) + payload.aux_offset : nullptr;

        // Let caller process the data before we deallocate
        callback(data_ptr, payload.data_size, aux_ptr, payload.aux_size);

        // Deallocate memory
        if (data_ptr) pool_.deallocate(data_ptr);
        if (aux_ptr) pool_.deallocate(aux_ptr);

        consumer_sem_.post();
        return true;
    }

private:
    boost::interprocess::managed_shared_memory segment_;
    MemoryPool pool_;
    moodycamel::ReaderWriterQueue<Payload> queue_;
    boost::interprocess::interprocess_semaphore producer_sem_;
    boost::interprocess::interprocess_semaphore consumer_sem_;
};

}  // namespace hybrid
}  // namespace ssln 

#endif // SHARED_MEMORY_CHANNEL_H_ 