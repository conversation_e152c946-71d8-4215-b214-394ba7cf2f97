#ifndef SSLN_HYBRID_SIG_PAYLOAD_H
#define SSLN_HYBRID_SIG_PAYLOAD_H   

#include <cstdint>
#include "quill/DirectFormatCodec.h"
#include "quill/Utility.h"
namespace ssln{
namespace hybrid{


enum SigType{
    hw2tlm_req ,
    hw2tlm_reply,
    tlm2hw_req,
    tlm2hw_reply
};


// sideband signal structure for IPC communication
struct SigPayload{
    uint64_t id;
    uint8_t type; 
    uint32_t signal_width; // signal width
    uint8_t* data; // signal value
};

}
}

template <>
struct fmtquill::formatter<ssln::hybrid::SigPayload>{
    constexpr auto parse(format_parse_context& ctx) { return ctx.begin(); }
    auto format(ssln::hybrid::SigPayload const& payload, format_context& ctx) const {
        auto out = fmtquill::format_to(ctx.out(), "Sideband----------------------->>>\n");
        const uint8_t* data_ptr = payload.data;
        std::string type_s;
        switch (payload.type)
        {
        case ssln::hybrid::SigType::hw2tlm_req :
            type_s = "hw2tlm_req";
            break;
        case ssln::hybrid::SigType::hw2tlm_reply :
            type_s = "hw2tlm_reply";
            break;
        case ssln::hybrid::SigType::tlm2hw_req :
            type_s = "tlm2hw_req";
            break;
        case ssln::hybrid::SigType::tlm2hw_reply :
            type_s = "tlm2hw_reply";
            break;
        default:
            type_s = "error type";
            break;
        }

        out = fmtquill::format_to(out, "id={}, type={}, signal_width={}\n", payload.id, type_s, payload.signal_width);
        if(payload.type == ssln::hybrid::SigType::hw2tlm_req || payload.type == ssln::hybrid::SigType::tlm2hw_req){
            uint32_t data_bytes = (payload.signal_width + 7) / 8;
            out = fmtquill::format_to(out, "data={}\n", quill::utility::to_hex(data_ptr, data_bytes));
        }
        else{
            out = fmtquill::format_to(out, "data=\n");
        }

        return out;
    }

};
template <>
struct quill::Codec<ssln::hybrid::SigPayload> : quill::DirectFormatCodec<ssln::hybrid::SigPayload> {};
#endif