#ifndef SSLN_HYBRID_PROFILEING_H
#define SSLN_HYBRID_PROFILEING_H

#include <map>
#include <chrono>
#include <cstdlib>
#include <string>
#include <cstdint>
#include <stdexcept>
#include "ssln/sslogger.h"
namespace ssln{
namespace hybrid{



class SslnProfiling {
public:
    SslnProfiling(std::string module_name_){
        const char* val = std::getenv("SSLN_PROFILING_ENABLE");
        if(val){
            std::string s_val(val);
            if(s_val == "true"){
                enable = true;
            }
            else{
                SSLN_LOG_INFO(perf_logger, "SSLN_PROFILING_ENABLE is not ture, profiling is disable");
                enable = false;
            }
        }

        const char* interval = std::getenv("SSLN_PROFILING_INTERVAL");
        if(interval){
            std::string s_interval(interval);
            print_interval = std::stoi(s_interval);
        }
        SSLN_LOG_INFO(perf_logger, "DT = Duration \nN = Count");
        module_name = module_name_;
    }
    ~SslnProfiling(){

    }

    void add_start_time(const std::string& name){
        if(!enable) return;
        map_start_time_1[name] = std::chrono::high_resolution_clock::now();
    }
    void add_duration_time(const std::string& name){
        if(!enable) return;
        add_duration(map_duration_1, map_start_time_1, map_count_1, name);
    }

    void add_start_time_cthread(const std::string& name){
        if(!enable) return;
        map_start_time_2[name] = std::chrono::high_resolution_clock::now();
    }
    void add_duration_time_cthread(const std::string& name){
        if(!enable) return;
        add_duration(map_duration_2, map_start_time_2, map_count_2, name);
    }



private:

    void add_duration(std::map<std::string, std::chrono::duration<int64_t, std::micro> >&map_duration,
        std::map<std::string, std::chrono::high_resolution_clock::time_point >& map_start_time,
        std::map<std::string, uint64_t>& map_count, const std::string& name){
        std::chrono::high_resolution_clock::time_point now_time = std::chrono::high_resolution_clock::now();
        if(map_start_time.count(name) == 0){
            throw std::runtime_error("not match start time");
        }
        map_duration[name] += std::chrono::duration_cast<std::chrono::microseconds>(now_time -map_start_time[name]);
        map_count[name] += 1;

        if(map_count[name] % print_interval == 0){
            // if(msg == nullptr){
                std::cout << "perf log" << std::endl;
                SSLN_LOG_INFO(perf_logger, "[{}] {}, dura = {}us, count = {}", module_name, name, map_duration[name].count(), map_count[name]);
            // }
            // else{
            //     SSLN_LOG_INFO(perf_logger, "profiling@{}, dura={}, count={}", msg, map_duration[name].count(), map_count[name]);
            // }
        }
    }

    bool enable{false};
    uint32_t print_interval{128};
    std::string module_name;
    std::map<std::string, std::chrono::duration<int64_t, std::micro> > map_duration_1;
    std::map<std::string, std::chrono::high_resolution_clock::time_point > map_start_time_1;
    std::map<std::string, uint64_t> map_count_1;


    std::map<std::string, std::chrono::duration<int64_t, std::micro> > map_duration_2;
    std::map<std::string, std::chrono::high_resolution_clock::time_point > map_start_time_2;
    std::map<std::string, uint64_t> map_count_2;
};


}
}



#endif