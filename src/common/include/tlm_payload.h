#ifndef SSLN_HYBRID_TLM_PAYLOAD_H
#define SSLN_HYBRID_TLM_PAYLOAD_H
#include <iostream>
#include <cstdint>
#include <sstream>
#include <chrono>
#include <ctime>
#include <iomanip>
#include <string>
#include <tlm>
#include "quill/DirectFormatCodec.h"
#include "quill/Utility.h"

namespace ssln {
namespace hybrid {

// TLM payload structure for IPC communication
struct TlmPayload {
    uint64_t id;
    uint8_t command;              // Command type
    uint64_t address;             // Target address
    uint32_t data_length;         // Length of data
    uint32_t byte_enable_length;  // Length of byte enable
    uint32_t axuser_length;       // Length of axuser
    uint32_t xuser_length;        // Length of xuser
    uint32_t streaming_width;     // Streaming width
    int8_t response;              // Response status // use int8, because tlm::tlm_response_status has negative
    uint8_t *data;                // Variable length data followed by byte enable

    std::string format(bool is_req) {
        std::stringstream ss;
        ss << "------------------>> time stamp@ ";
        // 获取当前时间点
        auto now = std::chrono::system_clock::now();
        
        // 转换为时间_t 类型（即自1970年1月1日以来的秒数）
        std::time_t now_c = std::chrono::system_clock::to_time_t(now);
        
        // 转换为tm结构体以便输出日期和时间
        std::tm now_tm = *std::localtime(&now_c);
        
        // 获取当前时间的微秒部分
        auto duration = now.time_since_epoch();
        auto micros = std::chrono::duration_cast<std::chrono::microseconds>(duration).count() % 1000000;
        
        // 输出日期和时间（年月日 时分秒 微秒）
        ss << std::put_time(&now_tm, "%Y-%m-%d %H:%M:%S") << "." << std::setfill('0') << std::setw(6) << micros << "\n";

        ss << "Id: " << id << "\n";
        if (is_req) {  // true:
            ss << "Op: " << (command == 0 ? "read request" : "write request") << "\n";
        } else {
            ss << "Op: " << (command == 0 ? "read response" : "write response") << "\n";
        }
        ss << "Addr: 0x" << std::hex << address << "\n";
        ss << "Data length: 0x" << std::hex << data_length << "\n";
        ss << "Streaming width: 0x" << streaming_width << "\n";
        ss << "Byte enable length: 0x" << byte_enable_length << "\n";
        ss << "Axuser length: 0x" << axuser_length << "\n";
        ss << "Xuser length: 0x" << xuser_length << "\n";
        ss << "------------------>>";
        return ss.str();
    }
};

class XuserExtension : public tlm::tlm_extension<XuserExtension> {
public:
    XuserExtension(uint32_t data_length_, uint32_t req_length_)
        : data_length(data_length_), req_length(req_length_) {
    data = new uint8_t[data_length + req_length];
    }
    ~XuserExtension() { delete[] data; }

    virtual tlm_extension_base *clone() const {
        XuserExtension *ext = new XuserExtension(data_length, req_length);
        memcpy(ext->data, data, data_length + req_length);
        return ext;
    }

    virtual void copy_from(const tlm_extension_base &ext) {
        const XuserExtension &m_ext = static_cast<const XuserExtension &>(ext);
        delete[] data;
        data = new uint8_t[m_ext.data_length + m_ext.req_length];
        data_length = m_ext.data_length;
        req_length = m_ext.req_length;
        memcpy(data, m_ext.data, m_ext.data_length + m_ext.req_length);
    }

    uint8_t *get_req_ptr() { return data + data_length; }

    uint8_t *get_data_ptr() { return data; }

    uint8_t *data;
    uint32_t data_length;
    uint32_t req_length;
    int16_t response{0};
};



}  // namespace hybrid
}  // namespace ssln


// Formatter for TlmPayload
template <>
struct fmtquill::formatter<ssln::hybrid::TlmPayload> {
    constexpr auto parse(format_parse_context& ctx) { return ctx.begin(); }

    auto format(ssln::hybrid::TlmPayload const& payload, format_context& ctx) const {
        auto out = fmtquill::format_to(ctx.out(), "TlmPayload----------------------->>>\n");
        // Get pointers to different sections of data
        const uint8_t* data_ptr = payload.data;
        const uint8_t* byte_enable_ptr = data_ptr + payload.data_length;
        const uint8_t* axuser_ptr = byte_enable_ptr + payload.byte_enable_length;
        const uint8_t* xuser_ptr = axuser_ptr + payload.axuser_length;

        // Convert command and response to strings
        const char* cmd_str = payload.command == tlm::TLM_WRITE_COMMAND ? "write" : 
                            payload.command == tlm::TLM_READ_COMMAND ? "read" : "other";
        const char* t_type = payload.response == tlm::TLM_INCOMPLETE_RESPONSE ? "request" : "response";
        const char* resp_str = payload.response == tlm::TLM_OK_RESPONSE ? "response okay" : payload.response == tlm::TLM_INCOMPLETE_RESPONSE ? "request" : "response error";

        // Format basic fields
        // auto out = fmtquill::format_to(ctx.out(), "TlmPayload----------------------->>>\n");
        out = fmtquill::format_to(out, 
            "id={}, cmd={}-{}, addr={:#x}, sw={}, data_length={}, byte_enable_length={}, axuser_length={}, xuser_length={}, resp={}\n",
            payload.id, 
            cmd_str,
            t_type,
            payload.address,
            payload.streaming_width,
            payload.data_length,
            payload.byte_enable_length,
            payload.axuser_length,
            payload.xuser_length,
            resp_str);

        // Format data section
        if(payload.response == tlm::TLM_INCOMPLETE_RESPONSE){ // request
            if(payload.command == tlm::TLM_WRITE_COMMAND){
                out = fmtquill::format_to(out, "data={}\n", quill::utility::to_hex(data_ptr, payload.data_length));
            }
            else{
                out = fmtquill::format_to(out, "data=\n");
            }
        }
        else{ // response
            if(payload.command == tlm::TLM_WRITE_COMMAND){
                out = fmtquill::format_to(out, "data=\n");
            }
            else{
                out = fmtquill::format_to(out, "data={}\n", quill::utility::to_hex(data_ptr, payload.data_length));
            }
        }

        // Format byte enable section
        if(payload.response == tlm::TLM_INCOMPLETE_RESPONSE){ // request
            if(payload.command == tlm::TLM_WRITE_COMMAND){
                out = fmtquill::format_to(out, "be={}\n", quill::utility::to_hex(byte_enable_ptr, payload.byte_enable_length));
            }
            else{
                out = fmtquill::format_to(out, "be=\n");
            }
        }
        else{ // response
            out = fmtquill::format_to(out, "be=\n");
        }

        // Format axuser section
        out = fmtquill::format_to(out, "axuser={}\n", quill::utility::to_hex(axuser_ptr, payload.axuser_length));

        // Format xuser section
        out = fmtquill::format_to(out, "xuser={}\n", quill::utility::to_hex(xuser_ptr, payload.xuser_length));

        return out;
    }
};

// Codec for TlmPayload
template <>
struct quill::Codec<ssln::hybrid::TlmPayload> : quill::DirectFormatCodec<ssln::hybrid::TlmPayload> {};


#endif  // SSLN_HYBRID_TLM_PAYLOAD_H
