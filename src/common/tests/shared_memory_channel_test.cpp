#include <gtest/gtest.h>
#include <thread>
#include <chrono>
#include <cstddef>  // for offsetof
#include "shared_memory_channel.h"

namespace ssln {
namespace hybrid {
namespace testing {

// Test payload structure
struct TestPayload {
    static constexpr size_t kDataSize = 64;
    alignas(8) int value;
    alignas(8) char data[kDataSize];
    SharedMemoryAllocator<char> allocator_;

    explicit TestPayload(const SharedMemoryAllocator<char>& alloc) 
        : value(0)
        , allocator_(alloc) {
        std::fill(std::begin(data), std::end(data), 0);
    }

    ~TestPayload() = default;
};

// 性能测试的payload结构
struct alignas(8) BenchPayload {
    // 减小数据大小，使总大小为240字节（包括8字节的allocator）
    // 这样boost::interprocess可以添加16字节的头信息，使总块大小为256字节
    static constexpr size_t kDataSize = 232;
    alignas(8) char data[kDataSize];
    alignas(8) SharedMemoryAllocator<char> allocator_;

    explicit BenchPayload(const SharedMemoryAllocator<char>& alloc) 
        : allocator_(alloc) {
        std::fill(std::begin(data), std::end(data), 0);
    }

    ~BenchPayload() = default;

    // 打印内存布局信息的辅助函数
    static void PrintLayout() {
        std::cout << "BenchPayload memory layout:\n"
                  << "  Total size: " << sizeof(BenchPayload) << " bytes\n"
                  << "  Allocator size: " << sizeof(SharedMemoryAllocator<char>) << " bytes\n"
                  << "  Data array offset: " << offsetof(BenchPayload, data) << " bytes\n"
                  << "  Data array size: " << kDataSize << " bytes\n"
                  << "  Allocator offset: " << offsetof(BenchPayload, allocator_) << " bytes\n"
                  << "  Is properly aligned: " << (sizeof(BenchPayload) % alignof(BenchPayload) == 0) << "\n"
                  << "  Alignment: " << alignof(BenchPayload) << " bytes\n"
                  << "  Size is multiple of 8: " << (sizeof(BenchPayload) % 8 == 0) << "\n"
                  << "  Size is multiple of 16: " << (sizeof(BenchPayload) % 16 == 0) << "\n";
    }
};

class SharedMemoryChannelTest : public ::testing::Test {
protected:
    void SetUp() override {
        config_.shm_name = "test_shm";
        config_.shm_size = 65536;
        config_.queue_size = 1024;
    }

    void TearDown() override {
        boost::interprocess::shared_memory_object::remove(config_.shm_name.c_str());
    }

    ShmConfiguration config_;
};

class PerformanceTest : public ::testing::Test {
protected:
    void SetUp() override {
        config_.shm_name = "perf_test_shm";
        config_.shm_size = 1024 * 1024 * 256;  // 256MB
        config_.queue_size = 32;  // 减小队列大小
        boost::interprocess::shared_memory_object::remove(config_.shm_name.c_str());
    }

    void TearDown() override {
        boost::interprocess::shared_memory_object::remove(config_.shm_name.c_str());
    }

    template<typename Channel>
    void RunBenchmark(const std::string& test_name, size_t num_messages) {
        std::atomic<bool> producer_ready{false};
        std::atomic<bool> consumer_ready{false};
        std::atomic<bool> error_occurred{false};
        std::atomic<size_t> processed_messages{0};

        auto start_time = std::chrono::high_resolution_clock::now();

        std::thread producer([&]() {
            try {
                ShmHandler handler;
                config_.role = ShmRole::CREATOR;
                auto segment = handler.CreateOrOpenSegment(config_);
                if (!segment) {
                    std::cerr << "Failed to create segment" << std::endl;
                    error_occurred = true;
                    return;
                }

                auto channel = Channel::Create(*segment, config_.queue_size);
                if (!channel) {
                    std::cerr << "Failed to create channel" << std::endl;
                    error_occurred = true;
                    return;
                }

                producer_ready = true;
                while (!consumer_ready && !error_occurred) {
                    std::this_thread::sleep_for(std::chrono::milliseconds(1));
                }

                if (error_occurred) {
                    return;
                }

                // 生产者等待消费者准备好后再开始发送数据
                for (size_t i = 0; i < num_messages && !error_occurred; ++i) {
                    auto payload = channel->AllocatePayload(*segment);
                    if (!payload) {
                        std::cerr << "Failed to allocate payload " << i << std::endl;
                        error_occurred = true;
                        break;
                    }

                    try {
                        std::fill(std::begin(payload->data), std::end(payload->data), static_cast<char>(i));
                    } catch (const std::exception& e) {
                        std::cerr << "Exception while filling payload: " << e.what() << std::endl;
                        channel->DeallocatePayload(*segment, payload);
                        error_occurred = true;
                        break;
                    }

                    // 等待队列有空间
                    size_t retry_count = 0;
                    const size_t max_retries = 1000;
                    while (!channel->EnqueuePayload(payload)) {
                        if (++retry_count >= max_retries) {
                            std::cerr << "Failed to enqueue payload after " << max_retries << " attempts" << std::endl;
                            channel->DeallocatePayload(*segment, payload);
                            error_occurred = true;
                            break;
                        }
                        std::this_thread::yield();
                    }

                    if (error_occurred) break;
                    channel->data_ready_.post();
                }
            } catch (const std::exception& e) {
                std::cerr << "Producer exception: " << e.what() << std::endl;
                error_occurred = true;
            }
        });

        std::thread consumer([&]() {
            try {
                ShmHandler handler;
                config_.role = ShmRole::OPENER;
                auto segment = handler.CreateOrOpenSegment(config_);
                if (!segment) {
                    std::cerr << "Failed to open segment" << std::endl;
                    error_occurred = true;
                    return;
                }

                while (!producer_ready && !error_occurred) {
                    std::this_thread::sleep_for(std::chrono::milliseconds(1));
                }

                auto channel = Channel::Open(*segment);
                if (!channel) {
                    std::cerr << "Failed to open channel" << std::endl;
                    error_occurred = true;
                    return;
                }

                consumer_ready = true;

                for (size_t i = 0; i < num_messages && !error_occurred; ++i) {
                    channel->data_ready_.wait();
                    BenchPayload* payload = nullptr;
                    
                    size_t retry_count = 0;
                    const size_t max_retries = 1000;
                    
                    while (!channel->DequeuePayload(payload)) {
                        if (++retry_count >= max_retries) {
                            std::cerr << "Failed to dequeue payload after " << max_retries << " attempts" << std::endl;
                            error_occurred = true;
                            break;
                        }
                        std::this_thread::yield();
                    }

                    if (error_occurred) break;

                    if (payload) {
                        // 验证数据
                        bool data_valid = true;
                        char expected_value = static_cast<char>(i);
                        for (char c : payload->data) {
                            if (c != expected_value) {
                                data_valid = false;
                                break;
                            }
                        }
                        if (!data_valid) {
                            std::cerr << "Data validation failed at message " << i << std::endl;
                            error_occurred = true;
                        }

                        channel->DeallocatePayload(*segment, payload);
                        processed_messages++;
                    } else {
                        std::cerr << "Received null payload at message " << i << std::endl;
                        error_occurred = true;
                        break;
                    }
                }
            } catch (const std::exception& e) {
                std::cerr << "Consumer exception: " << e.what() << std::endl;
                error_occurred = true;
            }
        });

        producer.join();
        consumer.join();

        if (error_occurred) {
            std::cerr << "Test failed due to errors" << std::endl;
            std::cerr << "Processed " << processed_messages << " messages before failure" << std::endl;
            return;
        }

        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
        
        double throughput = static_cast<double>(num_messages) / duration.count() * 1000000;  // msgs/sec
        double latency = static_cast<double>(duration.count()) / num_messages;  // microseconds/msg

        std::cout << "\n" << test_name << " Results:\n"
                  << "  Total time: " << duration.count() / 1000.0 << " ms\n"
                  << "  Throughput: " << throughput << " msgs/sec\n"
                  << "  Avg Latency: " << latency << " microseconds/msg\n"
                  << "  Processed messages: " << processed_messages << "\n";
    }

    ShmConfiguration config_;
};

/*
// Test creation and opening of shared memory channel
TEST_F(SharedMemoryChannelTest, CreateAndOpen) {
    // Creator process simulation
    std::thread creator([this]() {
        ShmHandler handler;
        config_.role = ShmRole::CREATOR;
        auto segment = handler.CreateOrOpenSegment(config_);
        auto channel = SharedMemoryChannel<TestPayload>::Create(*segment, config_.queue_size);
        EXPECT_NE(channel, nullptr);
        
        // Keep the segment alive for a while
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    });

    // Opener process simulation
    std::thread opener([this]() {
        ShmHandler handler;
        config_.role = ShmRole::OPENER;
        auto segment = handler.CreateOrOpenSegment(config_);
        auto channel = SharedMemoryChannel<TestPayload>::Open(*segment);
        EXPECT_NE(channel, nullptr);
    });

    creator.join();
    opener.join();
}

// Test data transmission between processes
TEST_F(SharedMemoryChannelTest, DataTransmission) {
    const int TEST_VALUE = 42;
    const std::string TEST_DATA = "Hello, SharedMemory!";
    
    // Creator process simulation
    std::thread creator([this, TEST_VALUE, TEST_DATA]() {
        ShmHandler handler;
        config_.role = ShmRole::CREATOR;
        auto segment = handler.CreateOrOpenSegment(config_);
        auto channel = SharedMemoryChannel<TestPayload>::Create(*segment, config_.queue_size);
        
        // Create and send payload
        auto alloc = SharedMemoryAllocator<char>(segment->get_segment_manager());
        auto payload = segment->construct<TestPayload>(boost::interprocess::anonymous_instance)(alloc);
        
        payload->value = TEST_VALUE;
        std::copy(TEST_DATA.begin(), TEST_DATA.end(), std::begin(payload->data));
        
        bool enqueued = channel->queue_->try_enqueue(payload);
        EXPECT_TRUE(enqueued);
        channel->data_ready_.post();
        
        // Wait for response
        channel->response_ready_.wait();
    });

    // Opener process simulation
    std::thread opener([this, TEST_VALUE, TEST_DATA]() {
        ShmHandler handler;
        config_.role = ShmRole::OPENER;
        auto segment = handler.CreateOrOpenSegment(config_);
        auto channel = SharedMemoryChannel<TestPayload>::Open(*segment);
        
        // Wait for data
        channel->data_ready_.wait();
        
        TestPayload* received = nullptr;
        bool dequeued = channel->queue_->try_dequeue(received);
        EXPECT_TRUE(dequeued);
        EXPECT_NE(received, nullptr);
        
        // Verify data
        EXPECT_EQ(received->value, TEST_VALUE);
        EXPECT_EQ(std::string(received->data, received->data + TEST_DATA.size()), TEST_DATA);
        
        // Signal completion
        channel->response_ready_.post();
    });

    creator.join();
    opener.join();
}
*/

TEST_F(PerformanceTest, BenchmarkReaderWriterQueue) {
    const size_t NUM_MESSAGES = 50;  // 减少消息数量

    // 打印内存布局信息
    BenchPayload::PrintLayout();

    std::cout << "\nRunning performance test with " << NUM_MESSAGES << " messages of "
              << BenchPayload::kDataSize << " bytes each\n";

    RunBenchmark<SharedMemoryChannel<BenchPayload>>(
        "ReaderWriterQueue Implementation", NUM_MESSAGES);
}

}  // namespace testing
}  // namespace hybrid
}  // namespace ssln

int main(int argc, char **argv) {
    testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
} 