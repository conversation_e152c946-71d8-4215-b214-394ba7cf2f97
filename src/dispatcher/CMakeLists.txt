# Find required packages for this module
find_package(Flatbuffers REQUIRED)

# --- Manual ZeroMQ Discovery ---
# Since ZeroMQ doesn't provide a config file, we find it manually.
find_path(ZeroMQ_INCLUDE_DIR NAMES zmq.h
    HINTS ${CMAKE_PREFIX_PATH}/include/zeromq
    PATH_SUFFIXES include
)
find_library(ZeroMQ_LIBRARY NAMES zmq
    HINTS ${CMAKE_PREFIX_PATH}/lib
)

if (NOT ZeroMQ_INCLUDE_DIR OR NOT ZeroMQ_LIBRARY)
    message(FATAL_ERROR "Could not find ZeroMQ headers or library.")
endif()

# --- FlatBuffers Compilation ---
# Define the path to the schema file and the output directory for the generated header.
set(FBS_FILE ${CMAKE_CURRENT_SOURCE_DIR}/schemas/payload.fbs)
set(FBS_OUTPUT_DIR ${CMAKE_CURRENT_BINARY_DIR}/generated)
include_directories(SYSTEM ${FBS_OUTPUT_DIR})

# Create the output directory.
file(MAKE_DIRECTORY ${FBS_OUTPUT_DIR})

# Add a custom command to run the FlatBuffers compiler (flatc).
# This command will be executed before compiling any source file that depends on the output.
add_custom_command(
    OUTPUT ${FBS_OUTPUT_DIR}/payload_generated.h
    COMMAND ${FLATBUFFERS_FLATC_EXECUTABLE} -o ${FBS_OUTPUT_DIR} --cpp ${FBS_FILE}
    DEPENDS ${FBS_FILE}
    COMMENT "Running FlatBuffers compiler on ${FBS_FILE}"
)

# Add a custom target to make the generated header a dependency for other targets.
add_custom_target(
    dispatcher_generate_headers
    DEPENDS ${FBS_OUTPUT_DIR}/payload_generated.h
)

# --- Library Definition ---
# Create a source group for clarity in IDEs
source_group(
    "Header Files"
    FILES
        "include/dispatcher.h"
        "include/i_endpoint.h"
)

# Define the dispatcher library.
# It starts as an INTERFACE library because it only contains headers.
# Once we add .cpp files, we will change this.
add_library(dispatcher INTERFACE)

# Add the generated headers target as a dependency to the dispatcher library.
add_dependencies(dispatcher dispatcher_generate_headers)

# Specify the public include directories for the dispatcher library.
# Any target linking against 'dispatcher' will automatically get this include path.
target_include_directories(dispatcher INTERFACE
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
    $<BUILD_INTERFACE:${FBS_OUTPUT_DIR}>
)

# Link the required libraries (Flatbuffers, ZMQ) to the dispatcher library.
target_link_libraries(dispatcher INTERFACE
    ${FLATBUFFERS_LIBRARIES}
    ${ZeroMQ_LIBRARY}
)
# No need to add include directory here, it's implicitly handled by find_path and linking
# target_include_directories(dispatcher INTERFACE
#     ${ZeroMQ_INCLUDE_DIR}
# )