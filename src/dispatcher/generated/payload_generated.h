// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_PAYLOAD_SSLN_HYBRID_DISPATCH_H_
#define FLATBUFFERS_GENERATED_PAYLOAD_SSLN_HYBRID_DISPATCH_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 23 &&
              FLATBUFFERS_VERSION_MINOR == 5 &&
              FLATBUFFERS_VERSION_REVISION == 26,
             "Non-compatible flatbuffers version included");

namespace ssln {
namespace hybrid {
namespace dispatch {

struct Payload;
struct PayloadBuilder;

enum PayloadType : uint8_t {
  PayloadType_TLM = 0,
  PayloadType_SIDEBAND = 1,
  PayloadType_MIN = PayloadType_TLM,
  PayloadType_MAX = PayloadType_SIDEBAND
};

inline const PayloadType (&EnumValuesPayloadType())[2] {
  static const PayloadType values[] = {
    PayloadType_TLM,
    PayloadType_SIDEBAND
  };
  return values;
}

inline const char * const *EnumNamesPayloadType() {
  static const char * const names[3] = {
    "TLM",
    "SIDEBAND",
    nullptr
  };
  return names;
}

inline const char *EnumNamePayloadType(PayloadType e) {
  if (::flatbuffers::IsOutRange(e, PayloadType_TLM, PayloadType_SIDEBAND)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesPayloadType()[index];
}

enum SidebandType : uint8_t {
  SidebandType_HW2TLM_REQ = 0,
  SidebandType_HW2TLM_REPLY = 1,
  SidebandType_TLM2HW_REQ = 2,
  SidebandType_TLM2HW_REPLY = 3,
  SidebandType_MIN = SidebandType_HW2TLM_REQ,
  SidebandType_MAX = SidebandType_TLM2HW_REPLY
};

inline const SidebandType (&EnumValuesSidebandType())[4] {
  static const SidebandType values[] = {
    SidebandType_HW2TLM_REQ,
    SidebandType_HW2TLM_REPLY,
    SidebandType_TLM2HW_REQ,
    SidebandType_TLM2HW_REPLY
  };
  return values;
}

inline const char * const *EnumNamesSidebandType() {
  static const char * const names[5] = {
    "HW2TLM_REQ",
    "HW2TLM_REPLY",
    "TLM2HW_REQ",
    "TLM2HW_REPLY",
    nullptr
  };
  return names;
}

inline const char *EnumNameSidebandType(SidebandType e) {
  if (::flatbuffers::IsOutRange(e, SidebandType_HW2TLM_REQ, SidebandType_TLM2HW_REPLY)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesSidebandType()[index];
}

struct Payload FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef PayloadBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_DESTINATION_ID = 4,
    VT_PAYLOAD_TYPE = 6,
    VT_ID = 8,
    VT_COMMAND = 10,
    VT_ADDRESS = 12,
    VT_RESPONSE = 14,
    VT_STREAMING_WIDTH = 16,
    VT_SIDEBAND_TYPE = 18,
    VT_SIGNAL_WIDTH = 20,
    VT_VARIABLE_DATA = 22,
    VT_DATA_LENGTH = 24,
    VT_BYTE_ENABLE_LENGTH = 26,
    VT_AXUSER_LENGTH = 28,
    VT_XUSER_LENGTH = 30
  };
  uint32_t destination_id() const {
    return GetField<uint32_t>(VT_DESTINATION_ID, 0);
  }
  ssln::hybrid::dispatch::PayloadType payload_type() const {
    return static_cast<ssln::hybrid::dispatch::PayloadType>(GetField<uint8_t>(VT_PAYLOAD_TYPE, 0));
  }
  uint64_t id() const {
    return GetField<uint64_t>(VT_ID, 0);
  }
  uint8_t command() const {
    return GetField<uint8_t>(VT_COMMAND, 0);
  }
  uint64_t address() const {
    return GetField<uint64_t>(VT_ADDRESS, 0);
  }
  int8_t response() const {
    return GetField<int8_t>(VT_RESPONSE, 0);
  }
  uint32_t streaming_width() const {
    return GetField<uint32_t>(VT_STREAMING_WIDTH, 0);
  }
  ssln::hybrid::dispatch::SidebandType sideband_type() const {
    return static_cast<ssln::hybrid::dispatch::SidebandType>(GetField<uint8_t>(VT_SIDEBAND_TYPE, 0));
  }
  uint32_t signal_width() const {
    return GetField<uint32_t>(VT_SIGNAL_WIDTH, 0);
  }
  const ::flatbuffers::Vector<uint8_t> *variable_data() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_VARIABLE_DATA);
  }
  uint32_t data_length() const {
    return GetField<uint32_t>(VT_DATA_LENGTH, 0);
  }
  uint32_t byte_enable_length() const {
    return GetField<uint32_t>(VT_BYTE_ENABLE_LENGTH, 0);
  }
  uint32_t axuser_length() const {
    return GetField<uint32_t>(VT_AXUSER_LENGTH, 0);
  }
  uint32_t xuser_length() const {
    return GetField<uint32_t>(VT_XUSER_LENGTH, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_DESTINATION_ID, 4) &&
           VerifyField<uint8_t>(verifier, VT_PAYLOAD_TYPE, 1) &&
           VerifyField<uint64_t>(verifier, VT_ID, 8) &&
           VerifyField<uint8_t>(verifier, VT_COMMAND, 1) &&
           VerifyField<uint64_t>(verifier, VT_ADDRESS, 8) &&
           VerifyField<int8_t>(verifier, VT_RESPONSE, 1) &&
           VerifyField<uint32_t>(verifier, VT_STREAMING_WIDTH, 4) &&
           VerifyField<uint8_t>(verifier, VT_SIDEBAND_TYPE, 1) &&
           VerifyField<uint32_t>(verifier, VT_SIGNAL_WIDTH, 4) &&
           VerifyOffset(verifier, VT_VARIABLE_DATA) &&
           verifier.VerifyVector(variable_data()) &&
           VerifyField<uint32_t>(verifier, VT_DATA_LENGTH, 4) &&
           VerifyField<uint32_t>(verifier, VT_BYTE_ENABLE_LENGTH, 4) &&
           VerifyField<uint32_t>(verifier, VT_AXUSER_LENGTH, 4) &&
           VerifyField<uint32_t>(verifier, VT_XUSER_LENGTH, 4) &&
           verifier.EndTable();
  }
};

struct PayloadBuilder {
  typedef Payload Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_destination_id(uint32_t destination_id) {
    fbb_.AddElement<uint32_t>(Payload::VT_DESTINATION_ID, destination_id, 0);
  }
  void add_payload_type(ssln::hybrid::dispatch::PayloadType payload_type) {
    fbb_.AddElement<uint8_t>(Payload::VT_PAYLOAD_TYPE, static_cast<uint8_t>(payload_type), 0);
  }
  void add_id(uint64_t id) {
    fbb_.AddElement<uint64_t>(Payload::VT_ID, id, 0);
  }
  void add_command(uint8_t command) {
    fbb_.AddElement<uint8_t>(Payload::VT_COMMAND, command, 0);
  }
  void add_address(uint64_t address) {
    fbb_.AddElement<uint64_t>(Payload::VT_ADDRESS, address, 0);
  }
  void add_response(int8_t response) {
    fbb_.AddElement<int8_t>(Payload::VT_RESPONSE, response, 0);
  }
  void add_streaming_width(uint32_t streaming_width) {
    fbb_.AddElement<uint32_t>(Payload::VT_STREAMING_WIDTH, streaming_width, 0);
  }
  void add_sideband_type(ssln::hybrid::dispatch::SidebandType sideband_type) {
    fbb_.AddElement<uint8_t>(Payload::VT_SIDEBAND_TYPE, static_cast<uint8_t>(sideband_type), 0);
  }
  void add_signal_width(uint32_t signal_width) {
    fbb_.AddElement<uint32_t>(Payload::VT_SIGNAL_WIDTH, signal_width, 0);
  }
  void add_variable_data(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> variable_data) {
    fbb_.AddOffset(Payload::VT_VARIABLE_DATA, variable_data);
  }
  void add_data_length(uint32_t data_length) {
    fbb_.AddElement<uint32_t>(Payload::VT_DATA_LENGTH, data_length, 0);
  }
  void add_byte_enable_length(uint32_t byte_enable_length) {
    fbb_.AddElement<uint32_t>(Payload::VT_BYTE_ENABLE_LENGTH, byte_enable_length, 0);
  }
  void add_axuser_length(uint32_t axuser_length) {
    fbb_.AddElement<uint32_t>(Payload::VT_AXUSER_LENGTH, axuser_length, 0);
  }
  void add_xuser_length(uint32_t xuser_length) {
    fbb_.AddElement<uint32_t>(Payload::VT_XUSER_LENGTH, xuser_length, 0);
  }
  explicit PayloadBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<Payload> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<Payload>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<Payload> CreatePayload(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t destination_id = 0,
    ssln::hybrid::dispatch::PayloadType payload_type = ssln::hybrid::dispatch::PayloadType_TLM,
    uint64_t id = 0,
    uint8_t command = 0,
    uint64_t address = 0,
    int8_t response = 0,
    uint32_t streaming_width = 0,
    ssln::hybrid::dispatch::SidebandType sideband_type = ssln::hybrid::dispatch::SidebandType_HW2TLM_REQ,
    uint32_t signal_width = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> variable_data = 0,
    uint32_t data_length = 0,
    uint32_t byte_enable_length = 0,
    uint32_t axuser_length = 0,
    uint32_t xuser_length = 0) {
  PayloadBuilder builder_(_fbb);
  builder_.add_address(address);
  builder_.add_id(id);
  builder_.add_xuser_length(xuser_length);
  builder_.add_axuser_length(axuser_length);
  builder_.add_byte_enable_length(byte_enable_length);
  builder_.add_data_length(data_length);
  builder_.add_variable_data(variable_data);
  builder_.add_signal_width(signal_width);
  builder_.add_streaming_width(streaming_width);
  builder_.add_destination_id(destination_id);
  builder_.add_sideband_type(sideband_type);
  builder_.add_response(response);
  builder_.add_command(command);
  builder_.add_payload_type(payload_type);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<Payload> CreatePayloadDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t destination_id = 0,
    ssln::hybrid::dispatch::PayloadType payload_type = ssln::hybrid::dispatch::PayloadType_TLM,
    uint64_t id = 0,
    uint8_t command = 0,
    uint64_t address = 0,
    int8_t response = 0,
    uint32_t streaming_width = 0,
    ssln::hybrid::dispatch::SidebandType sideband_type = ssln::hybrid::dispatch::SidebandType_HW2TLM_REQ,
    uint32_t signal_width = 0,
    const std::vector<uint8_t> *variable_data = nullptr,
    uint32_t data_length = 0,
    uint32_t byte_enable_length = 0,
    uint32_t axuser_length = 0,
    uint32_t xuser_length = 0) {
  auto variable_data__ = variable_data ? _fbb.CreateVector<uint8_t>(*variable_data) : 0;
  return ssln::hybrid::dispatch::CreatePayload(
      _fbb,
      destination_id,
      payload_type,
      id,
      command,
      address,
      response,
      streaming_width,
      sideband_type,
      signal_width,
      variable_data__,
      data_length,
      byte_enable_length,
      axuser_length,
      xuser_length);
}

inline const ssln::hybrid::dispatch::Payload *GetPayload(const void *buf) {
  return ::flatbuffers::GetRoot<ssln::hybrid::dispatch::Payload>(buf);
}

inline const ssln::hybrid::dispatch::Payload *GetSizePrefixedPayload(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<ssln::hybrid::dispatch::Payload>(buf);
}

inline bool VerifyPayloadBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<ssln::hybrid::dispatch::Payload>(nullptr);
}

inline bool VerifySizePrefixedPayloadBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<ssln::hybrid::dispatch::Payload>(nullptr);
}

inline void FinishPayloadBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<ssln::hybrid::dispatch::Payload> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedPayloadBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<ssln::hybrid::dispatch::Payload> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace dispatch
}  // namespace hybrid
}  // namespace ssln

#endif  // FLATBUFFERS_GENERATED_PAYLOAD_SSLN_HYBRID_DISPATCH_H_
