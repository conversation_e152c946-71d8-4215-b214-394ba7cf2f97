#ifndef SSLN_HYBRID_DISPATCHER_H
#define SSLN_HYBRID_DISPATCHER_H

#include <cstdint>
#include <cstddef>
#include <string>
#include <memory>
#include "i_endpoint.h"

namespace ssln {
namespace hybrid {

// Forward declaration of the Dispatcher class for the factory function
class Dispatcher;

// Enum for selecting the backend technology
enum class DispatcherType { SHM, ZMQ };

// Configuration structure for creating a Dispatcher instance
struct DispatcherConfig {
    // The unique name for this communication channel (e.g., "axi_bus")
    std::string channel_name; 

    // The backend communication technology to use
    DispatcherType type;

    // The role this dispatcher instance will play on the channel
    enum class Role {
        CREATOR, // Creates/Binds the channel (e.g., ZMQ_BIND, SHM Creator)
        OPENER   // Opens/Connects to an existing channel (e.g., ZMQ_CONNECT, SHM Opener)
    };
    Role role;
};

/**
 * @class Dispatcher
 * @brief An abstract base class for message dispatching channels.
 *
 * This class defines the interface for a communication channel that can
 * register endpoints and route messages between them. Concrete implementations
 * will provide backends for different IPC mechanisms like Shared Memory or ZMQ.
 */
class Dispatcher {
public:
    virtual ~Dispatcher() = default;

    /**
     * @brief Registers a communication endpoint with the dispatcher.
     * @param endpoint A pointer to an object that implements the IEndpoint interface.
     * @return True if registration is successful, false otherwise.
     */
    virtual bool register_endpoint(IEndpoint* endpoint) = 0;

    /**
     * @brief Sends a data buffer to a specific destination endpoint.
     * @param destination_id The unique ID of the target component.
     * @param buffer A pointer to the data buffer to send.
     * @param size The size of the data buffer.
     * @return True if the data was successfully sent, false otherwise.
     */
    virtual bool send(uint32_t destination_id, const void* buffer, size_t size) = 0;
    
    /**
     * @brief Starts the dispatcher's event loop in a background thread.
     */
    virtual void start() = 0;

    /**
     * @brief Stops the dispatcher's event loop and cleans up resources.
     */
    virtual void stop() = 0;
};

/**
 * @brief Factory function to create a concrete Dispatcher instance.
 * @param config The configuration specifying the channel name, type, and role.
 * @return A unique_ptr to the created Dispatcher, or nullptr on failure.
 */
std::unique_ptr<Dispatcher> create_dispatcher(const DispatcherConfig& config);

} // namespace hybrid
} // namespace ssln

#endif // SSLN_HYBRID_DISPATCHER_H