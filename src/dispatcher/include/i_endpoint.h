#ifndef SSLN_HYBRID_I_ENDPOINT_H_
#define SSLN_HYBRID_I_ENDPOINT_H_

#include <cstddef>
#include <cstdint>

namespace ssln {
namespace hybrid {

/**
 * @class IEndpoint
 * @brief Represents a communicable endpoint that can be managed by a Dispatcher.
 *
 * This interface defines the contract for any component (like a Proxy or a Bridge)
 * that wishes to send or receive data through the dispatching system. It ensures
 * that all communication endpoints can be handled polymorphically.
 */
class IEndpoint {
 public:
  virtual ~IEndpoint() = default;

  /**
   * @brief Handles incoming data from the dispatcher.
   * @param data A pointer to the read-only data buffer (e.g., a FlatBuffer).
   * @param size The size of the data buffer.
   * @return True if the data was handled successfully, false otherwise.
   */
  virtual bool HandleData(const void* data, size_t size) = 0;

  /**
   * @brief Gets the unique component ID of this endpoint.
   * @return The 32-bit unique identifier for this component.
   */
  virtual uint32_t GetComponentId() const = 0;
};

}  // namespace hybrid
}  // namespace ssln

#endif  // SSLN_HYBRID_I_ENDPOINT_H_