// FlatBuffers schema for the dispatcher payload.
// This defines the "on-the-wire" format for all data
// transmitted through any dispatcher backend (SHM, ZMQ, etc.).

namespace ssln.hybrid.dispatch;

// Enum for payload types
enum PayloadType:ubyte {
  TLM = 0,      // TLM transaction payload
  SIDEBAND = 1  // Sideband signal payload
}

// Enum for sideband signal types
enum SidebandType:ubyte {
  HW2TLM_REQ = 0,
  HW2TLM_REPLY = 1,
  TLM2HW_REQ = 2,
  TLM2HW_REPLY = 3
}

// The main payload structure.
table Payload {
    // --- Routing Information ---
    // The component ID of the final destination for this payload.
    destination_id:uint;

    // --- Payload Type ---
    // Indicates whether this is a TLM or Sideband payload
    payload_type:PayloadType = TLM;

    // --- Core TLM Data (mirroring TlmPayload) ---
    // These fields are used when payload_type == TLM
    id:ulong;
    command:ubyte;
    address:ulong;
    response:byte;
    streaming_width:uint;

    // --- Sideband Signal Data ---
    // These fields are used when payload_type == SIDEBAND
    sideband_type:SidebandType;
    signal_width:uint;

    // --- Variable-Length Data Section ---
    // For TLM: All variable-length fields from the original TlmPayload (data,
    // byte_enable, axuser, xuser) are concatenated into this single
    // byte vector for efficient, single-buffer transmission.
    // For Sideband: Contains the signal data
    variable_data:[ubyte];

    // --- Metadata for slicing variable_data (TLM only) ---
    // These lengths allow the receiver to reconstruct views into the
    // variable_data buffer without any memory copies.
    data_length:uint;
    byte_enable_length:uint;
    axuser_length:uint;
    xuser_length:uint;
}

// The root of a serialized message will be a Payload object.
root_type Payload;