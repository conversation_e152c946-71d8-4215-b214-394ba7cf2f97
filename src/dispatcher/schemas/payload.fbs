// FlatBuffers schema for the dispatcher payload.
// This defines the "on-the-wire" format for all data
// transmitted through any dispatcher backend (SHM, ZMQ, etc.).

namespace ssln.hybrid.dispatch;

// The main payload structure.
table Payload {
    // --- Routing Information ---
    // The component ID of the final destination for this payload.
    destination_id:uint;

    // --- Core TLM Data (mirroring TlmPayload) ---
    id:ulong;
    command:ubyte;
    address:ulong;
    response:byte;
    streaming_width:uint;
    
    // --- Variable-Length Data Section ---
    // All variable-length fields from the original TlmPayload (data, 
    // byte_enable, axuser, xuser) are concatenated into this single
    // byte vector for efficient, single-buffer transmission.
    variable_data:[ubyte];

    // --- Metadata for slicing variable_data ---
    // These lengths allow the receiver to reconstruct views into the
    // variable_data buffer without any memory copies.
    data_length:uint;
    byte_enable_length:uint;
    axuser_length:uint;
    xuser_length:uint;
}

// The root of a serialized message will be a Payload object.
root_type Payload;