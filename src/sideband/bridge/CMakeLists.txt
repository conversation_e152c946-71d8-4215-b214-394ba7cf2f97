add_library(sideband_sig_bridge
    src/sideband_sig_bridge.cc
)

target_include_directories(sideband_sig_bridge
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}/include
        $ENV{UMICOM_HOME}/include
        $ENV{UMICOM_HOME}/include/systemc
        #$ENV{XTOR_ROOT}/xtor_sig/c
)

target_link_libraries(sideband_sig_bridge
    PUBLIC
        common
        dispatcher
        axi_common
        sslogger
)

target_compile_definitions(sideband_sig_bridge
    PUBLIC
        #SC_CPLUSPLUS=201402L
)

target_compile_features(sideband_sig_bridge
    PUBLIC
        cxx_std_17
)

install(TARGETS sideband_sig_bridge
    LIBRARY DESTINATION ${CMAKE_INSTALL_PREFIX}/${LIB_VERSION}
    ARCHIVE DESTINATION ${CMAKE_INSTALL_PREFIX}/${LIB_VERSION}
)

add_subdirectory(tests EXCLUDE_FROM_ALL)