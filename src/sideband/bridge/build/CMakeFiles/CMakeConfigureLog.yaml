
---
events:
  -
    kind: "find-v1"
    backtrace:
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineSystem.cmake:12 (find_program)"
      - "CMakeLists.txt"
    mode: "program"
    variable: "CMAKE_UNAME"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "uname"
    candidate_directories:
      - "/opt/rh/devtoolset-11/root/usr/bin/"
      - "/opt/synopsys/vcs/V-2023.12-SP2/bin/"
      - "/opt/synopsys/verdi/V-2023.12-SP2/bin/"
      - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli/"
      - "/home/<USER>/git-extras/bin/"
      - "/home/<USER>/zsh/bin/"
      - "/home/<USER>/zeromq/bin/"
      - "/home/<USER>/yodl/bin/"
      - "/home/<USER>/xsel/bin/"
      - "/home/<USER>/xclip/bin/"
      - "/home/<USER>/vim/bin/"
      - "/home/<USER>/verible/bin/"
      - "/home/<USER>/tmux/bin/"
      - "/home/<USER>/tcl/bin/"
      - "/home/<USER>/readline/bin/"
      - "/home/<USER>/python/bin/"
      - "/home/<USER>/pcre2/bin/"
      - "/home/<USER>/openssl/bin/"
      - "/home/<USER>/oniguruma/bin/"
      - "/home/<USER>/node/bin/"
      - "/home/<USER>/make/bin/"
      - "/home/<USER>/m4/bin/"
      - "/home/<USER>/lua/bin/"
      - "/home/<USER>/llvm/bin/"
      - "/home/<USER>/libtool/bin/"
      - "/home/<USER>/libpsl/bin/"
      - "/home/<USER>/libiconv/bin/"
      - "/home/<USER>/libevent/bin/"
      - "/home/<USER>/iverilog/bin/"
      - "/home/<USER>/imlib2/bin/"
      - "/home/<USER>/graphviz/bin/"
      - "/home/<USER>/gperf/bin/"
      - "/home/<USER>/go/bin/"
      - "/home/<USER>/global/bin/"
      - "/home/<USER>/git/bin/"
      - "/home/<USER>/giflib/bin/"
      - "/home/<USER>/gettext/bin/"
      - "/home/<USER>/flatbuffers/bin/"
      - "/home/<USER>/feh/bin/"
      - "/home/<USER>/dtc/bin/"
      - "/home/<USER>/dropbear/bin/"
      - "/home/<USER>/dropbear/sbin/"
      - "/home/<USER>/doxygen/bin/"
      - "/home/<USER>/curl/bin/"
      - "/home/<USER>/ctags/bin/"
      - "/home/<USER>/cmake/bin/"
      - "/home/<USER>/bitwise/bin/"
      - "/home/<USER>/bear/bin/"
      - "/home/<USER>/automake/bin/"
      - "/home/<USER>/autoconf/bin/"
      - "/home/<USER>/local/bin/"
      - "/home/<USER>/.cargo/bin/"
      - "/trunk/go/bin/"
      - "/usr/local/bin/"
      - "/usr/bin/"
      - "/home/<USER>/.fzf/bin/"
      - "/bin/"
    searched_directories:
      - "/opt/rh/devtoolset-11/root/usr/bin/uname"
      - "/opt/synopsys/vcs/V-2023.12-SP2/bin/uname"
      - "/opt/synopsys/verdi/V-2023.12-SP2/bin/uname"
      - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli/uname"
      - "/home/<USER>/git-extras/bin/uname"
      - "/home/<USER>/zsh/bin/uname"
      - "/home/<USER>/zeromq/bin/uname"
      - "/home/<USER>/yodl/bin/uname"
      - "/home/<USER>/xsel/bin/uname"
      - "/home/<USER>/xclip/bin/uname"
      - "/home/<USER>/vim/bin/uname"
      - "/home/<USER>/verible/bin/uname"
      - "/home/<USER>/tmux/bin/uname"
      - "/home/<USER>/tcl/bin/uname"
      - "/home/<USER>/readline/bin/uname"
      - "/home/<USER>/python/bin/uname"
      - "/home/<USER>/pcre2/bin/uname"
      - "/home/<USER>/openssl/bin/uname"
      - "/home/<USER>/oniguruma/bin/uname"
      - "/home/<USER>/node/bin/uname"
      - "/home/<USER>/make/bin/uname"
      - "/home/<USER>/m4/bin/uname"
      - "/home/<USER>/lua/bin/uname"
      - "/home/<USER>/llvm/bin/uname"
      - "/home/<USER>/libtool/bin/uname"
      - "/home/<USER>/libpsl/bin/uname"
      - "/home/<USER>/libiconv/bin/uname"
      - "/home/<USER>/libevent/bin/uname"
      - "/home/<USER>/iverilog/bin/uname"
      - "/home/<USER>/imlib2/bin/uname"
      - "/home/<USER>/graphviz/bin/uname"
      - "/home/<USER>/gperf/bin/uname"
      - "/home/<USER>/go/bin/uname"
      - "/home/<USER>/global/bin/uname"
      - "/home/<USER>/git/bin/uname"
      - "/home/<USER>/giflib/bin/uname"
      - "/home/<USER>/gettext/bin/uname"
      - "/home/<USER>/flatbuffers/bin/uname"
      - "/home/<USER>/feh/bin/uname"
      - "/home/<USER>/dtc/bin/uname"
      - "/home/<USER>/dropbear/bin/uname"
      - "/home/<USER>/dropbear/sbin/uname"
      - "/home/<USER>/doxygen/bin/uname"
      - "/home/<USER>/curl/bin/uname"
      - "/home/<USER>/ctags/bin/uname"
      - "/home/<USER>/cmake/bin/uname"
      - "/home/<USER>/bitwise/bin/uname"
      - "/home/<USER>/bear/bin/uname"
      - "/home/<USER>/automake/bin/uname"
      - "/home/<USER>/autoconf/bin/uname"
      - "/home/<USER>/local/bin/uname"
      - "/home/<USER>/.cargo/bin/uname"
      - "/trunk/go/bin/uname"
      - "/usr/local/bin/uname"
    found: "/usr/bin/uname"
    search_context:
      ENV{PATH}:
        - "/opt/rh/devtoolset-11/root/usr/bin"
        - "/opt/synopsys/vcs/V-2023.12-SP2/bin"
        - "/opt/synopsys/verdi/V-2023.12-SP2/bin"
        - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli"
        - "/home/<USER>/git-extras/bin"
        - "/home/<USER>/zsh/bin"
        - "/home/<USER>/zeromq/bin"
        - "/home/<USER>/yodl/bin"
        - "/home/<USER>/xsel/bin"
        - "/home/<USER>/xclip/bin"
        - "/home/<USER>/vim/bin"
        - "/home/<USER>/verible/bin"
        - "/home/<USER>/tmux/bin"
        - "/home/<USER>/tcl/bin"
        - "/home/<USER>/readline/bin"
        - "/home/<USER>/python/bin"
        - "/home/<USER>/pcre2/bin"
        - "/home/<USER>/openssl/bin"
        - "/home/<USER>/oniguruma/bin"
        - "/home/<USER>/node/bin"
        - "/home/<USER>/make/bin"
        - "/home/<USER>/m4/bin"
        - "/home/<USER>/lua/bin"
        - "/home/<USER>/llvm/bin"
        - "/home/<USER>/libtool/bin"
        - "/home/<USER>/libpsl/bin"
        - "/home/<USER>/libiconv/bin"
        - "/home/<USER>/libevent/bin"
        - "/home/<USER>/iverilog/bin"
        - "/home/<USER>/imlib2/bin"
        - "/home/<USER>/graphviz/bin"
        - "/home/<USER>/gperf/bin"
        - "/home/<USER>/go/bin"
        - "/home/<USER>/global/bin"
        - "/home/<USER>/git/bin"
        - "/home/<USER>/giflib/bin"
        - "/home/<USER>/gettext/bin"
        - "/home/<USER>/flatbuffers/bin"
        - "/home/<USER>/feh/bin"
        - "/home/<USER>/dtc/bin"
        - "/home/<USER>/dropbear/bin"
        - "/home/<USER>/dropbear/sbin"
        - "/home/<USER>/doxygen/bin"
        - "/home/<USER>/curl/bin"
        - "/home/<USER>/ctags/bin"
        - "/home/<USER>/cmake/bin"
        - "/home/<USER>/bitwise/bin"
        - "/home/<USER>/bear/bin"
        - "/home/<USER>/automake/bin"
        - "/home/<USER>/autoconf/bin"
        - "/home/<USER>/local/bin"
        - "/home/<USER>/.cargo/bin"
        - "/trunk/go/bin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/home/<USER>/.fzf/bin"
  -
    kind: "message-v1"
    backtrace:
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt"
    message: |
      The system is: Linux - 3.10.0-1160.119.1.el7.x86_64 - x86_64
  -
    kind: "find-v1"
    backtrace:
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeUnixFindMake.cmake:5 (find_program)"
      - "CMakeLists.txt"
    mode: "program"
    variable: "CMAKE_MAKE_PROGRAM"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gmake"
      - "make"
      - "smake"
    candidate_directories:
      - "/opt/rh/devtoolset-11/root/usr/bin/"
      - "/opt/synopsys/vcs/V-2023.12-SP2/bin/"
      - "/opt/synopsys/verdi/V-2023.12-SP2/bin/"
      - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli/"
      - "/home/<USER>/git-extras/bin/"
      - "/home/<USER>/zsh/bin/"
      - "/home/<USER>/zeromq/bin/"
      - "/home/<USER>/yodl/bin/"
      - "/home/<USER>/xsel/bin/"
      - "/home/<USER>/xclip/bin/"
      - "/home/<USER>/vim/bin/"
      - "/home/<USER>/verible/bin/"
      - "/home/<USER>/tmux/bin/"
      - "/home/<USER>/tcl/bin/"
      - "/home/<USER>/readline/bin/"
      - "/home/<USER>/python/bin/"
      - "/home/<USER>/pcre2/bin/"
      - "/home/<USER>/openssl/bin/"
      - "/home/<USER>/oniguruma/bin/"
      - "/home/<USER>/node/bin/"
      - "/home/<USER>/make/bin/"
      - "/home/<USER>/m4/bin/"
      - "/home/<USER>/lua/bin/"
      - "/home/<USER>/llvm/bin/"
      - "/home/<USER>/libtool/bin/"
      - "/home/<USER>/libpsl/bin/"
      - "/home/<USER>/libiconv/bin/"
      - "/home/<USER>/libevent/bin/"
      - "/home/<USER>/iverilog/bin/"
      - "/home/<USER>/imlib2/bin/"
      - "/home/<USER>/graphviz/bin/"
      - "/home/<USER>/gperf/bin/"
      - "/home/<USER>/go/bin/"
      - "/home/<USER>/global/bin/"
      - "/home/<USER>/git/bin/"
      - "/home/<USER>/giflib/bin/"
      - "/home/<USER>/gettext/bin/"
      - "/home/<USER>/flatbuffers/bin/"
      - "/home/<USER>/feh/bin/"
      - "/home/<USER>/dtc/bin/"
      - "/home/<USER>/dropbear/bin/"
      - "/home/<USER>/dropbear/sbin/"
      - "/home/<USER>/doxygen/bin/"
      - "/home/<USER>/curl/bin/"
      - "/home/<USER>/ctags/bin/"
      - "/home/<USER>/cmake/bin/"
      - "/home/<USER>/bitwise/bin/"
      - "/home/<USER>/bear/bin/"
      - "/home/<USER>/automake/bin/"
      - "/home/<USER>/autoconf/bin/"
      - "/home/<USER>/local/bin/"
      - "/home/<USER>/.cargo/bin/"
      - "/trunk/go/bin/"
      - "/usr/local/bin/"
      - "/usr/bin/"
      - "/home/<USER>/.fzf/bin/"
    searched_directories:
      - "/opt/rh/devtoolset-11/root/usr/bin/gmake"
      - "/opt/synopsys/vcs/V-2023.12-SP2/bin/gmake"
      - "/opt/synopsys/verdi/V-2023.12-SP2/bin/gmake"
      - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli/gmake"
      - "/home/<USER>/git-extras/bin/gmake"
      - "/home/<USER>/zsh/bin/gmake"
      - "/home/<USER>/zeromq/bin/gmake"
      - "/home/<USER>/yodl/bin/gmake"
      - "/home/<USER>/xsel/bin/gmake"
      - "/home/<USER>/xclip/bin/gmake"
      - "/home/<USER>/vim/bin/gmake"
      - "/home/<USER>/verible/bin/gmake"
      - "/home/<USER>/tmux/bin/gmake"
      - "/home/<USER>/tcl/bin/gmake"
      - "/home/<USER>/readline/bin/gmake"
      - "/home/<USER>/python/bin/gmake"
      - "/home/<USER>/pcre2/bin/gmake"
      - "/home/<USER>/openssl/bin/gmake"
      - "/home/<USER>/oniguruma/bin/gmake"
      - "/home/<USER>/node/bin/gmake"
      - "/home/<USER>/make/bin/gmake"
      - "/home/<USER>/m4/bin/gmake"
      - "/home/<USER>/lua/bin/gmake"
      - "/home/<USER>/llvm/bin/gmake"
      - "/home/<USER>/libtool/bin/gmake"
      - "/home/<USER>/libpsl/bin/gmake"
      - "/home/<USER>/libiconv/bin/gmake"
      - "/home/<USER>/libevent/bin/gmake"
      - "/home/<USER>/iverilog/bin/gmake"
      - "/home/<USER>/imlib2/bin/gmake"
      - "/home/<USER>/graphviz/bin/gmake"
      - "/home/<USER>/gperf/bin/gmake"
      - "/home/<USER>/go/bin/gmake"
      - "/home/<USER>/global/bin/gmake"
      - "/home/<USER>/git/bin/gmake"
      - "/home/<USER>/giflib/bin/gmake"
      - "/home/<USER>/gettext/bin/gmake"
      - "/home/<USER>/flatbuffers/bin/gmake"
      - "/home/<USER>/feh/bin/gmake"
      - "/home/<USER>/dtc/bin/gmake"
      - "/home/<USER>/dropbear/bin/gmake"
      - "/home/<USER>/dropbear/sbin/gmake"
      - "/home/<USER>/doxygen/bin/gmake"
      - "/home/<USER>/curl/bin/gmake"
      - "/home/<USER>/ctags/bin/gmake"
      - "/home/<USER>/cmake/bin/gmake"
      - "/home/<USER>/bitwise/bin/gmake"
      - "/home/<USER>/bear/bin/gmake"
      - "/home/<USER>/automake/bin/gmake"
      - "/home/<USER>/autoconf/bin/gmake"
      - "/home/<USER>/local/bin/gmake"
      - "/home/<USER>/.cargo/bin/gmake"
      - "/trunk/go/bin/gmake"
      - "/usr/local/bin/gmake"
    found: "/usr/bin/gmake"
    search_context:
      ENV{PATH}:
        - "/opt/rh/devtoolset-11/root/usr/bin"
        - "/opt/synopsys/vcs/V-2023.12-SP2/bin"
        - "/opt/synopsys/verdi/V-2023.12-SP2/bin"
        - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli"
        - "/home/<USER>/git-extras/bin"
        - "/home/<USER>/zsh/bin"
        - "/home/<USER>/zeromq/bin"
        - "/home/<USER>/yodl/bin"
        - "/home/<USER>/xsel/bin"
        - "/home/<USER>/xclip/bin"
        - "/home/<USER>/vim/bin"
        - "/home/<USER>/verible/bin"
        - "/home/<USER>/tmux/bin"
        - "/home/<USER>/tcl/bin"
        - "/home/<USER>/readline/bin"
        - "/home/<USER>/python/bin"
        - "/home/<USER>/pcre2/bin"
        - "/home/<USER>/openssl/bin"
        - "/home/<USER>/oniguruma/bin"
        - "/home/<USER>/node/bin"
        - "/home/<USER>/make/bin"
        - "/home/<USER>/m4/bin"
        - "/home/<USER>/lua/bin"
        - "/home/<USER>/llvm/bin"
        - "/home/<USER>/libtool/bin"
        - "/home/<USER>/libpsl/bin"
        - "/home/<USER>/libiconv/bin"
        - "/home/<USER>/libevent/bin"
        - "/home/<USER>/iverilog/bin"
        - "/home/<USER>/imlib2/bin"
        - "/home/<USER>/graphviz/bin"
        - "/home/<USER>/gperf/bin"
        - "/home/<USER>/go/bin"
        - "/home/<USER>/global/bin"
        - "/home/<USER>/git/bin"
        - "/home/<USER>/giflib/bin"
        - "/home/<USER>/gettext/bin"
        - "/home/<USER>/flatbuffers/bin"
        - "/home/<USER>/feh/bin"
        - "/home/<USER>/dtc/bin"
        - "/home/<USER>/dropbear/bin"
        - "/home/<USER>/dropbear/sbin"
        - "/home/<USER>/doxygen/bin"
        - "/home/<USER>/curl/bin"
        - "/home/<USER>/ctags/bin"
        - "/home/<USER>/cmake/bin"
        - "/home/<USER>/bitwise/bin"
        - "/home/<USER>/bear/bin"
        - "/home/<USER>/automake/bin"
        - "/home/<USER>/autoconf/bin"
        - "/home/<USER>/local/bin"
        - "/home/<USER>/.cargo/bin"
        - "/trunk/go/bin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/home/<USER>/.fzf/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCompiler.cmake:73 (find_program)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCCompiler.cmake:64 (_cmake_find_compiler)"
      - "CMakeLists.txt"
    mode: "program"
    variable: "CMAKE_C_COMPILER"
    description: "C compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "cc"
      - "gcc"
      - "cl"
      - "bcc"
      - "xlc"
      - "icx"
      - "clang"
    candidate_directories:
      - "/opt/rh/devtoolset-11/root/usr/bin/"
      - "/opt/synopsys/vcs/V-2023.12-SP2/bin/"
      - "/opt/synopsys/verdi/V-2023.12-SP2/bin/"
      - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli/"
      - "/home/<USER>/git-extras/bin/"
      - "/home/<USER>/zsh/bin/"
      - "/home/<USER>/zeromq/bin/"
      - "/home/<USER>/yodl/bin/"
      - "/home/<USER>/xsel/bin/"
      - "/home/<USER>/xclip/bin/"
      - "/home/<USER>/vim/bin/"
      - "/home/<USER>/verible/bin/"
      - "/home/<USER>/tmux/bin/"
      - "/home/<USER>/tcl/bin/"
      - "/home/<USER>/readline/bin/"
      - "/home/<USER>/python/bin/"
      - "/home/<USER>/pcre2/bin/"
      - "/home/<USER>/openssl/bin/"
      - "/home/<USER>/oniguruma/bin/"
      - "/home/<USER>/node/bin/"
      - "/home/<USER>/make/bin/"
      - "/home/<USER>/m4/bin/"
      - "/home/<USER>/lua/bin/"
      - "/home/<USER>/llvm/bin/"
      - "/home/<USER>/libtool/bin/"
      - "/home/<USER>/libpsl/bin/"
      - "/home/<USER>/libiconv/bin/"
      - "/home/<USER>/libevent/bin/"
      - "/home/<USER>/iverilog/bin/"
      - "/home/<USER>/imlib2/bin/"
      - "/home/<USER>/graphviz/bin/"
      - "/home/<USER>/gperf/bin/"
      - "/home/<USER>/go/bin/"
      - "/home/<USER>/global/bin/"
      - "/home/<USER>/git/bin/"
      - "/home/<USER>/giflib/bin/"
      - "/home/<USER>/gettext/bin/"
      - "/home/<USER>/flatbuffers/bin/"
      - "/home/<USER>/feh/bin/"
      - "/home/<USER>/dtc/bin/"
      - "/home/<USER>/dropbear/bin/"
      - "/home/<USER>/dropbear/sbin/"
      - "/home/<USER>/doxygen/bin/"
      - "/home/<USER>/curl/bin/"
      - "/home/<USER>/ctags/bin/"
      - "/home/<USER>/cmake/bin/"
      - "/home/<USER>/bitwise/bin/"
      - "/home/<USER>/bear/bin/"
      - "/home/<USER>/automake/bin/"
      - "/home/<USER>/autoconf/bin/"
      - "/home/<USER>/local/bin/"
      - "/home/<USER>/.cargo/bin/"
      - "/trunk/go/bin/"
      - "/usr/local/bin/"
      - "/usr/bin/"
      - "/home/<USER>/.fzf/bin/"
    found: "/opt/rh/devtoolset-11/root/usr/bin/cc"
    search_context:
      ENV{PATH}:
        - "/opt/rh/devtoolset-11/root/usr/bin"
        - "/opt/synopsys/vcs/V-2023.12-SP2/bin"
        - "/opt/synopsys/verdi/V-2023.12-SP2/bin"
        - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli"
        - "/home/<USER>/git-extras/bin"
        - "/home/<USER>/zsh/bin"
        - "/home/<USER>/zeromq/bin"
        - "/home/<USER>/yodl/bin"
        - "/home/<USER>/xsel/bin"
        - "/home/<USER>/xclip/bin"
        - "/home/<USER>/vim/bin"
        - "/home/<USER>/verible/bin"
        - "/home/<USER>/tmux/bin"
        - "/home/<USER>/tcl/bin"
        - "/home/<USER>/readline/bin"
        - "/home/<USER>/python/bin"
        - "/home/<USER>/pcre2/bin"
        - "/home/<USER>/openssl/bin"
        - "/home/<USER>/oniguruma/bin"
        - "/home/<USER>/node/bin"
        - "/home/<USER>/make/bin"
        - "/home/<USER>/m4/bin"
        - "/home/<USER>/lua/bin"
        - "/home/<USER>/llvm/bin"
        - "/home/<USER>/libtool/bin"
        - "/home/<USER>/libpsl/bin"
        - "/home/<USER>/libiconv/bin"
        - "/home/<USER>/libevent/bin"
        - "/home/<USER>/iverilog/bin"
        - "/home/<USER>/imlib2/bin"
        - "/home/<USER>/graphviz/bin"
        - "/home/<USER>/gperf/bin"
        - "/home/<USER>/go/bin"
        - "/home/<USER>/global/bin"
        - "/home/<USER>/git/bin"
        - "/home/<USER>/giflib/bin"
        - "/home/<USER>/gettext/bin"
        - "/home/<USER>/flatbuffers/bin"
        - "/home/<USER>/feh/bin"
        - "/home/<USER>/dtc/bin"
        - "/home/<USER>/dropbear/bin"
        - "/home/<USER>/dropbear/sbin"
        - "/home/<USER>/doxygen/bin"
        - "/home/<USER>/curl/bin"
        - "/home/<USER>/ctags/bin"
        - "/home/<USER>/cmake/bin"
        - "/home/<USER>/bitwise/bin"
        - "/home/<USER>/bear/bin"
        - "/home/<USER>/automake/bin"
        - "/home/<USER>/autoconf/bin"
        - "/home/<USER>/local/bin"
        - "/home/<USER>/.cargo/bin"
        - "/trunk/go/bin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/home/<USER>/.fzf/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCCompilerId.c.in"
    candidate_directories:
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/"
    found: "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeCCompilerId.c.in"
    search_context:
      ENV{PATH}:
        - "/opt/rh/devtoolset-11/root/usr/bin"
        - "/opt/synopsys/vcs/V-2023.12-SP2/bin"
        - "/opt/synopsys/verdi/V-2023.12-SP2/bin"
        - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli"
        - "/home/<USER>/git-extras/bin"
        - "/home/<USER>/zsh/bin"
        - "/home/<USER>/zeromq/bin"
        - "/home/<USER>/yodl/bin"
        - "/home/<USER>/xsel/bin"
        - "/home/<USER>/xclip/bin"
        - "/home/<USER>/vim/bin"
        - "/home/<USER>/verible/bin"
        - "/home/<USER>/tmux/bin"
        - "/home/<USER>/tcl/bin"
        - "/home/<USER>/readline/bin"
        - "/home/<USER>/python/bin"
        - "/home/<USER>/pcre2/bin"
        - "/home/<USER>/openssl/bin"
        - "/home/<USER>/oniguruma/bin"
        - "/home/<USER>/node/bin"
        - "/home/<USER>/make/bin"
        - "/home/<USER>/m4/bin"
        - "/home/<USER>/lua/bin"
        - "/home/<USER>/llvm/bin"
        - "/home/<USER>/libtool/bin"
        - "/home/<USER>/libpsl/bin"
        - "/home/<USER>/libiconv/bin"
        - "/home/<USER>/libevent/bin"
        - "/home/<USER>/iverilog/bin"
        - "/home/<USER>/imlib2/bin"
        - "/home/<USER>/graphviz/bin"
        - "/home/<USER>/gperf/bin"
        - "/home/<USER>/go/bin"
        - "/home/<USER>/global/bin"
        - "/home/<USER>/git/bin"
        - "/home/<USER>/giflib/bin"
        - "/home/<USER>/gettext/bin"
        - "/home/<USER>/flatbuffers/bin"
        - "/home/<USER>/feh/bin"
        - "/home/<USER>/dtc/bin"
        - "/home/<USER>/dropbear/bin"
        - "/home/<USER>/dropbear/sbin"
        - "/home/<USER>/doxygen/bin"
        - "/home/<USER>/curl/bin"
        - "/home/<USER>/ctags/bin"
        - "/home/<USER>/cmake/bin"
        - "/home/<USER>/bitwise/bin"
        - "/home/<USER>/bear/bin"
        - "/home/<USER>/automake/bin"
        - "/home/<USER>/autoconf/bin"
        - "/home/<USER>/local/bin"
        - "/home/<USER>/.cargo/bin"
        - "/trunk/go/bin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/home/<USER>/.fzf/bin"
  -
    kind: "message-v1"
    backtrace:
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: /opt/rh/devtoolset-11/root/usr/bin/cc 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.out"
      
      The C compiler identification is GNU, found in:
        /trunk/hj/hybrid_kit_shm-enflame-0311/src/sideband/bridge/build/CMakeFiles/4.0.20250612-git/CompilerIdC/a.out
      
  -
    kind: "find-v1"
    backtrace:
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt"
    mode: "program"
    variable: "CMAKE_AR"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ar"
    candidate_directories:
      - "/opt/rh/devtoolset-11/root/usr/bin/"
      - "/opt/synopsys/vcs/V-2023.12-SP2/bin/"
      - "/opt/synopsys/verdi/V-2023.12-SP2/bin/"
      - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli/"
      - "/home/<USER>/git-extras/bin/"
      - "/home/<USER>/zsh/bin/"
      - "/home/<USER>/zeromq/bin/"
      - "/home/<USER>/yodl/bin/"
      - "/home/<USER>/xsel/bin/"
      - "/home/<USER>/xclip/bin/"
      - "/home/<USER>/vim/bin/"
      - "/home/<USER>/verible/bin/"
      - "/home/<USER>/tmux/bin/"
      - "/home/<USER>/tcl/bin/"
      - "/home/<USER>/readline/bin/"
      - "/home/<USER>/python/bin/"
      - "/home/<USER>/pcre2/bin/"
      - "/home/<USER>/openssl/bin/"
      - "/home/<USER>/oniguruma/bin/"
      - "/home/<USER>/node/bin/"
      - "/home/<USER>/make/bin/"
      - "/home/<USER>/m4/bin/"
      - "/home/<USER>/lua/bin/"
      - "/home/<USER>/llvm/bin/"
      - "/home/<USER>/libtool/bin/"
      - "/home/<USER>/libpsl/bin/"
      - "/home/<USER>/libiconv/bin/"
      - "/home/<USER>/libevent/bin/"
      - "/home/<USER>/iverilog/bin/"
      - "/home/<USER>/imlib2/bin/"
      - "/home/<USER>/graphviz/bin/"
      - "/home/<USER>/gperf/bin/"
      - "/home/<USER>/go/bin/"
      - "/home/<USER>/global/bin/"
      - "/home/<USER>/git/bin/"
      - "/home/<USER>/giflib/bin/"
      - "/home/<USER>/gettext/bin/"
      - "/home/<USER>/flatbuffers/bin/"
      - "/home/<USER>/feh/bin/"
      - "/home/<USER>/dtc/bin/"
      - "/home/<USER>/dropbear/bin/"
      - "/home/<USER>/dropbear/sbin/"
      - "/home/<USER>/doxygen/bin/"
      - "/home/<USER>/curl/bin/"
      - "/home/<USER>/ctags/bin/"
      - "/home/<USER>/cmake/bin/"
      - "/home/<USER>/bitwise/bin/"
      - "/home/<USER>/bear/bin/"
      - "/home/<USER>/automake/bin/"
      - "/home/<USER>/autoconf/bin/"
      - "/home/<USER>/local/bin/"
      - "/home/<USER>/.cargo/bin/"
      - "/trunk/go/bin/"
      - "/usr/local/bin/"
      - "/usr/bin/"
      - "/home/<USER>/.fzf/bin/"
    found: "/opt/rh/devtoolset-11/root/usr/bin/ar"
    search_context:
      ENV{PATH}:
        - "/opt/rh/devtoolset-11/root/usr/bin"
        - "/opt/synopsys/vcs/V-2023.12-SP2/bin"
        - "/opt/synopsys/verdi/V-2023.12-SP2/bin"
        - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli"
        - "/home/<USER>/git-extras/bin"
        - "/home/<USER>/zsh/bin"
        - "/home/<USER>/zeromq/bin"
        - "/home/<USER>/yodl/bin"
        - "/home/<USER>/xsel/bin"
        - "/home/<USER>/xclip/bin"
        - "/home/<USER>/vim/bin"
        - "/home/<USER>/verible/bin"
        - "/home/<USER>/tmux/bin"
        - "/home/<USER>/tcl/bin"
        - "/home/<USER>/readline/bin"
        - "/home/<USER>/python/bin"
        - "/home/<USER>/pcre2/bin"
        - "/home/<USER>/openssl/bin"
        - "/home/<USER>/oniguruma/bin"
        - "/home/<USER>/node/bin"
        - "/home/<USER>/make/bin"
        - "/home/<USER>/m4/bin"
        - "/home/<USER>/lua/bin"
        - "/home/<USER>/llvm/bin"
        - "/home/<USER>/libtool/bin"
        - "/home/<USER>/libpsl/bin"
        - "/home/<USER>/libiconv/bin"
        - "/home/<USER>/libevent/bin"
        - "/home/<USER>/iverilog/bin"
        - "/home/<USER>/imlib2/bin"
        - "/home/<USER>/graphviz/bin"
        - "/home/<USER>/gperf/bin"
        - "/home/<USER>/go/bin"
        - "/home/<USER>/global/bin"
        - "/home/<USER>/git/bin"
        - "/home/<USER>/giflib/bin"
        - "/home/<USER>/gettext/bin"
        - "/home/<USER>/flatbuffers/bin"
        - "/home/<USER>/feh/bin"
        - "/home/<USER>/dtc/bin"
        - "/home/<USER>/dropbear/bin"
        - "/home/<USER>/dropbear/sbin"
        - "/home/<USER>/doxygen/bin"
        - "/home/<USER>/curl/bin"
        - "/home/<USER>/ctags/bin"
        - "/home/<USER>/cmake/bin"
        - "/home/<USER>/bitwise/bin"
        - "/home/<USER>/bear/bin"
        - "/home/<USER>/automake/bin"
        - "/home/<USER>/autoconf/bin"
        - "/home/<USER>/local/bin"
        - "/home/<USER>/.cargo/bin"
        - "/trunk/go/bin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/home/<USER>/.fzf/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt"
    mode: "program"
    variable: "CMAKE_RANLIB"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ranlib"
    candidate_directories:
      - "/opt/rh/devtoolset-11/root/usr/bin/"
      - "/opt/synopsys/vcs/V-2023.12-SP2/bin/"
      - "/opt/synopsys/verdi/V-2023.12-SP2/bin/"
      - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli/"
      - "/home/<USER>/git-extras/bin/"
      - "/home/<USER>/zsh/bin/"
      - "/home/<USER>/zeromq/bin/"
      - "/home/<USER>/yodl/bin/"
      - "/home/<USER>/xsel/bin/"
      - "/home/<USER>/xclip/bin/"
      - "/home/<USER>/vim/bin/"
      - "/home/<USER>/verible/bin/"
      - "/home/<USER>/tmux/bin/"
      - "/home/<USER>/tcl/bin/"
      - "/home/<USER>/readline/bin/"
      - "/home/<USER>/python/bin/"
      - "/home/<USER>/pcre2/bin/"
      - "/home/<USER>/openssl/bin/"
      - "/home/<USER>/oniguruma/bin/"
      - "/home/<USER>/node/bin/"
      - "/home/<USER>/make/bin/"
      - "/home/<USER>/m4/bin/"
      - "/home/<USER>/lua/bin/"
      - "/home/<USER>/llvm/bin/"
      - "/home/<USER>/libtool/bin/"
      - "/home/<USER>/libpsl/bin/"
      - "/home/<USER>/libiconv/bin/"
      - "/home/<USER>/libevent/bin/"
      - "/home/<USER>/iverilog/bin/"
      - "/home/<USER>/imlib2/bin/"
      - "/home/<USER>/graphviz/bin/"
      - "/home/<USER>/gperf/bin/"
      - "/home/<USER>/go/bin/"
      - "/home/<USER>/global/bin/"
      - "/home/<USER>/git/bin/"
      - "/home/<USER>/giflib/bin/"
      - "/home/<USER>/gettext/bin/"
      - "/home/<USER>/flatbuffers/bin/"
      - "/home/<USER>/feh/bin/"
      - "/home/<USER>/dtc/bin/"
      - "/home/<USER>/dropbear/bin/"
      - "/home/<USER>/dropbear/sbin/"
      - "/home/<USER>/doxygen/bin/"
      - "/home/<USER>/curl/bin/"
      - "/home/<USER>/ctags/bin/"
      - "/home/<USER>/cmake/bin/"
      - "/home/<USER>/bitwise/bin/"
      - "/home/<USER>/bear/bin/"
      - "/home/<USER>/automake/bin/"
      - "/home/<USER>/autoconf/bin/"
      - "/home/<USER>/local/bin/"
      - "/home/<USER>/.cargo/bin/"
      - "/trunk/go/bin/"
      - "/usr/local/bin/"
      - "/usr/bin/"
      - "/home/<USER>/.fzf/bin/"
    found: "/opt/rh/devtoolset-11/root/usr/bin/ranlib"
    search_context:
      ENV{PATH}:
        - "/opt/rh/devtoolset-11/root/usr/bin"
        - "/opt/synopsys/vcs/V-2023.12-SP2/bin"
        - "/opt/synopsys/verdi/V-2023.12-SP2/bin"
        - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli"
        - "/home/<USER>/git-extras/bin"
        - "/home/<USER>/zsh/bin"
        - "/home/<USER>/zeromq/bin"
        - "/home/<USER>/yodl/bin"
        - "/home/<USER>/xsel/bin"
        - "/home/<USER>/xclip/bin"
        - "/home/<USER>/vim/bin"
        - "/home/<USER>/verible/bin"
        - "/home/<USER>/tmux/bin"
        - "/home/<USER>/tcl/bin"
        - "/home/<USER>/readline/bin"
        - "/home/<USER>/python/bin"
        - "/home/<USER>/pcre2/bin"
        - "/home/<USER>/openssl/bin"
        - "/home/<USER>/oniguruma/bin"
        - "/home/<USER>/node/bin"
        - "/home/<USER>/make/bin"
        - "/home/<USER>/m4/bin"
        - "/home/<USER>/lua/bin"
        - "/home/<USER>/llvm/bin"
        - "/home/<USER>/libtool/bin"
        - "/home/<USER>/libpsl/bin"
        - "/home/<USER>/libiconv/bin"
        - "/home/<USER>/libevent/bin"
        - "/home/<USER>/iverilog/bin"
        - "/home/<USER>/imlib2/bin"
        - "/home/<USER>/graphviz/bin"
        - "/home/<USER>/gperf/bin"
        - "/home/<USER>/go/bin"
        - "/home/<USER>/global/bin"
        - "/home/<USER>/git/bin"
        - "/home/<USER>/giflib/bin"
        - "/home/<USER>/gettext/bin"
        - "/home/<USER>/flatbuffers/bin"
        - "/home/<USER>/feh/bin"
        - "/home/<USER>/dtc/bin"
        - "/home/<USER>/dropbear/bin"
        - "/home/<USER>/dropbear/sbin"
        - "/home/<USER>/doxygen/bin"
        - "/home/<USER>/curl/bin"
        - "/home/<USER>/ctags/bin"
        - "/home/<USER>/cmake/bin"
        - "/home/<USER>/bitwise/bin"
        - "/home/<USER>/bear/bin"
        - "/home/<USER>/automake/bin"
        - "/home/<USER>/autoconf/bin"
        - "/home/<USER>/local/bin"
        - "/home/<USER>/.cargo/bin"
        - "/trunk/go/bin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/home/<USER>/.fzf/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt"
    mode: "program"
    variable: "CMAKE_STRIP"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "strip"
    candidate_directories:
      - "/opt/rh/devtoolset-11/root/usr/bin/"
      - "/opt/synopsys/vcs/V-2023.12-SP2/bin/"
      - "/opt/synopsys/verdi/V-2023.12-SP2/bin/"
      - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli/"
      - "/home/<USER>/git-extras/bin/"
      - "/home/<USER>/zsh/bin/"
      - "/home/<USER>/zeromq/bin/"
      - "/home/<USER>/yodl/bin/"
      - "/home/<USER>/xsel/bin/"
      - "/home/<USER>/xclip/bin/"
      - "/home/<USER>/vim/bin/"
      - "/home/<USER>/verible/bin/"
      - "/home/<USER>/tmux/bin/"
      - "/home/<USER>/tcl/bin/"
      - "/home/<USER>/readline/bin/"
      - "/home/<USER>/python/bin/"
      - "/home/<USER>/pcre2/bin/"
      - "/home/<USER>/openssl/bin/"
      - "/home/<USER>/oniguruma/bin/"
      - "/home/<USER>/node/bin/"
      - "/home/<USER>/make/bin/"
      - "/home/<USER>/m4/bin/"
      - "/home/<USER>/lua/bin/"
      - "/home/<USER>/llvm/bin/"
      - "/home/<USER>/libtool/bin/"
      - "/home/<USER>/libpsl/bin/"
      - "/home/<USER>/libiconv/bin/"
      - "/home/<USER>/libevent/bin/"
      - "/home/<USER>/iverilog/bin/"
      - "/home/<USER>/imlib2/bin/"
      - "/home/<USER>/graphviz/bin/"
      - "/home/<USER>/gperf/bin/"
      - "/home/<USER>/go/bin/"
      - "/home/<USER>/global/bin/"
      - "/home/<USER>/git/bin/"
      - "/home/<USER>/giflib/bin/"
      - "/home/<USER>/gettext/bin/"
      - "/home/<USER>/flatbuffers/bin/"
      - "/home/<USER>/feh/bin/"
      - "/home/<USER>/dtc/bin/"
      - "/home/<USER>/dropbear/bin/"
      - "/home/<USER>/dropbear/sbin/"
      - "/home/<USER>/doxygen/bin/"
      - "/home/<USER>/curl/bin/"
      - "/home/<USER>/ctags/bin/"
      - "/home/<USER>/cmake/bin/"
      - "/home/<USER>/bitwise/bin/"
      - "/home/<USER>/bear/bin/"
      - "/home/<USER>/automake/bin/"
      - "/home/<USER>/autoconf/bin/"
      - "/home/<USER>/local/bin/"
      - "/home/<USER>/.cargo/bin/"
      - "/trunk/go/bin/"
      - "/usr/local/bin/"
      - "/usr/bin/"
      - "/home/<USER>/.fzf/bin/"
    found: "/opt/rh/devtoolset-11/root/usr/bin/strip"
    search_context:
      ENV{PATH}:
        - "/opt/rh/devtoolset-11/root/usr/bin"
        - "/opt/synopsys/vcs/V-2023.12-SP2/bin"
        - "/opt/synopsys/verdi/V-2023.12-SP2/bin"
        - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli"
        - "/home/<USER>/git-extras/bin"
        - "/home/<USER>/zsh/bin"
        - "/home/<USER>/zeromq/bin"
        - "/home/<USER>/yodl/bin"
        - "/home/<USER>/xsel/bin"
        - "/home/<USER>/xclip/bin"
        - "/home/<USER>/vim/bin"
        - "/home/<USER>/verible/bin"
        - "/home/<USER>/tmux/bin"
        - "/home/<USER>/tcl/bin"
        - "/home/<USER>/readline/bin"
        - "/home/<USER>/python/bin"
        - "/home/<USER>/pcre2/bin"
        - "/home/<USER>/openssl/bin"
        - "/home/<USER>/oniguruma/bin"
        - "/home/<USER>/node/bin"
        - "/home/<USER>/make/bin"
        - "/home/<USER>/m4/bin"
        - "/home/<USER>/lua/bin"
        - "/home/<USER>/llvm/bin"
        - "/home/<USER>/libtool/bin"
        - "/home/<USER>/libpsl/bin"
        - "/home/<USER>/libiconv/bin"
        - "/home/<USER>/libevent/bin"
        - "/home/<USER>/iverilog/bin"
        - "/home/<USER>/imlib2/bin"
        - "/home/<USER>/graphviz/bin"
        - "/home/<USER>/gperf/bin"
        - "/home/<USER>/go/bin"
        - "/home/<USER>/global/bin"
        - "/home/<USER>/git/bin"
        - "/home/<USER>/giflib/bin"
        - "/home/<USER>/gettext/bin"
        - "/home/<USER>/flatbuffers/bin"
        - "/home/<USER>/feh/bin"
        - "/home/<USER>/dtc/bin"
        - "/home/<USER>/dropbear/bin"
        - "/home/<USER>/dropbear/sbin"
        - "/home/<USER>/doxygen/bin"
        - "/home/<USER>/curl/bin"
        - "/home/<USER>/ctags/bin"
        - "/home/<USER>/cmake/bin"
        - "/home/<USER>/bitwise/bin"
        - "/home/<USER>/bear/bin"
        - "/home/<USER>/automake/bin"
        - "/home/<USER>/autoconf/bin"
        - "/home/<USER>/local/bin"
        - "/home/<USER>/.cargo/bin"
        - "/trunk/go/bin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/home/<USER>/.fzf/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt"
    mode: "program"
    variable: "CMAKE_LINKER"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ld"
    candidate_directories:
      - "/opt/rh/devtoolset-11/root/usr/bin/"
      - "/opt/synopsys/vcs/V-2023.12-SP2/bin/"
      - "/opt/synopsys/verdi/V-2023.12-SP2/bin/"
      - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli/"
      - "/home/<USER>/git-extras/bin/"
      - "/home/<USER>/zsh/bin/"
      - "/home/<USER>/zeromq/bin/"
      - "/home/<USER>/yodl/bin/"
      - "/home/<USER>/xsel/bin/"
      - "/home/<USER>/xclip/bin/"
      - "/home/<USER>/vim/bin/"
      - "/home/<USER>/verible/bin/"
      - "/home/<USER>/tmux/bin/"
      - "/home/<USER>/tcl/bin/"
      - "/home/<USER>/readline/bin/"
      - "/home/<USER>/python/bin/"
      - "/home/<USER>/pcre2/bin/"
      - "/home/<USER>/openssl/bin/"
      - "/home/<USER>/oniguruma/bin/"
      - "/home/<USER>/node/bin/"
      - "/home/<USER>/make/bin/"
      - "/home/<USER>/m4/bin/"
      - "/home/<USER>/lua/bin/"
      - "/home/<USER>/llvm/bin/"
      - "/home/<USER>/libtool/bin/"
      - "/home/<USER>/libpsl/bin/"
      - "/home/<USER>/libiconv/bin/"
      - "/home/<USER>/libevent/bin/"
      - "/home/<USER>/iverilog/bin/"
      - "/home/<USER>/imlib2/bin/"
      - "/home/<USER>/graphviz/bin/"
      - "/home/<USER>/gperf/bin/"
      - "/home/<USER>/go/bin/"
      - "/home/<USER>/global/bin/"
      - "/home/<USER>/git/bin/"
      - "/home/<USER>/giflib/bin/"
      - "/home/<USER>/gettext/bin/"
      - "/home/<USER>/flatbuffers/bin/"
      - "/home/<USER>/feh/bin/"
      - "/home/<USER>/dtc/bin/"
      - "/home/<USER>/dropbear/bin/"
      - "/home/<USER>/dropbear/sbin/"
      - "/home/<USER>/doxygen/bin/"
      - "/home/<USER>/curl/bin/"
      - "/home/<USER>/ctags/bin/"
      - "/home/<USER>/cmake/bin/"
      - "/home/<USER>/bitwise/bin/"
      - "/home/<USER>/bear/bin/"
      - "/home/<USER>/automake/bin/"
      - "/home/<USER>/autoconf/bin/"
      - "/home/<USER>/local/bin/"
      - "/home/<USER>/.cargo/bin/"
      - "/trunk/go/bin/"
      - "/usr/local/bin/"
      - "/usr/bin/"
      - "/home/<USER>/.fzf/bin/"
    found: "/opt/rh/devtoolset-11/root/usr/bin/ld"
    search_context:
      ENV{PATH}:
        - "/opt/rh/devtoolset-11/root/usr/bin"
        - "/opt/synopsys/vcs/V-2023.12-SP2/bin"
        - "/opt/synopsys/verdi/V-2023.12-SP2/bin"
        - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli"
        - "/home/<USER>/git-extras/bin"
        - "/home/<USER>/zsh/bin"
        - "/home/<USER>/zeromq/bin"
        - "/home/<USER>/yodl/bin"
        - "/home/<USER>/xsel/bin"
        - "/home/<USER>/xclip/bin"
        - "/home/<USER>/vim/bin"
        - "/home/<USER>/verible/bin"
        - "/home/<USER>/tmux/bin"
        - "/home/<USER>/tcl/bin"
        - "/home/<USER>/readline/bin"
        - "/home/<USER>/python/bin"
        - "/home/<USER>/pcre2/bin"
        - "/home/<USER>/openssl/bin"
        - "/home/<USER>/oniguruma/bin"
        - "/home/<USER>/node/bin"
        - "/home/<USER>/make/bin"
        - "/home/<USER>/m4/bin"
        - "/home/<USER>/lua/bin"
        - "/home/<USER>/llvm/bin"
        - "/home/<USER>/libtool/bin"
        - "/home/<USER>/libpsl/bin"
        - "/home/<USER>/libiconv/bin"
        - "/home/<USER>/libevent/bin"
        - "/home/<USER>/iverilog/bin"
        - "/home/<USER>/imlib2/bin"
        - "/home/<USER>/graphviz/bin"
        - "/home/<USER>/gperf/bin"
        - "/home/<USER>/go/bin"
        - "/home/<USER>/global/bin"
        - "/home/<USER>/git/bin"
        - "/home/<USER>/giflib/bin"
        - "/home/<USER>/gettext/bin"
        - "/home/<USER>/flatbuffers/bin"
        - "/home/<USER>/feh/bin"
        - "/home/<USER>/dtc/bin"
        - "/home/<USER>/dropbear/bin"
        - "/home/<USER>/dropbear/sbin"
        - "/home/<USER>/doxygen/bin"
        - "/home/<USER>/curl/bin"
        - "/home/<USER>/ctags/bin"
        - "/home/<USER>/cmake/bin"
        - "/home/<USER>/bitwise/bin"
        - "/home/<USER>/bear/bin"
        - "/home/<USER>/automake/bin"
        - "/home/<USER>/autoconf/bin"
        - "/home/<USER>/local/bin"
        - "/home/<USER>/.cargo/bin"
        - "/trunk/go/bin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/home/<USER>/.fzf/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt"
    mode: "program"
    variable: "CMAKE_NM"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "nm"
    candidate_directories:
      - "/opt/rh/devtoolset-11/root/usr/bin/"
      - "/opt/synopsys/vcs/V-2023.12-SP2/bin/"
      - "/opt/synopsys/verdi/V-2023.12-SP2/bin/"
      - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli/"
      - "/home/<USER>/git-extras/bin/"
      - "/home/<USER>/zsh/bin/"
      - "/home/<USER>/zeromq/bin/"
      - "/home/<USER>/yodl/bin/"
      - "/home/<USER>/xsel/bin/"
      - "/home/<USER>/xclip/bin/"
      - "/home/<USER>/vim/bin/"
      - "/home/<USER>/verible/bin/"
      - "/home/<USER>/tmux/bin/"
      - "/home/<USER>/tcl/bin/"
      - "/home/<USER>/readline/bin/"
      - "/home/<USER>/python/bin/"
      - "/home/<USER>/pcre2/bin/"
      - "/home/<USER>/openssl/bin/"
      - "/home/<USER>/oniguruma/bin/"
      - "/home/<USER>/node/bin/"
      - "/home/<USER>/make/bin/"
      - "/home/<USER>/m4/bin/"
      - "/home/<USER>/lua/bin/"
      - "/home/<USER>/llvm/bin/"
      - "/home/<USER>/libtool/bin/"
      - "/home/<USER>/libpsl/bin/"
      - "/home/<USER>/libiconv/bin/"
      - "/home/<USER>/libevent/bin/"
      - "/home/<USER>/iverilog/bin/"
      - "/home/<USER>/imlib2/bin/"
      - "/home/<USER>/graphviz/bin/"
      - "/home/<USER>/gperf/bin/"
      - "/home/<USER>/go/bin/"
      - "/home/<USER>/global/bin/"
      - "/home/<USER>/git/bin/"
      - "/home/<USER>/giflib/bin/"
      - "/home/<USER>/gettext/bin/"
      - "/home/<USER>/flatbuffers/bin/"
      - "/home/<USER>/feh/bin/"
      - "/home/<USER>/dtc/bin/"
      - "/home/<USER>/dropbear/bin/"
      - "/home/<USER>/dropbear/sbin/"
      - "/home/<USER>/doxygen/bin/"
      - "/home/<USER>/curl/bin/"
      - "/home/<USER>/ctags/bin/"
      - "/home/<USER>/cmake/bin/"
      - "/home/<USER>/bitwise/bin/"
      - "/home/<USER>/bear/bin/"
      - "/home/<USER>/automake/bin/"
      - "/home/<USER>/autoconf/bin/"
      - "/home/<USER>/local/bin/"
      - "/home/<USER>/.cargo/bin/"
      - "/trunk/go/bin/"
      - "/usr/local/bin/"
      - "/usr/bin/"
      - "/home/<USER>/.fzf/bin/"
    found: "/opt/rh/devtoolset-11/root/usr/bin/nm"
    search_context:
      ENV{PATH}:
        - "/opt/rh/devtoolset-11/root/usr/bin"
        - "/opt/synopsys/vcs/V-2023.12-SP2/bin"
        - "/opt/synopsys/verdi/V-2023.12-SP2/bin"
        - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli"
        - "/home/<USER>/git-extras/bin"
        - "/home/<USER>/zsh/bin"
        - "/home/<USER>/zeromq/bin"
        - "/home/<USER>/yodl/bin"
        - "/home/<USER>/xsel/bin"
        - "/home/<USER>/xclip/bin"
        - "/home/<USER>/vim/bin"
        - "/home/<USER>/verible/bin"
        - "/home/<USER>/tmux/bin"
        - "/home/<USER>/tcl/bin"
        - "/home/<USER>/readline/bin"
        - "/home/<USER>/python/bin"
        - "/home/<USER>/pcre2/bin"
        - "/home/<USER>/openssl/bin"
        - "/home/<USER>/oniguruma/bin"
        - "/home/<USER>/node/bin"
        - "/home/<USER>/make/bin"
        - "/home/<USER>/m4/bin"
        - "/home/<USER>/lua/bin"
        - "/home/<USER>/llvm/bin"
        - "/home/<USER>/libtool/bin"
        - "/home/<USER>/libpsl/bin"
        - "/home/<USER>/libiconv/bin"
        - "/home/<USER>/libevent/bin"
        - "/home/<USER>/iverilog/bin"
        - "/home/<USER>/imlib2/bin"
        - "/home/<USER>/graphviz/bin"
        - "/home/<USER>/gperf/bin"
        - "/home/<USER>/go/bin"
        - "/home/<USER>/global/bin"
        - "/home/<USER>/git/bin"
        - "/home/<USER>/giflib/bin"
        - "/home/<USER>/gettext/bin"
        - "/home/<USER>/flatbuffers/bin"
        - "/home/<USER>/feh/bin"
        - "/home/<USER>/dtc/bin"
        - "/home/<USER>/dropbear/bin"
        - "/home/<USER>/dropbear/sbin"
        - "/home/<USER>/doxygen/bin"
        - "/home/<USER>/curl/bin"
        - "/home/<USER>/ctags/bin"
        - "/home/<USER>/cmake/bin"
        - "/home/<USER>/bitwise/bin"
        - "/home/<USER>/bear/bin"
        - "/home/<USER>/automake/bin"
        - "/home/<USER>/autoconf/bin"
        - "/home/<USER>/local/bin"
        - "/home/<USER>/.cargo/bin"
        - "/trunk/go/bin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/home/<USER>/.fzf/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt"
    mode: "program"
    variable: "CMAKE_OBJDUMP"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "objdump"
    candidate_directories:
      - "/opt/rh/devtoolset-11/root/usr/bin/"
      - "/opt/synopsys/vcs/V-2023.12-SP2/bin/"
      - "/opt/synopsys/verdi/V-2023.12-SP2/bin/"
      - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli/"
      - "/home/<USER>/git-extras/bin/"
      - "/home/<USER>/zsh/bin/"
      - "/home/<USER>/zeromq/bin/"
      - "/home/<USER>/yodl/bin/"
      - "/home/<USER>/xsel/bin/"
      - "/home/<USER>/xclip/bin/"
      - "/home/<USER>/vim/bin/"
      - "/home/<USER>/verible/bin/"
      - "/home/<USER>/tmux/bin/"
      - "/home/<USER>/tcl/bin/"
      - "/home/<USER>/readline/bin/"
      - "/home/<USER>/python/bin/"
      - "/home/<USER>/pcre2/bin/"
      - "/home/<USER>/openssl/bin/"
      - "/home/<USER>/oniguruma/bin/"
      - "/home/<USER>/node/bin/"
      - "/home/<USER>/make/bin/"
      - "/home/<USER>/m4/bin/"
      - "/home/<USER>/lua/bin/"
      - "/home/<USER>/llvm/bin/"
      - "/home/<USER>/libtool/bin/"
      - "/home/<USER>/libpsl/bin/"
      - "/home/<USER>/libiconv/bin/"
      - "/home/<USER>/libevent/bin/"
      - "/home/<USER>/iverilog/bin/"
      - "/home/<USER>/imlib2/bin/"
      - "/home/<USER>/graphviz/bin/"
      - "/home/<USER>/gperf/bin/"
      - "/home/<USER>/go/bin/"
      - "/home/<USER>/global/bin/"
      - "/home/<USER>/git/bin/"
      - "/home/<USER>/giflib/bin/"
      - "/home/<USER>/gettext/bin/"
      - "/home/<USER>/flatbuffers/bin/"
      - "/home/<USER>/feh/bin/"
      - "/home/<USER>/dtc/bin/"
      - "/home/<USER>/dropbear/bin/"
      - "/home/<USER>/dropbear/sbin/"
      - "/home/<USER>/doxygen/bin/"
      - "/home/<USER>/curl/bin/"
      - "/home/<USER>/ctags/bin/"
      - "/home/<USER>/cmake/bin/"
      - "/home/<USER>/bitwise/bin/"
      - "/home/<USER>/bear/bin/"
      - "/home/<USER>/automake/bin/"
      - "/home/<USER>/autoconf/bin/"
      - "/home/<USER>/local/bin/"
      - "/home/<USER>/.cargo/bin/"
      - "/trunk/go/bin/"
      - "/usr/local/bin/"
      - "/usr/bin/"
      - "/home/<USER>/.fzf/bin/"
    found: "/opt/rh/devtoolset-11/root/usr/bin/objdump"
    search_context:
      ENV{PATH}:
        - "/opt/rh/devtoolset-11/root/usr/bin"
        - "/opt/synopsys/vcs/V-2023.12-SP2/bin"
        - "/opt/synopsys/verdi/V-2023.12-SP2/bin"
        - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli"
        - "/home/<USER>/git-extras/bin"
        - "/home/<USER>/zsh/bin"
        - "/home/<USER>/zeromq/bin"
        - "/home/<USER>/yodl/bin"
        - "/home/<USER>/xsel/bin"
        - "/home/<USER>/xclip/bin"
        - "/home/<USER>/vim/bin"
        - "/home/<USER>/verible/bin"
        - "/home/<USER>/tmux/bin"
        - "/home/<USER>/tcl/bin"
        - "/home/<USER>/readline/bin"
        - "/home/<USER>/python/bin"
        - "/home/<USER>/pcre2/bin"
        - "/home/<USER>/openssl/bin"
        - "/home/<USER>/oniguruma/bin"
        - "/home/<USER>/node/bin"
        - "/home/<USER>/make/bin"
        - "/home/<USER>/m4/bin"
        - "/home/<USER>/lua/bin"
        - "/home/<USER>/llvm/bin"
        - "/home/<USER>/libtool/bin"
        - "/home/<USER>/libpsl/bin"
        - "/home/<USER>/libiconv/bin"
        - "/home/<USER>/libevent/bin"
        - "/home/<USER>/iverilog/bin"
        - "/home/<USER>/imlib2/bin"
        - "/home/<USER>/graphviz/bin"
        - "/home/<USER>/gperf/bin"
        - "/home/<USER>/go/bin"
        - "/home/<USER>/global/bin"
        - "/home/<USER>/git/bin"
        - "/home/<USER>/giflib/bin"
        - "/home/<USER>/gettext/bin"
        - "/home/<USER>/flatbuffers/bin"
        - "/home/<USER>/feh/bin"
        - "/home/<USER>/dtc/bin"
        - "/home/<USER>/dropbear/bin"
        - "/home/<USER>/dropbear/sbin"
        - "/home/<USER>/doxygen/bin"
        - "/home/<USER>/curl/bin"
        - "/home/<USER>/ctags/bin"
        - "/home/<USER>/cmake/bin"
        - "/home/<USER>/bitwise/bin"
        - "/home/<USER>/bear/bin"
        - "/home/<USER>/automake/bin"
        - "/home/<USER>/autoconf/bin"
        - "/home/<USER>/local/bin"
        - "/home/<USER>/.cargo/bin"
        - "/trunk/go/bin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/home/<USER>/.fzf/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt"
    mode: "program"
    variable: "CMAKE_OBJCOPY"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "objcopy"
    candidate_directories:
      - "/opt/rh/devtoolset-11/root/usr/bin/"
      - "/opt/synopsys/vcs/V-2023.12-SP2/bin/"
      - "/opt/synopsys/verdi/V-2023.12-SP2/bin/"
      - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli/"
      - "/home/<USER>/git-extras/bin/"
      - "/home/<USER>/zsh/bin/"
      - "/home/<USER>/zeromq/bin/"
      - "/home/<USER>/yodl/bin/"
      - "/home/<USER>/xsel/bin/"
      - "/home/<USER>/xclip/bin/"
      - "/home/<USER>/vim/bin/"
      - "/home/<USER>/verible/bin/"
      - "/home/<USER>/tmux/bin/"
      - "/home/<USER>/tcl/bin/"
      - "/home/<USER>/readline/bin/"
      - "/home/<USER>/python/bin/"
      - "/home/<USER>/pcre2/bin/"
      - "/home/<USER>/openssl/bin/"
      - "/home/<USER>/oniguruma/bin/"
      - "/home/<USER>/node/bin/"
      - "/home/<USER>/make/bin/"
      - "/home/<USER>/m4/bin/"
      - "/home/<USER>/lua/bin/"
      - "/home/<USER>/llvm/bin/"
      - "/home/<USER>/libtool/bin/"
      - "/home/<USER>/libpsl/bin/"
      - "/home/<USER>/libiconv/bin/"
      - "/home/<USER>/libevent/bin/"
      - "/home/<USER>/iverilog/bin/"
      - "/home/<USER>/imlib2/bin/"
      - "/home/<USER>/graphviz/bin/"
      - "/home/<USER>/gperf/bin/"
      - "/home/<USER>/go/bin/"
      - "/home/<USER>/global/bin/"
      - "/home/<USER>/git/bin/"
      - "/home/<USER>/giflib/bin/"
      - "/home/<USER>/gettext/bin/"
      - "/home/<USER>/flatbuffers/bin/"
      - "/home/<USER>/feh/bin/"
      - "/home/<USER>/dtc/bin/"
      - "/home/<USER>/dropbear/bin/"
      - "/home/<USER>/dropbear/sbin/"
      - "/home/<USER>/doxygen/bin/"
      - "/home/<USER>/curl/bin/"
      - "/home/<USER>/ctags/bin/"
      - "/home/<USER>/cmake/bin/"
      - "/home/<USER>/bitwise/bin/"
      - "/home/<USER>/bear/bin/"
      - "/home/<USER>/automake/bin/"
      - "/home/<USER>/autoconf/bin/"
      - "/home/<USER>/local/bin/"
      - "/home/<USER>/.cargo/bin/"
      - "/trunk/go/bin/"
      - "/usr/local/bin/"
      - "/usr/bin/"
      - "/home/<USER>/.fzf/bin/"
    found: "/opt/rh/devtoolset-11/root/usr/bin/objcopy"
    search_context:
      ENV{PATH}:
        - "/opt/rh/devtoolset-11/root/usr/bin"
        - "/opt/synopsys/vcs/V-2023.12-SP2/bin"
        - "/opt/synopsys/verdi/V-2023.12-SP2/bin"
        - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli"
        - "/home/<USER>/git-extras/bin"
        - "/home/<USER>/zsh/bin"
        - "/home/<USER>/zeromq/bin"
        - "/home/<USER>/yodl/bin"
        - "/home/<USER>/xsel/bin"
        - "/home/<USER>/xclip/bin"
        - "/home/<USER>/vim/bin"
        - "/home/<USER>/verible/bin"
        - "/home/<USER>/tmux/bin"
        - "/home/<USER>/tcl/bin"
        - "/home/<USER>/readline/bin"
        - "/home/<USER>/python/bin"
        - "/home/<USER>/pcre2/bin"
        - "/home/<USER>/openssl/bin"
        - "/home/<USER>/oniguruma/bin"
        - "/home/<USER>/node/bin"
        - "/home/<USER>/make/bin"
        - "/home/<USER>/m4/bin"
        - "/home/<USER>/lua/bin"
        - "/home/<USER>/llvm/bin"
        - "/home/<USER>/libtool/bin"
        - "/home/<USER>/libpsl/bin"
        - "/home/<USER>/libiconv/bin"
        - "/home/<USER>/libevent/bin"
        - "/home/<USER>/iverilog/bin"
        - "/home/<USER>/imlib2/bin"
        - "/home/<USER>/graphviz/bin"
        - "/home/<USER>/gperf/bin"
        - "/home/<USER>/go/bin"
        - "/home/<USER>/global/bin"
        - "/home/<USER>/git/bin"
        - "/home/<USER>/giflib/bin"
        - "/home/<USER>/gettext/bin"
        - "/home/<USER>/flatbuffers/bin"
        - "/home/<USER>/feh/bin"
        - "/home/<USER>/dtc/bin"
        - "/home/<USER>/dropbear/bin"
        - "/home/<USER>/dropbear/sbin"
        - "/home/<USER>/doxygen/bin"
        - "/home/<USER>/curl/bin"
        - "/home/<USER>/ctags/bin"
        - "/home/<USER>/cmake/bin"
        - "/home/<USER>/bitwise/bin"
        - "/home/<USER>/bear/bin"
        - "/home/<USER>/automake/bin"
        - "/home/<USER>/autoconf/bin"
        - "/home/<USER>/local/bin"
        - "/home/<USER>/.cargo/bin"
        - "/trunk/go/bin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/home/<USER>/.fzf/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt"
    mode: "program"
    variable: "CMAKE_READELF"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "readelf"
    candidate_directories:
      - "/opt/rh/devtoolset-11/root/usr/bin/"
      - "/opt/synopsys/vcs/V-2023.12-SP2/bin/"
      - "/opt/synopsys/verdi/V-2023.12-SP2/bin/"
      - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli/"
      - "/home/<USER>/git-extras/bin/"
      - "/home/<USER>/zsh/bin/"
      - "/home/<USER>/zeromq/bin/"
      - "/home/<USER>/yodl/bin/"
      - "/home/<USER>/xsel/bin/"
      - "/home/<USER>/xclip/bin/"
      - "/home/<USER>/vim/bin/"
      - "/home/<USER>/verible/bin/"
      - "/home/<USER>/tmux/bin/"
      - "/home/<USER>/tcl/bin/"
      - "/home/<USER>/readline/bin/"
      - "/home/<USER>/python/bin/"
      - "/home/<USER>/pcre2/bin/"
      - "/home/<USER>/openssl/bin/"
      - "/home/<USER>/oniguruma/bin/"
      - "/home/<USER>/node/bin/"
      - "/home/<USER>/make/bin/"
      - "/home/<USER>/m4/bin/"
      - "/home/<USER>/lua/bin/"
      - "/home/<USER>/llvm/bin/"
      - "/home/<USER>/libtool/bin/"
      - "/home/<USER>/libpsl/bin/"
      - "/home/<USER>/libiconv/bin/"
      - "/home/<USER>/libevent/bin/"
      - "/home/<USER>/iverilog/bin/"
      - "/home/<USER>/imlib2/bin/"
      - "/home/<USER>/graphviz/bin/"
      - "/home/<USER>/gperf/bin/"
      - "/home/<USER>/go/bin/"
      - "/home/<USER>/global/bin/"
      - "/home/<USER>/git/bin/"
      - "/home/<USER>/giflib/bin/"
      - "/home/<USER>/gettext/bin/"
      - "/home/<USER>/flatbuffers/bin/"
      - "/home/<USER>/feh/bin/"
      - "/home/<USER>/dtc/bin/"
      - "/home/<USER>/dropbear/bin/"
      - "/home/<USER>/dropbear/sbin/"
      - "/home/<USER>/doxygen/bin/"
      - "/home/<USER>/curl/bin/"
      - "/home/<USER>/ctags/bin/"
      - "/home/<USER>/cmake/bin/"
      - "/home/<USER>/bitwise/bin/"
      - "/home/<USER>/bear/bin/"
      - "/home/<USER>/automake/bin/"
      - "/home/<USER>/autoconf/bin/"
      - "/home/<USER>/local/bin/"
      - "/home/<USER>/.cargo/bin/"
      - "/trunk/go/bin/"
      - "/usr/local/bin/"
      - "/usr/bin/"
      - "/home/<USER>/.fzf/bin/"
    found: "/opt/rh/devtoolset-11/root/usr/bin/readelf"
    search_context:
      ENV{PATH}:
        - "/opt/rh/devtoolset-11/root/usr/bin"
        - "/opt/synopsys/vcs/V-2023.12-SP2/bin"
        - "/opt/synopsys/verdi/V-2023.12-SP2/bin"
        - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli"
        - "/home/<USER>/git-extras/bin"
        - "/home/<USER>/zsh/bin"
        - "/home/<USER>/zeromq/bin"
        - "/home/<USER>/yodl/bin"
        - "/home/<USER>/xsel/bin"
        - "/home/<USER>/xclip/bin"
        - "/home/<USER>/vim/bin"
        - "/home/<USER>/verible/bin"
        - "/home/<USER>/tmux/bin"
        - "/home/<USER>/tcl/bin"
        - "/home/<USER>/readline/bin"
        - "/home/<USER>/python/bin"
        - "/home/<USER>/pcre2/bin"
        - "/home/<USER>/openssl/bin"
        - "/home/<USER>/oniguruma/bin"
        - "/home/<USER>/node/bin"
        - "/home/<USER>/make/bin"
        - "/home/<USER>/m4/bin"
        - "/home/<USER>/lua/bin"
        - "/home/<USER>/llvm/bin"
        - "/home/<USER>/libtool/bin"
        - "/home/<USER>/libpsl/bin"
        - "/home/<USER>/libiconv/bin"
        - "/home/<USER>/libevent/bin"
        - "/home/<USER>/iverilog/bin"
        - "/home/<USER>/imlib2/bin"
        - "/home/<USER>/graphviz/bin"
        - "/home/<USER>/gperf/bin"
        - "/home/<USER>/go/bin"
        - "/home/<USER>/global/bin"
        - "/home/<USER>/git/bin"
        - "/home/<USER>/giflib/bin"
        - "/home/<USER>/gettext/bin"
        - "/home/<USER>/flatbuffers/bin"
        - "/home/<USER>/feh/bin"
        - "/home/<USER>/dtc/bin"
        - "/home/<USER>/dropbear/bin"
        - "/home/<USER>/dropbear/sbin"
        - "/home/<USER>/doxygen/bin"
        - "/home/<USER>/curl/bin"
        - "/home/<USER>/ctags/bin"
        - "/home/<USER>/cmake/bin"
        - "/home/<USER>/bitwise/bin"
        - "/home/<USER>/bear/bin"
        - "/home/<USER>/automake/bin"
        - "/home/<USER>/autoconf/bin"
        - "/home/<USER>/local/bin"
        - "/home/<USER>/.cargo/bin"
        - "/trunk/go/bin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/home/<USER>/.fzf/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt"
    mode: "program"
    variable: "CMAKE_DLLTOOL"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "dlltool"
    candidate_directories:
      - "/opt/rh/devtoolset-11/root/usr/bin/"
      - "/opt/synopsys/vcs/V-2023.12-SP2/bin/"
      - "/opt/synopsys/verdi/V-2023.12-SP2/bin/"
      - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli/"
      - "/home/<USER>/git-extras/bin/"
      - "/home/<USER>/zsh/bin/"
      - "/home/<USER>/zeromq/bin/"
      - "/home/<USER>/yodl/bin/"
      - "/home/<USER>/xsel/bin/"
      - "/home/<USER>/xclip/bin/"
      - "/home/<USER>/vim/bin/"
      - "/home/<USER>/verible/bin/"
      - "/home/<USER>/tmux/bin/"
      - "/home/<USER>/tcl/bin/"
      - "/home/<USER>/readline/bin/"
      - "/home/<USER>/python/bin/"
      - "/home/<USER>/pcre2/bin/"
      - "/home/<USER>/openssl/bin/"
      - "/home/<USER>/oniguruma/bin/"
      - "/home/<USER>/node/bin/"
      - "/home/<USER>/make/bin/"
      - "/home/<USER>/m4/bin/"
      - "/home/<USER>/lua/bin/"
      - "/home/<USER>/llvm/bin/"
      - "/home/<USER>/libtool/bin/"
      - "/home/<USER>/libpsl/bin/"
      - "/home/<USER>/libiconv/bin/"
      - "/home/<USER>/libevent/bin/"
      - "/home/<USER>/iverilog/bin/"
      - "/home/<USER>/imlib2/bin/"
      - "/home/<USER>/graphviz/bin/"
      - "/home/<USER>/gperf/bin/"
      - "/home/<USER>/go/bin/"
      - "/home/<USER>/global/bin/"
      - "/home/<USER>/git/bin/"
      - "/home/<USER>/giflib/bin/"
      - "/home/<USER>/gettext/bin/"
      - "/home/<USER>/flatbuffers/bin/"
      - "/home/<USER>/feh/bin/"
      - "/home/<USER>/dtc/bin/"
      - "/home/<USER>/dropbear/bin/"
      - "/home/<USER>/dropbear/sbin/"
      - "/home/<USER>/doxygen/bin/"
      - "/home/<USER>/curl/bin/"
      - "/home/<USER>/ctags/bin/"
      - "/home/<USER>/cmake/bin/"
      - "/home/<USER>/bitwise/bin/"
      - "/home/<USER>/bear/bin/"
      - "/home/<USER>/automake/bin/"
      - "/home/<USER>/autoconf/bin/"
      - "/home/<USER>/local/bin/"
      - "/home/<USER>/.cargo/bin/"
      - "/trunk/go/bin/"
      - "/usr/local/bin/"
      - "/usr/bin/"
      - "/home/<USER>/.fzf/bin/"
    searched_directories:
      - "/opt/rh/devtoolset-11/root/usr/bin/dlltool"
      - "/opt/synopsys/vcs/V-2023.12-SP2/bin/dlltool"
      - "/opt/synopsys/verdi/V-2023.12-SP2/bin/dlltool"
      - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli/dlltool"
      - "/home/<USER>/git-extras/bin/dlltool"
      - "/home/<USER>/zsh/bin/dlltool"
      - "/home/<USER>/zeromq/bin/dlltool"
      - "/home/<USER>/yodl/bin/dlltool"
      - "/home/<USER>/xsel/bin/dlltool"
      - "/home/<USER>/xclip/bin/dlltool"
      - "/home/<USER>/vim/bin/dlltool"
      - "/home/<USER>/verible/bin/dlltool"
      - "/home/<USER>/tmux/bin/dlltool"
      - "/home/<USER>/tcl/bin/dlltool"
      - "/home/<USER>/readline/bin/dlltool"
      - "/home/<USER>/python/bin/dlltool"
      - "/home/<USER>/pcre2/bin/dlltool"
      - "/home/<USER>/openssl/bin/dlltool"
      - "/home/<USER>/oniguruma/bin/dlltool"
      - "/home/<USER>/node/bin/dlltool"
      - "/home/<USER>/make/bin/dlltool"
      - "/home/<USER>/m4/bin/dlltool"
      - "/home/<USER>/lua/bin/dlltool"
      - "/home/<USER>/llvm/bin/dlltool"
      - "/home/<USER>/libtool/bin/dlltool"
      - "/home/<USER>/libpsl/bin/dlltool"
      - "/home/<USER>/libiconv/bin/dlltool"
      - "/home/<USER>/libevent/bin/dlltool"
      - "/home/<USER>/iverilog/bin/dlltool"
      - "/home/<USER>/imlib2/bin/dlltool"
      - "/home/<USER>/graphviz/bin/dlltool"
      - "/home/<USER>/gperf/bin/dlltool"
      - "/home/<USER>/go/bin/dlltool"
      - "/home/<USER>/global/bin/dlltool"
      - "/home/<USER>/git/bin/dlltool"
      - "/home/<USER>/giflib/bin/dlltool"
      - "/home/<USER>/gettext/bin/dlltool"
      - "/home/<USER>/flatbuffers/bin/dlltool"
      - "/home/<USER>/feh/bin/dlltool"
      - "/home/<USER>/dtc/bin/dlltool"
      - "/home/<USER>/dropbear/bin/dlltool"
      - "/home/<USER>/dropbear/sbin/dlltool"
      - "/home/<USER>/doxygen/bin/dlltool"
      - "/home/<USER>/curl/bin/dlltool"
      - "/home/<USER>/ctags/bin/dlltool"
      - "/home/<USER>/cmake/bin/dlltool"
      - "/home/<USER>/bitwise/bin/dlltool"
      - "/home/<USER>/bear/bin/dlltool"
      - "/home/<USER>/automake/bin/dlltool"
      - "/home/<USER>/autoconf/bin/dlltool"
      - "/home/<USER>/local/bin/dlltool"
      - "/home/<USER>/.cargo/bin/dlltool"
      - "/trunk/go/bin/dlltool"
      - "/usr/local/bin/dlltool"
      - "/usr/bin/dlltool"
      - "/home/<USER>/.fzf/bin/dlltool"
    found: false
    search_context:
      ENV{PATH}:
        - "/opt/rh/devtoolset-11/root/usr/bin"
        - "/opt/synopsys/vcs/V-2023.12-SP2/bin"
        - "/opt/synopsys/verdi/V-2023.12-SP2/bin"
        - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli"
        - "/home/<USER>/git-extras/bin"
        - "/home/<USER>/zsh/bin"
        - "/home/<USER>/zeromq/bin"
        - "/home/<USER>/yodl/bin"
        - "/home/<USER>/xsel/bin"
        - "/home/<USER>/xclip/bin"
        - "/home/<USER>/vim/bin"
        - "/home/<USER>/verible/bin"
        - "/home/<USER>/tmux/bin"
        - "/home/<USER>/tcl/bin"
        - "/home/<USER>/readline/bin"
        - "/home/<USER>/python/bin"
        - "/home/<USER>/pcre2/bin"
        - "/home/<USER>/openssl/bin"
        - "/home/<USER>/oniguruma/bin"
        - "/home/<USER>/node/bin"
        - "/home/<USER>/make/bin"
        - "/home/<USER>/m4/bin"
        - "/home/<USER>/lua/bin"
        - "/home/<USER>/llvm/bin"
        - "/home/<USER>/libtool/bin"
        - "/home/<USER>/libpsl/bin"
        - "/home/<USER>/libiconv/bin"
        - "/home/<USER>/libevent/bin"
        - "/home/<USER>/iverilog/bin"
        - "/home/<USER>/imlib2/bin"
        - "/home/<USER>/graphviz/bin"
        - "/home/<USER>/gperf/bin"
        - "/home/<USER>/go/bin"
        - "/home/<USER>/global/bin"
        - "/home/<USER>/git/bin"
        - "/home/<USER>/giflib/bin"
        - "/home/<USER>/gettext/bin"
        - "/home/<USER>/flatbuffers/bin"
        - "/home/<USER>/feh/bin"
        - "/home/<USER>/dtc/bin"
        - "/home/<USER>/dropbear/bin"
        - "/home/<USER>/dropbear/sbin"
        - "/home/<USER>/doxygen/bin"
        - "/home/<USER>/curl/bin"
        - "/home/<USER>/ctags/bin"
        - "/home/<USER>/cmake/bin"
        - "/home/<USER>/bitwise/bin"
        - "/home/<USER>/bear/bin"
        - "/home/<USER>/automake/bin"
        - "/home/<USER>/autoconf/bin"
        - "/home/<USER>/local/bin"
        - "/home/<USER>/.cargo/bin"
        - "/trunk/go/bin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/home/<USER>/.fzf/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt"
    mode: "program"
    variable: "CMAKE_ADDR2LINE"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "addr2line"
    candidate_directories:
      - "/opt/rh/devtoolset-11/root/usr/bin/"
      - "/opt/synopsys/vcs/V-2023.12-SP2/bin/"
      - "/opt/synopsys/verdi/V-2023.12-SP2/bin/"
      - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli/"
      - "/home/<USER>/git-extras/bin/"
      - "/home/<USER>/zsh/bin/"
      - "/home/<USER>/zeromq/bin/"
      - "/home/<USER>/yodl/bin/"
      - "/home/<USER>/xsel/bin/"
      - "/home/<USER>/xclip/bin/"
      - "/home/<USER>/vim/bin/"
      - "/home/<USER>/verible/bin/"
      - "/home/<USER>/tmux/bin/"
      - "/home/<USER>/tcl/bin/"
      - "/home/<USER>/readline/bin/"
      - "/home/<USER>/python/bin/"
      - "/home/<USER>/pcre2/bin/"
      - "/home/<USER>/openssl/bin/"
      - "/home/<USER>/oniguruma/bin/"
      - "/home/<USER>/node/bin/"
      - "/home/<USER>/make/bin/"
      - "/home/<USER>/m4/bin/"
      - "/home/<USER>/lua/bin/"
      - "/home/<USER>/llvm/bin/"
      - "/home/<USER>/libtool/bin/"
      - "/home/<USER>/libpsl/bin/"
      - "/home/<USER>/libiconv/bin/"
      - "/home/<USER>/libevent/bin/"
      - "/home/<USER>/iverilog/bin/"
      - "/home/<USER>/imlib2/bin/"
      - "/home/<USER>/graphviz/bin/"
      - "/home/<USER>/gperf/bin/"
      - "/home/<USER>/go/bin/"
      - "/home/<USER>/global/bin/"
      - "/home/<USER>/git/bin/"
      - "/home/<USER>/giflib/bin/"
      - "/home/<USER>/gettext/bin/"
      - "/home/<USER>/flatbuffers/bin/"
      - "/home/<USER>/feh/bin/"
      - "/home/<USER>/dtc/bin/"
      - "/home/<USER>/dropbear/bin/"
      - "/home/<USER>/dropbear/sbin/"
      - "/home/<USER>/doxygen/bin/"
      - "/home/<USER>/curl/bin/"
      - "/home/<USER>/ctags/bin/"
      - "/home/<USER>/cmake/bin/"
      - "/home/<USER>/bitwise/bin/"
      - "/home/<USER>/bear/bin/"
      - "/home/<USER>/automake/bin/"
      - "/home/<USER>/autoconf/bin/"
      - "/home/<USER>/local/bin/"
      - "/home/<USER>/.cargo/bin/"
      - "/trunk/go/bin/"
      - "/usr/local/bin/"
      - "/usr/bin/"
      - "/home/<USER>/.fzf/bin/"
    found: "/opt/rh/devtoolset-11/root/usr/bin/addr2line"
    search_context:
      ENV{PATH}:
        - "/opt/rh/devtoolset-11/root/usr/bin"
        - "/opt/synopsys/vcs/V-2023.12-SP2/bin"
        - "/opt/synopsys/verdi/V-2023.12-SP2/bin"
        - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli"
        - "/home/<USER>/git-extras/bin"
        - "/home/<USER>/zsh/bin"
        - "/home/<USER>/zeromq/bin"
        - "/home/<USER>/yodl/bin"
        - "/home/<USER>/xsel/bin"
        - "/home/<USER>/xclip/bin"
        - "/home/<USER>/vim/bin"
        - "/home/<USER>/verible/bin"
        - "/home/<USER>/tmux/bin"
        - "/home/<USER>/tcl/bin"
        - "/home/<USER>/readline/bin"
        - "/home/<USER>/python/bin"
        - "/home/<USER>/pcre2/bin"
        - "/home/<USER>/openssl/bin"
        - "/home/<USER>/oniguruma/bin"
        - "/home/<USER>/node/bin"
        - "/home/<USER>/make/bin"
        - "/home/<USER>/m4/bin"
        - "/home/<USER>/lua/bin"
        - "/home/<USER>/llvm/bin"
        - "/home/<USER>/libtool/bin"
        - "/home/<USER>/libpsl/bin"
        - "/home/<USER>/libiconv/bin"
        - "/home/<USER>/libevent/bin"
        - "/home/<USER>/iverilog/bin"
        - "/home/<USER>/imlib2/bin"
        - "/home/<USER>/graphviz/bin"
        - "/home/<USER>/gperf/bin"
        - "/home/<USER>/go/bin"
        - "/home/<USER>/global/bin"
        - "/home/<USER>/git/bin"
        - "/home/<USER>/giflib/bin"
        - "/home/<USER>/gettext/bin"
        - "/home/<USER>/flatbuffers/bin"
        - "/home/<USER>/feh/bin"
        - "/home/<USER>/dtc/bin"
        - "/home/<USER>/dropbear/bin"
        - "/home/<USER>/dropbear/sbin"
        - "/home/<USER>/doxygen/bin"
        - "/home/<USER>/curl/bin"
        - "/home/<USER>/ctags/bin"
        - "/home/<USER>/cmake/bin"
        - "/home/<USER>/bitwise/bin"
        - "/home/<USER>/bear/bin"
        - "/home/<USER>/automake/bin"
        - "/home/<USER>/autoconf/bin"
        - "/home/<USER>/local/bin"
        - "/home/<USER>/.cargo/bin"
        - "/trunk/go/bin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/home/<USER>/.fzf/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt"
    mode: "program"
    variable: "CMAKE_TAPI"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "tapi"
    candidate_directories:
      - "/opt/rh/devtoolset-11/root/usr/bin/"
      - "/opt/synopsys/vcs/V-2023.12-SP2/bin/"
      - "/opt/synopsys/verdi/V-2023.12-SP2/bin/"
      - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli/"
      - "/home/<USER>/git-extras/bin/"
      - "/home/<USER>/zsh/bin/"
      - "/home/<USER>/zeromq/bin/"
      - "/home/<USER>/yodl/bin/"
      - "/home/<USER>/xsel/bin/"
      - "/home/<USER>/xclip/bin/"
      - "/home/<USER>/vim/bin/"
      - "/home/<USER>/verible/bin/"
      - "/home/<USER>/tmux/bin/"
      - "/home/<USER>/tcl/bin/"
      - "/home/<USER>/readline/bin/"
      - "/home/<USER>/python/bin/"
      - "/home/<USER>/pcre2/bin/"
      - "/home/<USER>/openssl/bin/"
      - "/home/<USER>/oniguruma/bin/"
      - "/home/<USER>/node/bin/"
      - "/home/<USER>/make/bin/"
      - "/home/<USER>/m4/bin/"
      - "/home/<USER>/lua/bin/"
      - "/home/<USER>/llvm/bin/"
      - "/home/<USER>/libtool/bin/"
      - "/home/<USER>/libpsl/bin/"
      - "/home/<USER>/libiconv/bin/"
      - "/home/<USER>/libevent/bin/"
      - "/home/<USER>/iverilog/bin/"
      - "/home/<USER>/imlib2/bin/"
      - "/home/<USER>/graphviz/bin/"
      - "/home/<USER>/gperf/bin/"
      - "/home/<USER>/go/bin/"
      - "/home/<USER>/global/bin/"
      - "/home/<USER>/git/bin/"
      - "/home/<USER>/giflib/bin/"
      - "/home/<USER>/gettext/bin/"
      - "/home/<USER>/flatbuffers/bin/"
      - "/home/<USER>/feh/bin/"
      - "/home/<USER>/dtc/bin/"
      - "/home/<USER>/dropbear/bin/"
      - "/home/<USER>/dropbear/sbin/"
      - "/home/<USER>/doxygen/bin/"
      - "/home/<USER>/curl/bin/"
      - "/home/<USER>/ctags/bin/"
      - "/home/<USER>/cmake/bin/"
      - "/home/<USER>/bitwise/bin/"
      - "/home/<USER>/bear/bin/"
      - "/home/<USER>/automake/bin/"
      - "/home/<USER>/autoconf/bin/"
      - "/home/<USER>/local/bin/"
      - "/home/<USER>/.cargo/bin/"
      - "/trunk/go/bin/"
      - "/usr/local/bin/"
      - "/usr/bin/"
      - "/home/<USER>/.fzf/bin/"
    searched_directories:
      - "/opt/rh/devtoolset-11/root/usr/bin/tapi"
      - "/opt/synopsys/vcs/V-2023.12-SP2/bin/tapi"
      - "/opt/synopsys/verdi/V-2023.12-SP2/bin/tapi"
      - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli/tapi"
      - "/home/<USER>/git-extras/bin/tapi"
      - "/home/<USER>/zsh/bin/tapi"
      - "/home/<USER>/zeromq/bin/tapi"
      - "/home/<USER>/yodl/bin/tapi"
      - "/home/<USER>/xsel/bin/tapi"
      - "/home/<USER>/xclip/bin/tapi"
      - "/home/<USER>/vim/bin/tapi"
      - "/home/<USER>/verible/bin/tapi"
      - "/home/<USER>/tmux/bin/tapi"
      - "/home/<USER>/tcl/bin/tapi"
      - "/home/<USER>/readline/bin/tapi"
      - "/home/<USER>/python/bin/tapi"
      - "/home/<USER>/pcre2/bin/tapi"
      - "/home/<USER>/openssl/bin/tapi"
      - "/home/<USER>/oniguruma/bin/tapi"
      - "/home/<USER>/node/bin/tapi"
      - "/home/<USER>/make/bin/tapi"
      - "/home/<USER>/m4/bin/tapi"
      - "/home/<USER>/lua/bin/tapi"
      - "/home/<USER>/llvm/bin/tapi"
      - "/home/<USER>/libtool/bin/tapi"
      - "/home/<USER>/libpsl/bin/tapi"
      - "/home/<USER>/libiconv/bin/tapi"
      - "/home/<USER>/libevent/bin/tapi"
      - "/home/<USER>/iverilog/bin/tapi"
      - "/home/<USER>/imlib2/bin/tapi"
      - "/home/<USER>/graphviz/bin/tapi"
      - "/home/<USER>/gperf/bin/tapi"
      - "/home/<USER>/go/bin/tapi"
      - "/home/<USER>/global/bin/tapi"
      - "/home/<USER>/git/bin/tapi"
      - "/home/<USER>/giflib/bin/tapi"
      - "/home/<USER>/gettext/bin/tapi"
      - "/home/<USER>/flatbuffers/bin/tapi"
      - "/home/<USER>/feh/bin/tapi"
      - "/home/<USER>/dtc/bin/tapi"
      - "/home/<USER>/dropbear/bin/tapi"
      - "/home/<USER>/dropbear/sbin/tapi"
      - "/home/<USER>/doxygen/bin/tapi"
      - "/home/<USER>/curl/bin/tapi"
      - "/home/<USER>/ctags/bin/tapi"
      - "/home/<USER>/cmake/bin/tapi"
      - "/home/<USER>/bitwise/bin/tapi"
      - "/home/<USER>/bear/bin/tapi"
      - "/home/<USER>/automake/bin/tapi"
      - "/home/<USER>/autoconf/bin/tapi"
      - "/home/<USER>/local/bin/tapi"
      - "/home/<USER>/.cargo/bin/tapi"
      - "/trunk/go/bin/tapi"
      - "/usr/local/bin/tapi"
      - "/usr/bin/tapi"
      - "/home/<USER>/.fzf/bin/tapi"
    found: false
    search_context:
      ENV{PATH}:
        - "/opt/rh/devtoolset-11/root/usr/bin"
        - "/opt/synopsys/vcs/V-2023.12-SP2/bin"
        - "/opt/synopsys/verdi/V-2023.12-SP2/bin"
        - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli"
        - "/home/<USER>/git-extras/bin"
        - "/home/<USER>/zsh/bin"
        - "/home/<USER>/zeromq/bin"
        - "/home/<USER>/yodl/bin"
        - "/home/<USER>/xsel/bin"
        - "/home/<USER>/xclip/bin"
        - "/home/<USER>/vim/bin"
        - "/home/<USER>/verible/bin"
        - "/home/<USER>/tmux/bin"
        - "/home/<USER>/tcl/bin"
        - "/home/<USER>/readline/bin"
        - "/home/<USER>/python/bin"
        - "/home/<USER>/pcre2/bin"
        - "/home/<USER>/openssl/bin"
        - "/home/<USER>/oniguruma/bin"
        - "/home/<USER>/node/bin"
        - "/home/<USER>/make/bin"
        - "/home/<USER>/m4/bin"
        - "/home/<USER>/lua/bin"
        - "/home/<USER>/llvm/bin"
        - "/home/<USER>/libtool/bin"
        - "/home/<USER>/libpsl/bin"
        - "/home/<USER>/libiconv/bin"
        - "/home/<USER>/libevent/bin"
        - "/home/<USER>/iverilog/bin"
        - "/home/<USER>/imlib2/bin"
        - "/home/<USER>/graphviz/bin"
        - "/home/<USER>/gperf/bin"
        - "/home/<USER>/go/bin"
        - "/home/<USER>/global/bin"
        - "/home/<USER>/git/bin"
        - "/home/<USER>/giflib/bin"
        - "/home/<USER>/gettext/bin"
        - "/home/<USER>/flatbuffers/bin"
        - "/home/<USER>/feh/bin"
        - "/home/<USER>/dtc/bin"
        - "/home/<USER>/dropbear/bin"
        - "/home/<USER>/dropbear/sbin"
        - "/home/<USER>/doxygen/bin"
        - "/home/<USER>/curl/bin"
        - "/home/<USER>/ctags/bin"
        - "/home/<USER>/cmake/bin"
        - "/home/<USER>/bitwise/bin"
        - "/home/<USER>/bear/bin"
        - "/home/<USER>/automake/bin"
        - "/home/<USER>/autoconf/bin"
        - "/home/<USER>/local/bin"
        - "/home/<USER>/.cargo/bin"
        - "/trunk/go/bin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/home/<USER>/.fzf/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/Compiler/GNU-FindBinUtils.cmake:18 (find_program)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCCompiler.cmake:201 (include)"
      - "CMakeLists.txt"
    mode: "program"
    variable: "CMAKE_C_COMPILER_AR"
    description: "A wrapper around 'ar' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcc-ar-11.2"
      - "gcc-ar-11"
      - "gcc-ar11"
      - "gcc-ar"
    candidate_directories:
      - "/opt/rh/devtoolset-11/root/usr/bin/"
      - "/opt/synopsys/vcs/V-2023.12-SP2/bin/"
      - "/opt/synopsys/verdi/V-2023.12-SP2/bin/"
      - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli/"
      - "/home/<USER>/git-extras/bin/"
      - "/home/<USER>/zsh/bin/"
      - "/home/<USER>/zeromq/bin/"
      - "/home/<USER>/yodl/bin/"
      - "/home/<USER>/xsel/bin/"
      - "/home/<USER>/xclip/bin/"
      - "/home/<USER>/vim/bin/"
      - "/home/<USER>/verible/bin/"
      - "/home/<USER>/tmux/bin/"
      - "/home/<USER>/tcl/bin/"
      - "/home/<USER>/readline/bin/"
      - "/home/<USER>/python/bin/"
      - "/home/<USER>/pcre2/bin/"
      - "/home/<USER>/openssl/bin/"
      - "/home/<USER>/oniguruma/bin/"
      - "/home/<USER>/node/bin/"
      - "/home/<USER>/make/bin/"
      - "/home/<USER>/m4/bin/"
      - "/home/<USER>/lua/bin/"
      - "/home/<USER>/llvm/bin/"
      - "/home/<USER>/libtool/bin/"
      - "/home/<USER>/libpsl/bin/"
      - "/home/<USER>/libiconv/bin/"
      - "/home/<USER>/libevent/bin/"
      - "/home/<USER>/iverilog/bin/"
      - "/home/<USER>/imlib2/bin/"
      - "/home/<USER>/graphviz/bin/"
      - "/home/<USER>/gperf/bin/"
      - "/home/<USER>/go/bin/"
      - "/home/<USER>/global/bin/"
      - "/home/<USER>/git/bin/"
      - "/home/<USER>/giflib/bin/"
      - "/home/<USER>/gettext/bin/"
      - "/home/<USER>/flatbuffers/bin/"
      - "/home/<USER>/feh/bin/"
      - "/home/<USER>/dtc/bin/"
      - "/home/<USER>/dropbear/bin/"
      - "/home/<USER>/dropbear/sbin/"
      - "/home/<USER>/doxygen/bin/"
      - "/home/<USER>/curl/bin/"
      - "/home/<USER>/ctags/bin/"
      - "/home/<USER>/cmake/bin/"
      - "/home/<USER>/bitwise/bin/"
      - "/home/<USER>/bear/bin/"
      - "/home/<USER>/automake/bin/"
      - "/home/<USER>/autoconf/bin/"
      - "/home/<USER>/local/bin/"
      - "/home/<USER>/.cargo/bin/"
      - "/trunk/go/bin/"
      - "/usr/local/bin/"
      - "/usr/bin/"
      - "/home/<USER>/.fzf/bin/"
    searched_directories:
      - "/opt/rh/devtoolset-11/root/usr/bin/gcc-ar-11.2"
      - "/opt/synopsys/vcs/V-2023.12-SP2/bin/gcc-ar-11.2"
      - "/opt/synopsys/verdi/V-2023.12-SP2/bin/gcc-ar-11.2"
      - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli/gcc-ar-11.2"
      - "/home/<USER>/git-extras/bin/gcc-ar-11.2"
      - "/home/<USER>/zsh/bin/gcc-ar-11.2"
      - "/home/<USER>/zeromq/bin/gcc-ar-11.2"
      - "/home/<USER>/yodl/bin/gcc-ar-11.2"
      - "/home/<USER>/xsel/bin/gcc-ar-11.2"
      - "/home/<USER>/xclip/bin/gcc-ar-11.2"
      - "/home/<USER>/vim/bin/gcc-ar-11.2"
      - "/home/<USER>/verible/bin/gcc-ar-11.2"
      - "/home/<USER>/tmux/bin/gcc-ar-11.2"
      - "/home/<USER>/tcl/bin/gcc-ar-11.2"
      - "/home/<USER>/readline/bin/gcc-ar-11.2"
      - "/home/<USER>/python/bin/gcc-ar-11.2"
      - "/home/<USER>/pcre2/bin/gcc-ar-11.2"
      - "/home/<USER>/openssl/bin/gcc-ar-11.2"
      - "/home/<USER>/oniguruma/bin/gcc-ar-11.2"
      - "/home/<USER>/node/bin/gcc-ar-11.2"
      - "/home/<USER>/make/bin/gcc-ar-11.2"
      - "/home/<USER>/m4/bin/gcc-ar-11.2"
      - "/home/<USER>/lua/bin/gcc-ar-11.2"
      - "/home/<USER>/llvm/bin/gcc-ar-11.2"
      - "/home/<USER>/libtool/bin/gcc-ar-11.2"
      - "/home/<USER>/libpsl/bin/gcc-ar-11.2"
      - "/home/<USER>/libiconv/bin/gcc-ar-11.2"
      - "/home/<USER>/libevent/bin/gcc-ar-11.2"
      - "/home/<USER>/iverilog/bin/gcc-ar-11.2"
      - "/home/<USER>/imlib2/bin/gcc-ar-11.2"
      - "/home/<USER>/graphviz/bin/gcc-ar-11.2"
      - "/home/<USER>/gperf/bin/gcc-ar-11.2"
      - "/home/<USER>/go/bin/gcc-ar-11.2"
      - "/home/<USER>/global/bin/gcc-ar-11.2"
      - "/home/<USER>/git/bin/gcc-ar-11.2"
      - "/home/<USER>/giflib/bin/gcc-ar-11.2"
      - "/home/<USER>/gettext/bin/gcc-ar-11.2"
      - "/home/<USER>/flatbuffers/bin/gcc-ar-11.2"
      - "/home/<USER>/feh/bin/gcc-ar-11.2"
      - "/home/<USER>/dtc/bin/gcc-ar-11.2"
      - "/home/<USER>/dropbear/bin/gcc-ar-11.2"
      - "/home/<USER>/dropbear/sbin/gcc-ar-11.2"
      - "/home/<USER>/doxygen/bin/gcc-ar-11.2"
      - "/home/<USER>/curl/bin/gcc-ar-11.2"
      - "/home/<USER>/ctags/bin/gcc-ar-11.2"
      - "/home/<USER>/cmake/bin/gcc-ar-11.2"
      - "/home/<USER>/bitwise/bin/gcc-ar-11.2"
      - "/home/<USER>/bear/bin/gcc-ar-11.2"
      - "/home/<USER>/automake/bin/gcc-ar-11.2"
      - "/home/<USER>/autoconf/bin/gcc-ar-11.2"
      - "/home/<USER>/local/bin/gcc-ar-11.2"
      - "/home/<USER>/.cargo/bin/gcc-ar-11.2"
      - "/trunk/go/bin/gcc-ar-11.2"
      - "/usr/local/bin/gcc-ar-11.2"
      - "/usr/bin/gcc-ar-11.2"
      - "/home/<USER>/.fzf/bin/gcc-ar-11.2"
      - "/opt/rh/devtoolset-11/root/usr/bin/gcc-ar-11"
      - "/opt/synopsys/vcs/V-2023.12-SP2/bin/gcc-ar-11"
      - "/opt/synopsys/verdi/V-2023.12-SP2/bin/gcc-ar-11"
      - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli/gcc-ar-11"
      - "/home/<USER>/git-extras/bin/gcc-ar-11"
      - "/home/<USER>/zsh/bin/gcc-ar-11"
      - "/home/<USER>/zeromq/bin/gcc-ar-11"
      - "/home/<USER>/yodl/bin/gcc-ar-11"
      - "/home/<USER>/xsel/bin/gcc-ar-11"
      - "/home/<USER>/xclip/bin/gcc-ar-11"
      - "/home/<USER>/vim/bin/gcc-ar-11"
      - "/home/<USER>/verible/bin/gcc-ar-11"
      - "/home/<USER>/tmux/bin/gcc-ar-11"
      - "/home/<USER>/tcl/bin/gcc-ar-11"
      - "/home/<USER>/readline/bin/gcc-ar-11"
      - "/home/<USER>/python/bin/gcc-ar-11"
      - "/home/<USER>/pcre2/bin/gcc-ar-11"
      - "/home/<USER>/openssl/bin/gcc-ar-11"
      - "/home/<USER>/oniguruma/bin/gcc-ar-11"
      - "/home/<USER>/node/bin/gcc-ar-11"
      - "/home/<USER>/make/bin/gcc-ar-11"
      - "/home/<USER>/m4/bin/gcc-ar-11"
      - "/home/<USER>/lua/bin/gcc-ar-11"
      - "/home/<USER>/llvm/bin/gcc-ar-11"
      - "/home/<USER>/libtool/bin/gcc-ar-11"
      - "/home/<USER>/libpsl/bin/gcc-ar-11"
      - "/home/<USER>/libiconv/bin/gcc-ar-11"
      - "/home/<USER>/libevent/bin/gcc-ar-11"
      - "/home/<USER>/iverilog/bin/gcc-ar-11"
      - "/home/<USER>/imlib2/bin/gcc-ar-11"
      - "/home/<USER>/graphviz/bin/gcc-ar-11"
      - "/home/<USER>/gperf/bin/gcc-ar-11"
      - "/home/<USER>/go/bin/gcc-ar-11"
      - "/home/<USER>/global/bin/gcc-ar-11"
      - "/home/<USER>/git/bin/gcc-ar-11"
      - "/home/<USER>/giflib/bin/gcc-ar-11"
      - "/home/<USER>/gettext/bin/gcc-ar-11"
      - "/home/<USER>/flatbuffers/bin/gcc-ar-11"
      - "/home/<USER>/feh/bin/gcc-ar-11"
      - "/home/<USER>/dtc/bin/gcc-ar-11"
      - "/home/<USER>/dropbear/bin/gcc-ar-11"
      - "/home/<USER>/dropbear/sbin/gcc-ar-11"
      - "/home/<USER>/doxygen/bin/gcc-ar-11"
      - "/home/<USER>/curl/bin/gcc-ar-11"
      - "/home/<USER>/ctags/bin/gcc-ar-11"
      - "/home/<USER>/cmake/bin/gcc-ar-11"
      - "/home/<USER>/bitwise/bin/gcc-ar-11"
      - "/home/<USER>/bear/bin/gcc-ar-11"
      - "/home/<USER>/automake/bin/gcc-ar-11"
      - "/home/<USER>/autoconf/bin/gcc-ar-11"
      - "/home/<USER>/local/bin/gcc-ar-11"
      - "/home/<USER>/.cargo/bin/gcc-ar-11"
      - "/trunk/go/bin/gcc-ar-11"
      - "/usr/local/bin/gcc-ar-11"
      - "/usr/bin/gcc-ar-11"
      - "/home/<USER>/.fzf/bin/gcc-ar-11"
      - "/opt/rh/devtoolset-11/root/usr/bin/gcc-ar11"
      - "/opt/synopsys/vcs/V-2023.12-SP2/bin/gcc-ar11"
      - "/opt/synopsys/verdi/V-2023.12-SP2/bin/gcc-ar11"
      - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli/gcc-ar11"
      - "/home/<USER>/git-extras/bin/gcc-ar11"
      - "/home/<USER>/zsh/bin/gcc-ar11"
      - "/home/<USER>/zeromq/bin/gcc-ar11"
      - "/home/<USER>/yodl/bin/gcc-ar11"
      - "/home/<USER>/xsel/bin/gcc-ar11"
      - "/home/<USER>/xclip/bin/gcc-ar11"
      - "/home/<USER>/vim/bin/gcc-ar11"
      - "/home/<USER>/verible/bin/gcc-ar11"
      - "/home/<USER>/tmux/bin/gcc-ar11"
      - "/home/<USER>/tcl/bin/gcc-ar11"
      - "/home/<USER>/readline/bin/gcc-ar11"
      - "/home/<USER>/python/bin/gcc-ar11"
      - "/home/<USER>/pcre2/bin/gcc-ar11"
      - "/home/<USER>/openssl/bin/gcc-ar11"
      - "/home/<USER>/oniguruma/bin/gcc-ar11"
      - "/home/<USER>/node/bin/gcc-ar11"
      - "/home/<USER>/make/bin/gcc-ar11"
      - "/home/<USER>/m4/bin/gcc-ar11"
      - "/home/<USER>/lua/bin/gcc-ar11"
      - "/home/<USER>/llvm/bin/gcc-ar11"
      - "/home/<USER>/libtool/bin/gcc-ar11"
      - "/home/<USER>/libpsl/bin/gcc-ar11"
      - "/home/<USER>/libiconv/bin/gcc-ar11"
      - "/home/<USER>/libevent/bin/gcc-ar11"
      - "/home/<USER>/iverilog/bin/gcc-ar11"
      - "/home/<USER>/imlib2/bin/gcc-ar11"
      - "/home/<USER>/graphviz/bin/gcc-ar11"
      - "/home/<USER>/gperf/bin/gcc-ar11"
      - "/home/<USER>/go/bin/gcc-ar11"
      - "/home/<USER>/global/bin/gcc-ar11"
      - "/home/<USER>/git/bin/gcc-ar11"
      - "/home/<USER>/giflib/bin/gcc-ar11"
      - "/home/<USER>/gettext/bin/gcc-ar11"
      - "/home/<USER>/flatbuffers/bin/gcc-ar11"
      - "/home/<USER>/feh/bin/gcc-ar11"
      - "/home/<USER>/dtc/bin/gcc-ar11"
      - "/home/<USER>/dropbear/bin/gcc-ar11"
      - "/home/<USER>/dropbear/sbin/gcc-ar11"
      - "/home/<USER>/doxygen/bin/gcc-ar11"
      - "/home/<USER>/curl/bin/gcc-ar11"
      - "/home/<USER>/ctags/bin/gcc-ar11"
      - "/home/<USER>/cmake/bin/gcc-ar11"
      - "/home/<USER>/bitwise/bin/gcc-ar11"
      - "/home/<USER>/bear/bin/gcc-ar11"
      - "/home/<USER>/automake/bin/gcc-ar11"
      - "/home/<USER>/autoconf/bin/gcc-ar11"
      - "/home/<USER>/local/bin/gcc-ar11"
      - "/home/<USER>/.cargo/bin/gcc-ar11"
      - "/trunk/go/bin/gcc-ar11"
      - "/usr/local/bin/gcc-ar11"
      - "/usr/bin/gcc-ar11"
      - "/home/<USER>/.fzf/bin/gcc-ar11"
    found: "/opt/rh/devtoolset-11/root/usr/bin/gcc-ar"
    search_context:
      ENV{PATH}:
        - "/opt/rh/devtoolset-11/root/usr/bin"
        - "/opt/synopsys/vcs/V-2023.12-SP2/bin"
        - "/opt/synopsys/verdi/V-2023.12-SP2/bin"
        - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli"
        - "/home/<USER>/git-extras/bin"
        - "/home/<USER>/zsh/bin"
        - "/home/<USER>/zeromq/bin"
        - "/home/<USER>/yodl/bin"
        - "/home/<USER>/xsel/bin"
        - "/home/<USER>/xclip/bin"
        - "/home/<USER>/vim/bin"
        - "/home/<USER>/verible/bin"
        - "/home/<USER>/tmux/bin"
        - "/home/<USER>/tcl/bin"
        - "/home/<USER>/readline/bin"
        - "/home/<USER>/python/bin"
        - "/home/<USER>/pcre2/bin"
        - "/home/<USER>/openssl/bin"
        - "/home/<USER>/oniguruma/bin"
        - "/home/<USER>/node/bin"
        - "/home/<USER>/make/bin"
        - "/home/<USER>/m4/bin"
        - "/home/<USER>/lua/bin"
        - "/home/<USER>/llvm/bin"
        - "/home/<USER>/libtool/bin"
        - "/home/<USER>/libpsl/bin"
        - "/home/<USER>/libiconv/bin"
        - "/home/<USER>/libevent/bin"
        - "/home/<USER>/iverilog/bin"
        - "/home/<USER>/imlib2/bin"
        - "/home/<USER>/graphviz/bin"
        - "/home/<USER>/gperf/bin"
        - "/home/<USER>/go/bin"
        - "/home/<USER>/global/bin"
        - "/home/<USER>/git/bin"
        - "/home/<USER>/giflib/bin"
        - "/home/<USER>/gettext/bin"
        - "/home/<USER>/flatbuffers/bin"
        - "/home/<USER>/feh/bin"
        - "/home/<USER>/dtc/bin"
        - "/home/<USER>/dropbear/bin"
        - "/home/<USER>/dropbear/sbin"
        - "/home/<USER>/doxygen/bin"
        - "/home/<USER>/curl/bin"
        - "/home/<USER>/ctags/bin"
        - "/home/<USER>/cmake/bin"
        - "/home/<USER>/bitwise/bin"
        - "/home/<USER>/bear/bin"
        - "/home/<USER>/automake/bin"
        - "/home/<USER>/autoconf/bin"
        - "/home/<USER>/local/bin"
        - "/home/<USER>/.cargo/bin"
        - "/trunk/go/bin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/home/<USER>/.fzf/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/Compiler/GNU-FindBinUtils.cmake:30 (find_program)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCCompiler.cmake:201 (include)"
      - "CMakeLists.txt"
    mode: "program"
    variable: "CMAKE_C_COMPILER_RANLIB"
    description: "A wrapper around 'ranlib' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcc-ranlib-11.2"
      - "gcc-ranlib-11"
      - "gcc-ranlib11"
      - "gcc-ranlib"
    candidate_directories:
      - "/opt/rh/devtoolset-11/root/usr/bin/"
      - "/opt/synopsys/vcs/V-2023.12-SP2/bin/"
      - "/opt/synopsys/verdi/V-2023.12-SP2/bin/"
      - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli/"
      - "/home/<USER>/git-extras/bin/"
      - "/home/<USER>/zsh/bin/"
      - "/home/<USER>/zeromq/bin/"
      - "/home/<USER>/yodl/bin/"
      - "/home/<USER>/xsel/bin/"
      - "/home/<USER>/xclip/bin/"
      - "/home/<USER>/vim/bin/"
      - "/home/<USER>/verible/bin/"
      - "/home/<USER>/tmux/bin/"
      - "/home/<USER>/tcl/bin/"
      - "/home/<USER>/readline/bin/"
      - "/home/<USER>/python/bin/"
      - "/home/<USER>/pcre2/bin/"
      - "/home/<USER>/openssl/bin/"
      - "/home/<USER>/oniguruma/bin/"
      - "/home/<USER>/node/bin/"
      - "/home/<USER>/make/bin/"
      - "/home/<USER>/m4/bin/"
      - "/home/<USER>/lua/bin/"
      - "/home/<USER>/llvm/bin/"
      - "/home/<USER>/libtool/bin/"
      - "/home/<USER>/libpsl/bin/"
      - "/home/<USER>/libiconv/bin/"
      - "/home/<USER>/libevent/bin/"
      - "/home/<USER>/iverilog/bin/"
      - "/home/<USER>/imlib2/bin/"
      - "/home/<USER>/graphviz/bin/"
      - "/home/<USER>/gperf/bin/"
      - "/home/<USER>/go/bin/"
      - "/home/<USER>/global/bin/"
      - "/home/<USER>/git/bin/"
      - "/home/<USER>/giflib/bin/"
      - "/home/<USER>/gettext/bin/"
      - "/home/<USER>/flatbuffers/bin/"
      - "/home/<USER>/feh/bin/"
      - "/home/<USER>/dtc/bin/"
      - "/home/<USER>/dropbear/bin/"
      - "/home/<USER>/dropbear/sbin/"
      - "/home/<USER>/doxygen/bin/"
      - "/home/<USER>/curl/bin/"
      - "/home/<USER>/ctags/bin/"
      - "/home/<USER>/cmake/bin/"
      - "/home/<USER>/bitwise/bin/"
      - "/home/<USER>/bear/bin/"
      - "/home/<USER>/automake/bin/"
      - "/home/<USER>/autoconf/bin/"
      - "/home/<USER>/local/bin/"
      - "/home/<USER>/.cargo/bin/"
      - "/trunk/go/bin/"
      - "/usr/local/bin/"
      - "/usr/bin/"
      - "/home/<USER>/.fzf/bin/"
    searched_directories:
      - "/opt/rh/devtoolset-11/root/usr/bin/gcc-ranlib-11.2"
      - "/opt/synopsys/vcs/V-2023.12-SP2/bin/gcc-ranlib-11.2"
      - "/opt/synopsys/verdi/V-2023.12-SP2/bin/gcc-ranlib-11.2"
      - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli/gcc-ranlib-11.2"
      - "/home/<USER>/git-extras/bin/gcc-ranlib-11.2"
      - "/home/<USER>/zsh/bin/gcc-ranlib-11.2"
      - "/home/<USER>/zeromq/bin/gcc-ranlib-11.2"
      - "/home/<USER>/yodl/bin/gcc-ranlib-11.2"
      - "/home/<USER>/xsel/bin/gcc-ranlib-11.2"
      - "/home/<USER>/xclip/bin/gcc-ranlib-11.2"
      - "/home/<USER>/vim/bin/gcc-ranlib-11.2"
      - "/home/<USER>/verible/bin/gcc-ranlib-11.2"
      - "/home/<USER>/tmux/bin/gcc-ranlib-11.2"
      - "/home/<USER>/tcl/bin/gcc-ranlib-11.2"
      - "/home/<USER>/readline/bin/gcc-ranlib-11.2"
      - "/home/<USER>/python/bin/gcc-ranlib-11.2"
      - "/home/<USER>/pcre2/bin/gcc-ranlib-11.2"
      - "/home/<USER>/openssl/bin/gcc-ranlib-11.2"
      - "/home/<USER>/oniguruma/bin/gcc-ranlib-11.2"
      - "/home/<USER>/node/bin/gcc-ranlib-11.2"
      - "/home/<USER>/make/bin/gcc-ranlib-11.2"
      - "/home/<USER>/m4/bin/gcc-ranlib-11.2"
      - "/home/<USER>/lua/bin/gcc-ranlib-11.2"
      - "/home/<USER>/llvm/bin/gcc-ranlib-11.2"
      - "/home/<USER>/libtool/bin/gcc-ranlib-11.2"
      - "/home/<USER>/libpsl/bin/gcc-ranlib-11.2"
      - "/home/<USER>/libiconv/bin/gcc-ranlib-11.2"
      - "/home/<USER>/libevent/bin/gcc-ranlib-11.2"
      - "/home/<USER>/iverilog/bin/gcc-ranlib-11.2"
      - "/home/<USER>/imlib2/bin/gcc-ranlib-11.2"
      - "/home/<USER>/graphviz/bin/gcc-ranlib-11.2"
      - "/home/<USER>/gperf/bin/gcc-ranlib-11.2"
      - "/home/<USER>/go/bin/gcc-ranlib-11.2"
      - "/home/<USER>/global/bin/gcc-ranlib-11.2"
      - "/home/<USER>/git/bin/gcc-ranlib-11.2"
      - "/home/<USER>/giflib/bin/gcc-ranlib-11.2"
      - "/home/<USER>/gettext/bin/gcc-ranlib-11.2"
      - "/home/<USER>/flatbuffers/bin/gcc-ranlib-11.2"
      - "/home/<USER>/feh/bin/gcc-ranlib-11.2"
      - "/home/<USER>/dtc/bin/gcc-ranlib-11.2"
      - "/home/<USER>/dropbear/bin/gcc-ranlib-11.2"
      - "/home/<USER>/dropbear/sbin/gcc-ranlib-11.2"
      - "/home/<USER>/doxygen/bin/gcc-ranlib-11.2"
      - "/home/<USER>/curl/bin/gcc-ranlib-11.2"
      - "/home/<USER>/ctags/bin/gcc-ranlib-11.2"
      - "/home/<USER>/cmake/bin/gcc-ranlib-11.2"
      - "/home/<USER>/bitwise/bin/gcc-ranlib-11.2"
      - "/home/<USER>/bear/bin/gcc-ranlib-11.2"
      - "/home/<USER>/automake/bin/gcc-ranlib-11.2"
      - "/home/<USER>/autoconf/bin/gcc-ranlib-11.2"
      - "/home/<USER>/local/bin/gcc-ranlib-11.2"
      - "/home/<USER>/.cargo/bin/gcc-ranlib-11.2"
      - "/trunk/go/bin/gcc-ranlib-11.2"
      - "/usr/local/bin/gcc-ranlib-11.2"
      - "/usr/bin/gcc-ranlib-11.2"
      - "/home/<USER>/.fzf/bin/gcc-ranlib-11.2"
      - "/opt/rh/devtoolset-11/root/usr/bin/gcc-ranlib-11"
      - "/opt/synopsys/vcs/V-2023.12-SP2/bin/gcc-ranlib-11"
      - "/opt/synopsys/verdi/V-2023.12-SP2/bin/gcc-ranlib-11"
      - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli/gcc-ranlib-11"
      - "/home/<USER>/git-extras/bin/gcc-ranlib-11"
      - "/home/<USER>/zsh/bin/gcc-ranlib-11"
      - "/home/<USER>/zeromq/bin/gcc-ranlib-11"
      - "/home/<USER>/yodl/bin/gcc-ranlib-11"
      - "/home/<USER>/xsel/bin/gcc-ranlib-11"
      - "/home/<USER>/xclip/bin/gcc-ranlib-11"
      - "/home/<USER>/vim/bin/gcc-ranlib-11"
      - "/home/<USER>/verible/bin/gcc-ranlib-11"
      - "/home/<USER>/tmux/bin/gcc-ranlib-11"
      - "/home/<USER>/tcl/bin/gcc-ranlib-11"
      - "/home/<USER>/readline/bin/gcc-ranlib-11"
      - "/home/<USER>/python/bin/gcc-ranlib-11"
      - "/home/<USER>/pcre2/bin/gcc-ranlib-11"
      - "/home/<USER>/openssl/bin/gcc-ranlib-11"
      - "/home/<USER>/oniguruma/bin/gcc-ranlib-11"
      - "/home/<USER>/node/bin/gcc-ranlib-11"
      - "/home/<USER>/make/bin/gcc-ranlib-11"
      - "/home/<USER>/m4/bin/gcc-ranlib-11"
      - "/home/<USER>/lua/bin/gcc-ranlib-11"
      - "/home/<USER>/llvm/bin/gcc-ranlib-11"
      - "/home/<USER>/libtool/bin/gcc-ranlib-11"
      - "/home/<USER>/libpsl/bin/gcc-ranlib-11"
      - "/home/<USER>/libiconv/bin/gcc-ranlib-11"
      - "/home/<USER>/libevent/bin/gcc-ranlib-11"
      - "/home/<USER>/iverilog/bin/gcc-ranlib-11"
      - "/home/<USER>/imlib2/bin/gcc-ranlib-11"
      - "/home/<USER>/graphviz/bin/gcc-ranlib-11"
      - "/home/<USER>/gperf/bin/gcc-ranlib-11"
      - "/home/<USER>/go/bin/gcc-ranlib-11"
      - "/home/<USER>/global/bin/gcc-ranlib-11"
      - "/home/<USER>/git/bin/gcc-ranlib-11"
      - "/home/<USER>/giflib/bin/gcc-ranlib-11"
      - "/home/<USER>/gettext/bin/gcc-ranlib-11"
      - "/home/<USER>/flatbuffers/bin/gcc-ranlib-11"
      - "/home/<USER>/feh/bin/gcc-ranlib-11"
      - "/home/<USER>/dtc/bin/gcc-ranlib-11"
      - "/home/<USER>/dropbear/bin/gcc-ranlib-11"
      - "/home/<USER>/dropbear/sbin/gcc-ranlib-11"
      - "/home/<USER>/doxygen/bin/gcc-ranlib-11"
      - "/home/<USER>/curl/bin/gcc-ranlib-11"
      - "/home/<USER>/ctags/bin/gcc-ranlib-11"
      - "/home/<USER>/cmake/bin/gcc-ranlib-11"
      - "/home/<USER>/bitwise/bin/gcc-ranlib-11"
      - "/home/<USER>/bear/bin/gcc-ranlib-11"
      - "/home/<USER>/automake/bin/gcc-ranlib-11"
      - "/home/<USER>/autoconf/bin/gcc-ranlib-11"
      - "/home/<USER>/local/bin/gcc-ranlib-11"
      - "/home/<USER>/.cargo/bin/gcc-ranlib-11"
      - "/trunk/go/bin/gcc-ranlib-11"
      - "/usr/local/bin/gcc-ranlib-11"
      - "/usr/bin/gcc-ranlib-11"
      - "/home/<USER>/.fzf/bin/gcc-ranlib-11"
      - "/opt/rh/devtoolset-11/root/usr/bin/gcc-ranlib11"
      - "/opt/synopsys/vcs/V-2023.12-SP2/bin/gcc-ranlib11"
      - "/opt/synopsys/verdi/V-2023.12-SP2/bin/gcc-ranlib11"
      - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli/gcc-ranlib11"
      - "/home/<USER>/git-extras/bin/gcc-ranlib11"
      - "/home/<USER>/zsh/bin/gcc-ranlib11"
      - "/home/<USER>/zeromq/bin/gcc-ranlib11"
      - "/home/<USER>/yodl/bin/gcc-ranlib11"
      - "/home/<USER>/xsel/bin/gcc-ranlib11"
      - "/home/<USER>/xclip/bin/gcc-ranlib11"
      - "/home/<USER>/vim/bin/gcc-ranlib11"
      - "/home/<USER>/verible/bin/gcc-ranlib11"
      - "/home/<USER>/tmux/bin/gcc-ranlib11"
      - "/home/<USER>/tcl/bin/gcc-ranlib11"
      - "/home/<USER>/readline/bin/gcc-ranlib11"
      - "/home/<USER>/python/bin/gcc-ranlib11"
      - "/home/<USER>/pcre2/bin/gcc-ranlib11"
      - "/home/<USER>/openssl/bin/gcc-ranlib11"
      - "/home/<USER>/oniguruma/bin/gcc-ranlib11"
      - "/home/<USER>/node/bin/gcc-ranlib11"
      - "/home/<USER>/make/bin/gcc-ranlib11"
      - "/home/<USER>/m4/bin/gcc-ranlib11"
      - "/home/<USER>/lua/bin/gcc-ranlib11"
      - "/home/<USER>/llvm/bin/gcc-ranlib11"
      - "/home/<USER>/libtool/bin/gcc-ranlib11"
      - "/home/<USER>/libpsl/bin/gcc-ranlib11"
      - "/home/<USER>/libiconv/bin/gcc-ranlib11"
      - "/home/<USER>/libevent/bin/gcc-ranlib11"
      - "/home/<USER>/iverilog/bin/gcc-ranlib11"
      - "/home/<USER>/imlib2/bin/gcc-ranlib11"
      - "/home/<USER>/graphviz/bin/gcc-ranlib11"
      - "/home/<USER>/gperf/bin/gcc-ranlib11"
      - "/home/<USER>/go/bin/gcc-ranlib11"
      - "/home/<USER>/global/bin/gcc-ranlib11"
      - "/home/<USER>/git/bin/gcc-ranlib11"
      - "/home/<USER>/giflib/bin/gcc-ranlib11"
      - "/home/<USER>/gettext/bin/gcc-ranlib11"
      - "/home/<USER>/flatbuffers/bin/gcc-ranlib11"
      - "/home/<USER>/feh/bin/gcc-ranlib11"
      - "/home/<USER>/dtc/bin/gcc-ranlib11"
      - "/home/<USER>/dropbear/bin/gcc-ranlib11"
      - "/home/<USER>/dropbear/sbin/gcc-ranlib11"
      - "/home/<USER>/doxygen/bin/gcc-ranlib11"
      - "/home/<USER>/curl/bin/gcc-ranlib11"
      - "/home/<USER>/ctags/bin/gcc-ranlib11"
      - "/home/<USER>/cmake/bin/gcc-ranlib11"
      - "/home/<USER>/bitwise/bin/gcc-ranlib11"
      - "/home/<USER>/bear/bin/gcc-ranlib11"
      - "/home/<USER>/automake/bin/gcc-ranlib11"
      - "/home/<USER>/autoconf/bin/gcc-ranlib11"
      - "/home/<USER>/local/bin/gcc-ranlib11"
      - "/home/<USER>/.cargo/bin/gcc-ranlib11"
      - "/trunk/go/bin/gcc-ranlib11"
      - "/usr/local/bin/gcc-ranlib11"
      - "/usr/bin/gcc-ranlib11"
      - "/home/<USER>/.fzf/bin/gcc-ranlib11"
    found: "/opt/rh/devtoolset-11/root/usr/bin/gcc-ranlib"
    search_context:
      ENV{PATH}:
        - "/opt/rh/devtoolset-11/root/usr/bin"
        - "/opt/synopsys/vcs/V-2023.12-SP2/bin"
        - "/opt/synopsys/verdi/V-2023.12-SP2/bin"
        - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli"
        - "/home/<USER>/git-extras/bin"
        - "/home/<USER>/zsh/bin"
        - "/home/<USER>/zeromq/bin"
        - "/home/<USER>/yodl/bin"
        - "/home/<USER>/xsel/bin"
        - "/home/<USER>/xclip/bin"
        - "/home/<USER>/vim/bin"
        - "/home/<USER>/verible/bin"
        - "/home/<USER>/tmux/bin"
        - "/home/<USER>/tcl/bin"
        - "/home/<USER>/readline/bin"
        - "/home/<USER>/python/bin"
        - "/home/<USER>/pcre2/bin"
        - "/home/<USER>/openssl/bin"
        - "/home/<USER>/oniguruma/bin"
        - "/home/<USER>/node/bin"
        - "/home/<USER>/make/bin"
        - "/home/<USER>/m4/bin"
        - "/home/<USER>/lua/bin"
        - "/home/<USER>/llvm/bin"
        - "/home/<USER>/libtool/bin"
        - "/home/<USER>/libpsl/bin"
        - "/home/<USER>/libiconv/bin"
        - "/home/<USER>/libevent/bin"
        - "/home/<USER>/iverilog/bin"
        - "/home/<USER>/imlib2/bin"
        - "/home/<USER>/graphviz/bin"
        - "/home/<USER>/gperf/bin"
        - "/home/<USER>/go/bin"
        - "/home/<USER>/global/bin"
        - "/home/<USER>/git/bin"
        - "/home/<USER>/giflib/bin"
        - "/home/<USER>/gettext/bin"
        - "/home/<USER>/flatbuffers/bin"
        - "/home/<USER>/feh/bin"
        - "/home/<USER>/dtc/bin"
        - "/home/<USER>/dropbear/bin"
        - "/home/<USER>/dropbear/sbin"
        - "/home/<USER>/doxygen/bin"
        - "/home/<USER>/curl/bin"
        - "/home/<USER>/ctags/bin"
        - "/home/<USER>/cmake/bin"
        - "/home/<USER>/bitwise/bin"
        - "/home/<USER>/bear/bin"
        - "/home/<USER>/automake/bin"
        - "/home/<USER>/autoconf/bin"
        - "/home/<USER>/local/bin"
        - "/home/<USER>/.cargo/bin"
        - "/trunk/go/bin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/home/<USER>/.fzf/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCompiler.cmake:54 (find_program)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCXXCompiler.cmake:69 (_cmake_find_compiler)"
      - "CMakeLists.txt"
    mode: "program"
    variable: "CMAKE_CXX_COMPILER"
    description: "CXX compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "c++"
      - "CC"
      - "g++"
      - "aCC"
      - "cl"
      - "bcc"
      - "xlC"
      - "icpx"
      - "icx"
      - "clang++"
    candidate_directories:
      - "/opt/rh/devtoolset-11/root/usr/bin/"
    found: "/opt/rh/devtoolset-11/root/usr/bin/c++"
    search_context:
      ENV{PATH}:
        - "/opt/rh/devtoolset-11/root/usr/bin"
        - "/opt/synopsys/vcs/V-2023.12-SP2/bin"
        - "/opt/synopsys/verdi/V-2023.12-SP2/bin"
        - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli"
        - "/home/<USER>/git-extras/bin"
        - "/home/<USER>/zsh/bin"
        - "/home/<USER>/zeromq/bin"
        - "/home/<USER>/yodl/bin"
        - "/home/<USER>/xsel/bin"
        - "/home/<USER>/xclip/bin"
        - "/home/<USER>/vim/bin"
        - "/home/<USER>/verible/bin"
        - "/home/<USER>/tmux/bin"
        - "/home/<USER>/tcl/bin"
        - "/home/<USER>/readline/bin"
        - "/home/<USER>/python/bin"
        - "/home/<USER>/pcre2/bin"
        - "/home/<USER>/openssl/bin"
        - "/home/<USER>/oniguruma/bin"
        - "/home/<USER>/node/bin"
        - "/home/<USER>/make/bin"
        - "/home/<USER>/m4/bin"
        - "/home/<USER>/lua/bin"
        - "/home/<USER>/llvm/bin"
        - "/home/<USER>/libtool/bin"
        - "/home/<USER>/libpsl/bin"
        - "/home/<USER>/libiconv/bin"
        - "/home/<USER>/libevent/bin"
        - "/home/<USER>/iverilog/bin"
        - "/home/<USER>/imlib2/bin"
        - "/home/<USER>/graphviz/bin"
        - "/home/<USER>/gperf/bin"
        - "/home/<USER>/go/bin"
        - "/home/<USER>/global/bin"
        - "/home/<USER>/git/bin"
        - "/home/<USER>/giflib/bin"
        - "/home/<USER>/gettext/bin"
        - "/home/<USER>/flatbuffers/bin"
        - "/home/<USER>/feh/bin"
        - "/home/<USER>/dtc/bin"
        - "/home/<USER>/dropbear/bin"
        - "/home/<USER>/dropbear/sbin"
        - "/home/<USER>/doxygen/bin"
        - "/home/<USER>/curl/bin"
        - "/home/<USER>/ctags/bin"
        - "/home/<USER>/cmake/bin"
        - "/home/<USER>/bitwise/bin"
        - "/home/<USER>/bear/bin"
        - "/home/<USER>/automake/bin"
        - "/home/<USER>/autoconf/bin"
        - "/home/<USER>/local/bin"
        - "/home/<USER>/.cargo/bin"
        - "/trunk/go/bin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/home/<USER>/.fzf/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/"
    found: "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "/opt/rh/devtoolset-11/root/usr/bin"
        - "/opt/synopsys/vcs/V-2023.12-SP2/bin"
        - "/opt/synopsys/verdi/V-2023.12-SP2/bin"
        - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli"
        - "/home/<USER>/git-extras/bin"
        - "/home/<USER>/zsh/bin"
        - "/home/<USER>/zeromq/bin"
        - "/home/<USER>/yodl/bin"
        - "/home/<USER>/xsel/bin"
        - "/home/<USER>/xclip/bin"
        - "/home/<USER>/vim/bin"
        - "/home/<USER>/verible/bin"
        - "/home/<USER>/tmux/bin"
        - "/home/<USER>/tcl/bin"
        - "/home/<USER>/readline/bin"
        - "/home/<USER>/python/bin"
        - "/home/<USER>/pcre2/bin"
        - "/home/<USER>/openssl/bin"
        - "/home/<USER>/oniguruma/bin"
        - "/home/<USER>/node/bin"
        - "/home/<USER>/make/bin"
        - "/home/<USER>/m4/bin"
        - "/home/<USER>/lua/bin"
        - "/home/<USER>/llvm/bin"
        - "/home/<USER>/libtool/bin"
        - "/home/<USER>/libpsl/bin"
        - "/home/<USER>/libiconv/bin"
        - "/home/<USER>/libevent/bin"
        - "/home/<USER>/iverilog/bin"
        - "/home/<USER>/imlib2/bin"
        - "/home/<USER>/graphviz/bin"
        - "/home/<USER>/gperf/bin"
        - "/home/<USER>/go/bin"
        - "/home/<USER>/global/bin"
        - "/home/<USER>/git/bin"
        - "/home/<USER>/giflib/bin"
        - "/home/<USER>/gettext/bin"
        - "/home/<USER>/flatbuffers/bin"
        - "/home/<USER>/feh/bin"
        - "/home/<USER>/dtc/bin"
        - "/home/<USER>/dropbear/bin"
        - "/home/<USER>/dropbear/sbin"
        - "/home/<USER>/doxygen/bin"
        - "/home/<USER>/curl/bin"
        - "/home/<USER>/ctags/bin"
        - "/home/<USER>/cmake/bin"
        - "/home/<USER>/bitwise/bin"
        - "/home/<USER>/bear/bin"
        - "/home/<USER>/automake/bin"
        - "/home/<USER>/autoconf/bin"
        - "/home/<USER>/local/bin"
        - "/home/<USER>/.cargo/bin"
        - "/trunk/go/bin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/home/<USER>/.fzf/bin"
  -
    kind: "message-v1"
    backtrace:
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: /opt/rh/devtoolset-11/root/usr/bin/c++ 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.out"
      
      The CXX compiler identification is GNU, found in:
        /trunk/hj/hybrid_kit_shm-enflame-0311/src/sideband/bridge/build/CMakeFiles/4.0.20250612-git/CompilerIdCXX/a.out
      
  -
    kind: "find-v1"
    backtrace:
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/Compiler/GNU-FindBinUtils.cmake:18 (find_program)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCXXCompiler.cmake:207 (include)"
      - "CMakeLists.txt"
    mode: "program"
    variable: "CMAKE_CXX_COMPILER_AR"
    description: "A wrapper around 'ar' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcc-ar-11.2"
      - "gcc-ar-11"
      - "gcc-ar11"
      - "gcc-ar"
    candidate_directories:
      - "/opt/rh/devtoolset-11/root/usr/bin/"
      - "/opt/synopsys/vcs/V-2023.12-SP2/bin/"
      - "/opt/synopsys/verdi/V-2023.12-SP2/bin/"
      - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli/"
      - "/home/<USER>/git-extras/bin/"
      - "/home/<USER>/zsh/bin/"
      - "/home/<USER>/zeromq/bin/"
      - "/home/<USER>/yodl/bin/"
      - "/home/<USER>/xsel/bin/"
      - "/home/<USER>/xclip/bin/"
      - "/home/<USER>/vim/bin/"
      - "/home/<USER>/verible/bin/"
      - "/home/<USER>/tmux/bin/"
      - "/home/<USER>/tcl/bin/"
      - "/home/<USER>/readline/bin/"
      - "/home/<USER>/python/bin/"
      - "/home/<USER>/pcre2/bin/"
      - "/home/<USER>/openssl/bin/"
      - "/home/<USER>/oniguruma/bin/"
      - "/home/<USER>/node/bin/"
      - "/home/<USER>/make/bin/"
      - "/home/<USER>/m4/bin/"
      - "/home/<USER>/lua/bin/"
      - "/home/<USER>/llvm/bin/"
      - "/home/<USER>/libtool/bin/"
      - "/home/<USER>/libpsl/bin/"
      - "/home/<USER>/libiconv/bin/"
      - "/home/<USER>/libevent/bin/"
      - "/home/<USER>/iverilog/bin/"
      - "/home/<USER>/imlib2/bin/"
      - "/home/<USER>/graphviz/bin/"
      - "/home/<USER>/gperf/bin/"
      - "/home/<USER>/go/bin/"
      - "/home/<USER>/global/bin/"
      - "/home/<USER>/git/bin/"
      - "/home/<USER>/giflib/bin/"
      - "/home/<USER>/gettext/bin/"
      - "/home/<USER>/flatbuffers/bin/"
      - "/home/<USER>/feh/bin/"
      - "/home/<USER>/dtc/bin/"
      - "/home/<USER>/dropbear/bin/"
      - "/home/<USER>/dropbear/sbin/"
      - "/home/<USER>/doxygen/bin/"
      - "/home/<USER>/curl/bin/"
      - "/home/<USER>/ctags/bin/"
      - "/home/<USER>/cmake/bin/"
      - "/home/<USER>/bitwise/bin/"
      - "/home/<USER>/bear/bin/"
      - "/home/<USER>/automake/bin/"
      - "/home/<USER>/autoconf/bin/"
      - "/home/<USER>/local/bin/"
      - "/home/<USER>/.cargo/bin/"
      - "/trunk/go/bin/"
      - "/usr/local/bin/"
      - "/usr/bin/"
      - "/home/<USER>/.fzf/bin/"
    searched_directories:
      - "/opt/rh/devtoolset-11/root/usr/bin/gcc-ar-11.2"
      - "/opt/synopsys/vcs/V-2023.12-SP2/bin/gcc-ar-11.2"
      - "/opt/synopsys/verdi/V-2023.12-SP2/bin/gcc-ar-11.2"
      - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli/gcc-ar-11.2"
      - "/home/<USER>/git-extras/bin/gcc-ar-11.2"
      - "/home/<USER>/zsh/bin/gcc-ar-11.2"
      - "/home/<USER>/zeromq/bin/gcc-ar-11.2"
      - "/home/<USER>/yodl/bin/gcc-ar-11.2"
      - "/home/<USER>/xsel/bin/gcc-ar-11.2"
      - "/home/<USER>/xclip/bin/gcc-ar-11.2"
      - "/home/<USER>/vim/bin/gcc-ar-11.2"
      - "/home/<USER>/verible/bin/gcc-ar-11.2"
      - "/home/<USER>/tmux/bin/gcc-ar-11.2"
      - "/home/<USER>/tcl/bin/gcc-ar-11.2"
      - "/home/<USER>/readline/bin/gcc-ar-11.2"
      - "/home/<USER>/python/bin/gcc-ar-11.2"
      - "/home/<USER>/pcre2/bin/gcc-ar-11.2"
      - "/home/<USER>/openssl/bin/gcc-ar-11.2"
      - "/home/<USER>/oniguruma/bin/gcc-ar-11.2"
      - "/home/<USER>/node/bin/gcc-ar-11.2"
      - "/home/<USER>/make/bin/gcc-ar-11.2"
      - "/home/<USER>/m4/bin/gcc-ar-11.2"
      - "/home/<USER>/lua/bin/gcc-ar-11.2"
      - "/home/<USER>/llvm/bin/gcc-ar-11.2"
      - "/home/<USER>/libtool/bin/gcc-ar-11.2"
      - "/home/<USER>/libpsl/bin/gcc-ar-11.2"
      - "/home/<USER>/libiconv/bin/gcc-ar-11.2"
      - "/home/<USER>/libevent/bin/gcc-ar-11.2"
      - "/home/<USER>/iverilog/bin/gcc-ar-11.2"
      - "/home/<USER>/imlib2/bin/gcc-ar-11.2"
      - "/home/<USER>/graphviz/bin/gcc-ar-11.2"
      - "/home/<USER>/gperf/bin/gcc-ar-11.2"
      - "/home/<USER>/go/bin/gcc-ar-11.2"
      - "/home/<USER>/global/bin/gcc-ar-11.2"
      - "/home/<USER>/git/bin/gcc-ar-11.2"
      - "/home/<USER>/giflib/bin/gcc-ar-11.2"
      - "/home/<USER>/gettext/bin/gcc-ar-11.2"
      - "/home/<USER>/flatbuffers/bin/gcc-ar-11.2"
      - "/home/<USER>/feh/bin/gcc-ar-11.2"
      - "/home/<USER>/dtc/bin/gcc-ar-11.2"
      - "/home/<USER>/dropbear/bin/gcc-ar-11.2"
      - "/home/<USER>/dropbear/sbin/gcc-ar-11.2"
      - "/home/<USER>/doxygen/bin/gcc-ar-11.2"
      - "/home/<USER>/curl/bin/gcc-ar-11.2"
      - "/home/<USER>/ctags/bin/gcc-ar-11.2"
      - "/home/<USER>/cmake/bin/gcc-ar-11.2"
      - "/home/<USER>/bitwise/bin/gcc-ar-11.2"
      - "/home/<USER>/bear/bin/gcc-ar-11.2"
      - "/home/<USER>/automake/bin/gcc-ar-11.2"
      - "/home/<USER>/autoconf/bin/gcc-ar-11.2"
      - "/home/<USER>/local/bin/gcc-ar-11.2"
      - "/home/<USER>/.cargo/bin/gcc-ar-11.2"
      - "/trunk/go/bin/gcc-ar-11.2"
      - "/usr/local/bin/gcc-ar-11.2"
      - "/usr/bin/gcc-ar-11.2"
      - "/home/<USER>/.fzf/bin/gcc-ar-11.2"
      - "/opt/rh/devtoolset-11/root/usr/bin/gcc-ar-11"
      - "/opt/synopsys/vcs/V-2023.12-SP2/bin/gcc-ar-11"
      - "/opt/synopsys/verdi/V-2023.12-SP2/bin/gcc-ar-11"
      - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli/gcc-ar-11"
      - "/home/<USER>/git-extras/bin/gcc-ar-11"
      - "/home/<USER>/zsh/bin/gcc-ar-11"
      - "/home/<USER>/zeromq/bin/gcc-ar-11"
      - "/home/<USER>/yodl/bin/gcc-ar-11"
      - "/home/<USER>/xsel/bin/gcc-ar-11"
      - "/home/<USER>/xclip/bin/gcc-ar-11"
      - "/home/<USER>/vim/bin/gcc-ar-11"
      - "/home/<USER>/verible/bin/gcc-ar-11"
      - "/home/<USER>/tmux/bin/gcc-ar-11"
      - "/home/<USER>/tcl/bin/gcc-ar-11"
      - "/home/<USER>/readline/bin/gcc-ar-11"
      - "/home/<USER>/python/bin/gcc-ar-11"
      - "/home/<USER>/pcre2/bin/gcc-ar-11"
      - "/home/<USER>/openssl/bin/gcc-ar-11"
      - "/home/<USER>/oniguruma/bin/gcc-ar-11"
      - "/home/<USER>/node/bin/gcc-ar-11"
      - "/home/<USER>/make/bin/gcc-ar-11"
      - "/home/<USER>/m4/bin/gcc-ar-11"
      - "/home/<USER>/lua/bin/gcc-ar-11"
      - "/home/<USER>/llvm/bin/gcc-ar-11"
      - "/home/<USER>/libtool/bin/gcc-ar-11"
      - "/home/<USER>/libpsl/bin/gcc-ar-11"
      - "/home/<USER>/libiconv/bin/gcc-ar-11"
      - "/home/<USER>/libevent/bin/gcc-ar-11"
      - "/home/<USER>/iverilog/bin/gcc-ar-11"
      - "/home/<USER>/imlib2/bin/gcc-ar-11"
      - "/home/<USER>/graphviz/bin/gcc-ar-11"
      - "/home/<USER>/gperf/bin/gcc-ar-11"
      - "/home/<USER>/go/bin/gcc-ar-11"
      - "/home/<USER>/global/bin/gcc-ar-11"
      - "/home/<USER>/git/bin/gcc-ar-11"
      - "/home/<USER>/giflib/bin/gcc-ar-11"
      - "/home/<USER>/gettext/bin/gcc-ar-11"
      - "/home/<USER>/flatbuffers/bin/gcc-ar-11"
      - "/home/<USER>/feh/bin/gcc-ar-11"
      - "/home/<USER>/dtc/bin/gcc-ar-11"
      - "/home/<USER>/dropbear/bin/gcc-ar-11"
      - "/home/<USER>/dropbear/sbin/gcc-ar-11"
      - "/home/<USER>/doxygen/bin/gcc-ar-11"
      - "/home/<USER>/curl/bin/gcc-ar-11"
      - "/home/<USER>/ctags/bin/gcc-ar-11"
      - "/home/<USER>/cmake/bin/gcc-ar-11"
      - "/home/<USER>/bitwise/bin/gcc-ar-11"
      - "/home/<USER>/bear/bin/gcc-ar-11"
      - "/home/<USER>/automake/bin/gcc-ar-11"
      - "/home/<USER>/autoconf/bin/gcc-ar-11"
      - "/home/<USER>/local/bin/gcc-ar-11"
      - "/home/<USER>/.cargo/bin/gcc-ar-11"
      - "/trunk/go/bin/gcc-ar-11"
      - "/usr/local/bin/gcc-ar-11"
      - "/usr/bin/gcc-ar-11"
      - "/home/<USER>/.fzf/bin/gcc-ar-11"
      - "/opt/rh/devtoolset-11/root/usr/bin/gcc-ar11"
      - "/opt/synopsys/vcs/V-2023.12-SP2/bin/gcc-ar11"
      - "/opt/synopsys/verdi/V-2023.12-SP2/bin/gcc-ar11"
      - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli/gcc-ar11"
      - "/home/<USER>/git-extras/bin/gcc-ar11"
      - "/home/<USER>/zsh/bin/gcc-ar11"
      - "/home/<USER>/zeromq/bin/gcc-ar11"
      - "/home/<USER>/yodl/bin/gcc-ar11"
      - "/home/<USER>/xsel/bin/gcc-ar11"
      - "/home/<USER>/xclip/bin/gcc-ar11"
      - "/home/<USER>/vim/bin/gcc-ar11"
      - "/home/<USER>/verible/bin/gcc-ar11"
      - "/home/<USER>/tmux/bin/gcc-ar11"
      - "/home/<USER>/tcl/bin/gcc-ar11"
      - "/home/<USER>/readline/bin/gcc-ar11"
      - "/home/<USER>/python/bin/gcc-ar11"
      - "/home/<USER>/pcre2/bin/gcc-ar11"
      - "/home/<USER>/openssl/bin/gcc-ar11"
      - "/home/<USER>/oniguruma/bin/gcc-ar11"
      - "/home/<USER>/node/bin/gcc-ar11"
      - "/home/<USER>/make/bin/gcc-ar11"
      - "/home/<USER>/m4/bin/gcc-ar11"
      - "/home/<USER>/lua/bin/gcc-ar11"
      - "/home/<USER>/llvm/bin/gcc-ar11"
      - "/home/<USER>/libtool/bin/gcc-ar11"
      - "/home/<USER>/libpsl/bin/gcc-ar11"
      - "/home/<USER>/libiconv/bin/gcc-ar11"
      - "/home/<USER>/libevent/bin/gcc-ar11"
      - "/home/<USER>/iverilog/bin/gcc-ar11"
      - "/home/<USER>/imlib2/bin/gcc-ar11"
      - "/home/<USER>/graphviz/bin/gcc-ar11"
      - "/home/<USER>/gperf/bin/gcc-ar11"
      - "/home/<USER>/go/bin/gcc-ar11"
      - "/home/<USER>/global/bin/gcc-ar11"
      - "/home/<USER>/git/bin/gcc-ar11"
      - "/home/<USER>/giflib/bin/gcc-ar11"
      - "/home/<USER>/gettext/bin/gcc-ar11"
      - "/home/<USER>/flatbuffers/bin/gcc-ar11"
      - "/home/<USER>/feh/bin/gcc-ar11"
      - "/home/<USER>/dtc/bin/gcc-ar11"
      - "/home/<USER>/dropbear/bin/gcc-ar11"
      - "/home/<USER>/dropbear/sbin/gcc-ar11"
      - "/home/<USER>/doxygen/bin/gcc-ar11"
      - "/home/<USER>/curl/bin/gcc-ar11"
      - "/home/<USER>/ctags/bin/gcc-ar11"
      - "/home/<USER>/cmake/bin/gcc-ar11"
      - "/home/<USER>/bitwise/bin/gcc-ar11"
      - "/home/<USER>/bear/bin/gcc-ar11"
      - "/home/<USER>/automake/bin/gcc-ar11"
      - "/home/<USER>/autoconf/bin/gcc-ar11"
      - "/home/<USER>/local/bin/gcc-ar11"
      - "/home/<USER>/.cargo/bin/gcc-ar11"
      - "/trunk/go/bin/gcc-ar11"
      - "/usr/local/bin/gcc-ar11"
      - "/usr/bin/gcc-ar11"
      - "/home/<USER>/.fzf/bin/gcc-ar11"
    found: "/opt/rh/devtoolset-11/root/usr/bin/gcc-ar"
    search_context:
      ENV{PATH}:
        - "/opt/rh/devtoolset-11/root/usr/bin"
        - "/opt/synopsys/vcs/V-2023.12-SP2/bin"
        - "/opt/synopsys/verdi/V-2023.12-SP2/bin"
        - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli"
        - "/home/<USER>/git-extras/bin"
        - "/home/<USER>/zsh/bin"
        - "/home/<USER>/zeromq/bin"
        - "/home/<USER>/yodl/bin"
        - "/home/<USER>/xsel/bin"
        - "/home/<USER>/xclip/bin"
        - "/home/<USER>/vim/bin"
        - "/home/<USER>/verible/bin"
        - "/home/<USER>/tmux/bin"
        - "/home/<USER>/tcl/bin"
        - "/home/<USER>/readline/bin"
        - "/home/<USER>/python/bin"
        - "/home/<USER>/pcre2/bin"
        - "/home/<USER>/openssl/bin"
        - "/home/<USER>/oniguruma/bin"
        - "/home/<USER>/node/bin"
        - "/home/<USER>/make/bin"
        - "/home/<USER>/m4/bin"
        - "/home/<USER>/lua/bin"
        - "/home/<USER>/llvm/bin"
        - "/home/<USER>/libtool/bin"
        - "/home/<USER>/libpsl/bin"
        - "/home/<USER>/libiconv/bin"
        - "/home/<USER>/libevent/bin"
        - "/home/<USER>/iverilog/bin"
        - "/home/<USER>/imlib2/bin"
        - "/home/<USER>/graphviz/bin"
        - "/home/<USER>/gperf/bin"
        - "/home/<USER>/go/bin"
        - "/home/<USER>/global/bin"
        - "/home/<USER>/git/bin"
        - "/home/<USER>/giflib/bin"
        - "/home/<USER>/gettext/bin"
        - "/home/<USER>/flatbuffers/bin"
        - "/home/<USER>/feh/bin"
        - "/home/<USER>/dtc/bin"
        - "/home/<USER>/dropbear/bin"
        - "/home/<USER>/dropbear/sbin"
        - "/home/<USER>/doxygen/bin"
        - "/home/<USER>/curl/bin"
        - "/home/<USER>/ctags/bin"
        - "/home/<USER>/cmake/bin"
        - "/home/<USER>/bitwise/bin"
        - "/home/<USER>/bear/bin"
        - "/home/<USER>/automake/bin"
        - "/home/<USER>/autoconf/bin"
        - "/home/<USER>/local/bin"
        - "/home/<USER>/.cargo/bin"
        - "/trunk/go/bin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/home/<USER>/.fzf/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/Compiler/GNU-FindBinUtils.cmake:30 (find_program)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCXXCompiler.cmake:207 (include)"
      - "CMakeLists.txt"
    mode: "program"
    variable: "CMAKE_CXX_COMPILER_RANLIB"
    description: "A wrapper around 'ranlib' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcc-ranlib-11.2"
      - "gcc-ranlib-11"
      - "gcc-ranlib11"
      - "gcc-ranlib"
    candidate_directories:
      - "/opt/rh/devtoolset-11/root/usr/bin/"
      - "/opt/synopsys/vcs/V-2023.12-SP2/bin/"
      - "/opt/synopsys/verdi/V-2023.12-SP2/bin/"
      - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli/"
      - "/home/<USER>/git-extras/bin/"
      - "/home/<USER>/zsh/bin/"
      - "/home/<USER>/zeromq/bin/"
      - "/home/<USER>/yodl/bin/"
      - "/home/<USER>/xsel/bin/"
      - "/home/<USER>/xclip/bin/"
      - "/home/<USER>/vim/bin/"
      - "/home/<USER>/verible/bin/"
      - "/home/<USER>/tmux/bin/"
      - "/home/<USER>/tcl/bin/"
      - "/home/<USER>/readline/bin/"
      - "/home/<USER>/python/bin/"
      - "/home/<USER>/pcre2/bin/"
      - "/home/<USER>/openssl/bin/"
      - "/home/<USER>/oniguruma/bin/"
      - "/home/<USER>/node/bin/"
      - "/home/<USER>/make/bin/"
      - "/home/<USER>/m4/bin/"
      - "/home/<USER>/lua/bin/"
      - "/home/<USER>/llvm/bin/"
      - "/home/<USER>/libtool/bin/"
      - "/home/<USER>/libpsl/bin/"
      - "/home/<USER>/libiconv/bin/"
      - "/home/<USER>/libevent/bin/"
      - "/home/<USER>/iverilog/bin/"
      - "/home/<USER>/imlib2/bin/"
      - "/home/<USER>/graphviz/bin/"
      - "/home/<USER>/gperf/bin/"
      - "/home/<USER>/go/bin/"
      - "/home/<USER>/global/bin/"
      - "/home/<USER>/git/bin/"
      - "/home/<USER>/giflib/bin/"
      - "/home/<USER>/gettext/bin/"
      - "/home/<USER>/flatbuffers/bin/"
      - "/home/<USER>/feh/bin/"
      - "/home/<USER>/dtc/bin/"
      - "/home/<USER>/dropbear/bin/"
      - "/home/<USER>/dropbear/sbin/"
      - "/home/<USER>/doxygen/bin/"
      - "/home/<USER>/curl/bin/"
      - "/home/<USER>/ctags/bin/"
      - "/home/<USER>/cmake/bin/"
      - "/home/<USER>/bitwise/bin/"
      - "/home/<USER>/bear/bin/"
      - "/home/<USER>/automake/bin/"
      - "/home/<USER>/autoconf/bin/"
      - "/home/<USER>/local/bin/"
      - "/home/<USER>/.cargo/bin/"
      - "/trunk/go/bin/"
      - "/usr/local/bin/"
      - "/usr/bin/"
      - "/home/<USER>/.fzf/bin/"
    searched_directories:
      - "/opt/rh/devtoolset-11/root/usr/bin/gcc-ranlib-11.2"
      - "/opt/synopsys/vcs/V-2023.12-SP2/bin/gcc-ranlib-11.2"
      - "/opt/synopsys/verdi/V-2023.12-SP2/bin/gcc-ranlib-11.2"
      - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli/gcc-ranlib-11.2"
      - "/home/<USER>/git-extras/bin/gcc-ranlib-11.2"
      - "/home/<USER>/zsh/bin/gcc-ranlib-11.2"
      - "/home/<USER>/zeromq/bin/gcc-ranlib-11.2"
      - "/home/<USER>/yodl/bin/gcc-ranlib-11.2"
      - "/home/<USER>/xsel/bin/gcc-ranlib-11.2"
      - "/home/<USER>/xclip/bin/gcc-ranlib-11.2"
      - "/home/<USER>/vim/bin/gcc-ranlib-11.2"
      - "/home/<USER>/verible/bin/gcc-ranlib-11.2"
      - "/home/<USER>/tmux/bin/gcc-ranlib-11.2"
      - "/home/<USER>/tcl/bin/gcc-ranlib-11.2"
      - "/home/<USER>/readline/bin/gcc-ranlib-11.2"
      - "/home/<USER>/python/bin/gcc-ranlib-11.2"
      - "/home/<USER>/pcre2/bin/gcc-ranlib-11.2"
      - "/home/<USER>/openssl/bin/gcc-ranlib-11.2"
      - "/home/<USER>/oniguruma/bin/gcc-ranlib-11.2"
      - "/home/<USER>/node/bin/gcc-ranlib-11.2"
      - "/home/<USER>/make/bin/gcc-ranlib-11.2"
      - "/home/<USER>/m4/bin/gcc-ranlib-11.2"
      - "/home/<USER>/lua/bin/gcc-ranlib-11.2"
      - "/home/<USER>/llvm/bin/gcc-ranlib-11.2"
      - "/home/<USER>/libtool/bin/gcc-ranlib-11.2"
      - "/home/<USER>/libpsl/bin/gcc-ranlib-11.2"
      - "/home/<USER>/libiconv/bin/gcc-ranlib-11.2"
      - "/home/<USER>/libevent/bin/gcc-ranlib-11.2"
      - "/home/<USER>/iverilog/bin/gcc-ranlib-11.2"
      - "/home/<USER>/imlib2/bin/gcc-ranlib-11.2"
      - "/home/<USER>/graphviz/bin/gcc-ranlib-11.2"
      - "/home/<USER>/gperf/bin/gcc-ranlib-11.2"
      - "/home/<USER>/go/bin/gcc-ranlib-11.2"
      - "/home/<USER>/global/bin/gcc-ranlib-11.2"
      - "/home/<USER>/git/bin/gcc-ranlib-11.2"
      - "/home/<USER>/giflib/bin/gcc-ranlib-11.2"
      - "/home/<USER>/gettext/bin/gcc-ranlib-11.2"
      - "/home/<USER>/flatbuffers/bin/gcc-ranlib-11.2"
      - "/home/<USER>/feh/bin/gcc-ranlib-11.2"
      - "/home/<USER>/dtc/bin/gcc-ranlib-11.2"
      - "/home/<USER>/dropbear/bin/gcc-ranlib-11.2"
      - "/home/<USER>/dropbear/sbin/gcc-ranlib-11.2"
      - "/home/<USER>/doxygen/bin/gcc-ranlib-11.2"
      - "/home/<USER>/curl/bin/gcc-ranlib-11.2"
      - "/home/<USER>/ctags/bin/gcc-ranlib-11.2"
      - "/home/<USER>/cmake/bin/gcc-ranlib-11.2"
      - "/home/<USER>/bitwise/bin/gcc-ranlib-11.2"
      - "/home/<USER>/bear/bin/gcc-ranlib-11.2"
      - "/home/<USER>/automake/bin/gcc-ranlib-11.2"
      - "/home/<USER>/autoconf/bin/gcc-ranlib-11.2"
      - "/home/<USER>/local/bin/gcc-ranlib-11.2"
      - "/home/<USER>/.cargo/bin/gcc-ranlib-11.2"
      - "/trunk/go/bin/gcc-ranlib-11.2"
      - "/usr/local/bin/gcc-ranlib-11.2"
      - "/usr/bin/gcc-ranlib-11.2"
      - "/home/<USER>/.fzf/bin/gcc-ranlib-11.2"
      - "/opt/rh/devtoolset-11/root/usr/bin/gcc-ranlib-11"
      - "/opt/synopsys/vcs/V-2023.12-SP2/bin/gcc-ranlib-11"
      - "/opt/synopsys/verdi/V-2023.12-SP2/bin/gcc-ranlib-11"
      - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli/gcc-ranlib-11"
      - "/home/<USER>/git-extras/bin/gcc-ranlib-11"
      - "/home/<USER>/zsh/bin/gcc-ranlib-11"
      - "/home/<USER>/zeromq/bin/gcc-ranlib-11"
      - "/home/<USER>/yodl/bin/gcc-ranlib-11"
      - "/home/<USER>/xsel/bin/gcc-ranlib-11"
      - "/home/<USER>/xclip/bin/gcc-ranlib-11"
      - "/home/<USER>/vim/bin/gcc-ranlib-11"
      - "/home/<USER>/verible/bin/gcc-ranlib-11"
      - "/home/<USER>/tmux/bin/gcc-ranlib-11"
      - "/home/<USER>/tcl/bin/gcc-ranlib-11"
      - "/home/<USER>/readline/bin/gcc-ranlib-11"
      - "/home/<USER>/python/bin/gcc-ranlib-11"
      - "/home/<USER>/pcre2/bin/gcc-ranlib-11"
      - "/home/<USER>/openssl/bin/gcc-ranlib-11"
      - "/home/<USER>/oniguruma/bin/gcc-ranlib-11"
      - "/home/<USER>/node/bin/gcc-ranlib-11"
      - "/home/<USER>/make/bin/gcc-ranlib-11"
      - "/home/<USER>/m4/bin/gcc-ranlib-11"
      - "/home/<USER>/lua/bin/gcc-ranlib-11"
      - "/home/<USER>/llvm/bin/gcc-ranlib-11"
      - "/home/<USER>/libtool/bin/gcc-ranlib-11"
      - "/home/<USER>/libpsl/bin/gcc-ranlib-11"
      - "/home/<USER>/libiconv/bin/gcc-ranlib-11"
      - "/home/<USER>/libevent/bin/gcc-ranlib-11"
      - "/home/<USER>/iverilog/bin/gcc-ranlib-11"
      - "/home/<USER>/imlib2/bin/gcc-ranlib-11"
      - "/home/<USER>/graphviz/bin/gcc-ranlib-11"
      - "/home/<USER>/gperf/bin/gcc-ranlib-11"
      - "/home/<USER>/go/bin/gcc-ranlib-11"
      - "/home/<USER>/global/bin/gcc-ranlib-11"
      - "/home/<USER>/git/bin/gcc-ranlib-11"
      - "/home/<USER>/giflib/bin/gcc-ranlib-11"
      - "/home/<USER>/gettext/bin/gcc-ranlib-11"
      - "/home/<USER>/flatbuffers/bin/gcc-ranlib-11"
      - "/home/<USER>/feh/bin/gcc-ranlib-11"
      - "/home/<USER>/dtc/bin/gcc-ranlib-11"
      - "/home/<USER>/dropbear/bin/gcc-ranlib-11"
      - "/home/<USER>/dropbear/sbin/gcc-ranlib-11"
      - "/home/<USER>/doxygen/bin/gcc-ranlib-11"
      - "/home/<USER>/curl/bin/gcc-ranlib-11"
      - "/home/<USER>/ctags/bin/gcc-ranlib-11"
      - "/home/<USER>/cmake/bin/gcc-ranlib-11"
      - "/home/<USER>/bitwise/bin/gcc-ranlib-11"
      - "/home/<USER>/bear/bin/gcc-ranlib-11"
      - "/home/<USER>/automake/bin/gcc-ranlib-11"
      - "/home/<USER>/autoconf/bin/gcc-ranlib-11"
      - "/home/<USER>/local/bin/gcc-ranlib-11"
      - "/home/<USER>/.cargo/bin/gcc-ranlib-11"
      - "/trunk/go/bin/gcc-ranlib-11"
      - "/usr/local/bin/gcc-ranlib-11"
      - "/usr/bin/gcc-ranlib-11"
      - "/home/<USER>/.fzf/bin/gcc-ranlib-11"
      - "/opt/rh/devtoolset-11/root/usr/bin/gcc-ranlib11"
      - "/opt/synopsys/vcs/V-2023.12-SP2/bin/gcc-ranlib11"
      - "/opt/synopsys/verdi/V-2023.12-SP2/bin/gcc-ranlib11"
      - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli/gcc-ranlib11"
      - "/home/<USER>/git-extras/bin/gcc-ranlib11"
      - "/home/<USER>/zsh/bin/gcc-ranlib11"
      - "/home/<USER>/zeromq/bin/gcc-ranlib11"
      - "/home/<USER>/yodl/bin/gcc-ranlib11"
      - "/home/<USER>/xsel/bin/gcc-ranlib11"
      - "/home/<USER>/xclip/bin/gcc-ranlib11"
      - "/home/<USER>/vim/bin/gcc-ranlib11"
      - "/home/<USER>/verible/bin/gcc-ranlib11"
      - "/home/<USER>/tmux/bin/gcc-ranlib11"
      - "/home/<USER>/tcl/bin/gcc-ranlib11"
      - "/home/<USER>/readline/bin/gcc-ranlib11"
      - "/home/<USER>/python/bin/gcc-ranlib11"
      - "/home/<USER>/pcre2/bin/gcc-ranlib11"
      - "/home/<USER>/openssl/bin/gcc-ranlib11"
      - "/home/<USER>/oniguruma/bin/gcc-ranlib11"
      - "/home/<USER>/node/bin/gcc-ranlib11"
      - "/home/<USER>/make/bin/gcc-ranlib11"
      - "/home/<USER>/m4/bin/gcc-ranlib11"
      - "/home/<USER>/lua/bin/gcc-ranlib11"
      - "/home/<USER>/llvm/bin/gcc-ranlib11"
      - "/home/<USER>/libtool/bin/gcc-ranlib11"
      - "/home/<USER>/libpsl/bin/gcc-ranlib11"
      - "/home/<USER>/libiconv/bin/gcc-ranlib11"
      - "/home/<USER>/libevent/bin/gcc-ranlib11"
      - "/home/<USER>/iverilog/bin/gcc-ranlib11"
      - "/home/<USER>/imlib2/bin/gcc-ranlib11"
      - "/home/<USER>/graphviz/bin/gcc-ranlib11"
      - "/home/<USER>/gperf/bin/gcc-ranlib11"
      - "/home/<USER>/go/bin/gcc-ranlib11"
      - "/home/<USER>/global/bin/gcc-ranlib11"
      - "/home/<USER>/git/bin/gcc-ranlib11"
      - "/home/<USER>/giflib/bin/gcc-ranlib11"
      - "/home/<USER>/gettext/bin/gcc-ranlib11"
      - "/home/<USER>/flatbuffers/bin/gcc-ranlib11"
      - "/home/<USER>/feh/bin/gcc-ranlib11"
      - "/home/<USER>/dtc/bin/gcc-ranlib11"
      - "/home/<USER>/dropbear/bin/gcc-ranlib11"
      - "/home/<USER>/dropbear/sbin/gcc-ranlib11"
      - "/home/<USER>/doxygen/bin/gcc-ranlib11"
      - "/home/<USER>/curl/bin/gcc-ranlib11"
      - "/home/<USER>/ctags/bin/gcc-ranlib11"
      - "/home/<USER>/cmake/bin/gcc-ranlib11"
      - "/home/<USER>/bitwise/bin/gcc-ranlib11"
      - "/home/<USER>/bear/bin/gcc-ranlib11"
      - "/home/<USER>/automake/bin/gcc-ranlib11"
      - "/home/<USER>/autoconf/bin/gcc-ranlib11"
      - "/home/<USER>/local/bin/gcc-ranlib11"
      - "/home/<USER>/.cargo/bin/gcc-ranlib11"
      - "/trunk/go/bin/gcc-ranlib11"
      - "/usr/local/bin/gcc-ranlib11"
      - "/usr/bin/gcc-ranlib11"
      - "/home/<USER>/.fzf/bin/gcc-ranlib11"
    found: "/opt/rh/devtoolset-11/root/usr/bin/gcc-ranlib"
    search_context:
      ENV{PATH}:
        - "/opt/rh/devtoolset-11/root/usr/bin"
        - "/opt/synopsys/vcs/V-2023.12-SP2/bin"
        - "/opt/synopsys/verdi/V-2023.12-SP2/bin"
        - "/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli"
        - "/home/<USER>/git-extras/bin"
        - "/home/<USER>/zsh/bin"
        - "/home/<USER>/zeromq/bin"
        - "/home/<USER>/yodl/bin"
        - "/home/<USER>/xsel/bin"
        - "/home/<USER>/xclip/bin"
        - "/home/<USER>/vim/bin"
        - "/home/<USER>/verible/bin"
        - "/home/<USER>/tmux/bin"
        - "/home/<USER>/tcl/bin"
        - "/home/<USER>/readline/bin"
        - "/home/<USER>/python/bin"
        - "/home/<USER>/pcre2/bin"
        - "/home/<USER>/openssl/bin"
        - "/home/<USER>/oniguruma/bin"
        - "/home/<USER>/node/bin"
        - "/home/<USER>/make/bin"
        - "/home/<USER>/m4/bin"
        - "/home/<USER>/lua/bin"
        - "/home/<USER>/llvm/bin"
        - "/home/<USER>/libtool/bin"
        - "/home/<USER>/libpsl/bin"
        - "/home/<USER>/libiconv/bin"
        - "/home/<USER>/libevent/bin"
        - "/home/<USER>/iverilog/bin"
        - "/home/<USER>/imlib2/bin"
        - "/home/<USER>/graphviz/bin"
        - "/home/<USER>/gperf/bin"
        - "/home/<USER>/go/bin"
        - "/home/<USER>/global/bin"
        - "/home/<USER>/git/bin"
        - "/home/<USER>/giflib/bin"
        - "/home/<USER>/gettext/bin"
        - "/home/<USER>/flatbuffers/bin"
        - "/home/<USER>/feh/bin"
        - "/home/<USER>/dtc/bin"
        - "/home/<USER>/dropbear/bin"
        - "/home/<USER>/dropbear/sbin"
        - "/home/<USER>/doxygen/bin"
        - "/home/<USER>/curl/bin"
        - "/home/<USER>/ctags/bin"
        - "/home/<USER>/cmake/bin"
        - "/home/<USER>/bitwise/bin"
        - "/home/<USER>/bear/bin"
        - "/home/<USER>/automake/bin"
        - "/home/<USER>/autoconf/bin"
        - "/home/<USER>/local/bin"
        - "/home/<USER>/.cargo/bin"
        - "/trunk/go/bin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/home/<USER>/.fzf/bin"
  -
    kind: "try_compile-v1"
    backtrace:
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "/trunk/hj/hybrid_kit_shm-enflame-0311/src/sideband/bridge/build/CMakeFiles/CMakeScratch/TryCompile-jZwEdp"
      binary: "/trunk/hj/hybrid_kit_shm-enflame-0311/src/sideband/bridge/build/CMakeFiles/CMakeScratch/TryCompile-jZwEdp"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/trunk/hj/hybrid_kit_shm-enflame-0311/src/sideband/bridge/build/CMakeFiles/CMakeScratch/TryCompile-jZwEdp'
        
        Run Build Command(s): /home/<USER>/cmake/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_427d8/fast
        /usr/bin/gmake  -f CMakeFiles/cmTC_427d8.dir/build.make CMakeFiles/cmTC_427d8.dir/build
        gmake[1]: Entering directory `/trunk/hj/hybrid_kit_shm-enflame-0311/src/sideband/bridge/build/CMakeFiles/CMakeScratch/TryCompile-jZwEdp'
        Building C object CMakeFiles/cmTC_427d8.dir/CMakeCCompilerABI.c.o
        /opt/rh/devtoolset-11/root/usr/bin/cc   -v -o CMakeFiles/cmTC_427d8.dir/CMakeCCompilerABI.c.o -c /home/<USER>/cmake/share/cmake-4.0/Modules/CMakeCCompilerABI.c
        Using built-in specs.
        COLLECT_GCC=/opt/rh/devtoolset-11/root/usr/bin/cc
        Target: x86_64-redhat-linux
        Configured with: ../configure --enable-bootstrap --enable-languages=c,c++,fortran,lto --prefix=/opt/rh/devtoolset-11/root/usr --mandir=/opt/rh/devtoolset-11/root/usr/share/man --infodir=/opt/rh/devtoolset-11/root/usr/share/info --with-bugurl=http://bugzilla.redhat.com/bugzilla --enable-shared --enable-threads=posix --enable-checking=release --enable-multilib --with-system-zlib --enable-__cxa_atexit --disable-libunwind-exceptions --enable-gnu-unique-object --enable-linker-build-id --with-gcc-major-version-only --with-linker-hash-style=gnu --with-default-libstdcxx-abi=gcc4-compatible --enable-plugin --enable-initfini-array --with-isl=/builddir/build/BUILD/gcc-11.2.1-20220127/obj-x86_64-redhat-linux/isl-install --enable-gnu-indirect-function --with-tune=generic --with-arch_32=x86-64 --build=x86_64-redhat-linux
        Thread model: posix
        Supported LTO compression algorithms: zlib
        gcc version 11.2.1 20220127 (Red Hat 11.2.1-9) (GCC) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_427d8.dir/CMakeCCompilerABI.c.o' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_427d8.dir/'
         /opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/cc1 -quiet -v /home/<USER>/cmake/share/cmake-4.0/Modules/CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles/cmTC_427d8.dir/ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -mtune=generic -march=x86-64 -version -o /tmp/ccQICqKr.s
        GNU C17 (GCC) version 11.2.1 20220127 (Red Hat 11.2.1-9) (x86_64-redhat-linux)
        	compiled by GNU C version 11.2.1 20220127 (Red Hat 11.2.1-9), GMP version 6.0.0, MPFR version 3.1.1, MPC version 1.0.1, isl version isl-0.18-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring nonexistent directory "/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/include-fixed"
        ignoring nonexistent directory "/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../x86_64-redhat-linux/include"
        #include "..." search starts here:
        #include <...> search starts here:
         /opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/include
         /usr/local/include
         /opt/rh/devtoolset-11/root/usr/include
         /usr/include
        End of search list.
        GNU C17 (GCC) version 11.2.1 20220127 (Red Hat 11.2.1-9) (x86_64-redhat-linux)
        	compiled by GNU C version 11.2.1 20220127 (Red Hat 11.2.1-9), GMP version 6.0.0, MPFR version 3.1.1, MPC version 1.0.1, isl version isl-0.18-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        Compiler executable checksum: fcd9572df254446d5004a010508b613e
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_427d8.dir/CMakeCCompilerABI.c.o' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_427d8.dir/'
         /opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/as -v --64 -o CMakeFiles/cmTC_427d8.dir/CMakeCCompilerABI.c.o /tmp/ccQICqKr.s
        GNU assembler version 2.36.1 (x86_64-redhat-linux) using BFD version version 2.36.1-1.el7.2
        COMPILER_PATH=/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/:/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/:/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/:/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/:/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/
        LIBRARY_PATH=/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/:/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../lib64/:/lib/../lib64/:/usr/lib/../lib64/:/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_427d8.dir/CMakeCCompilerABI.c.o' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_427d8.dir/CMakeCCompilerABI.c.'
        Linking C executable cmTC_427d8
        /home/<USER>/cmake/bin/cmake -E cmake_link_script CMakeFiles/cmTC_427d8.dir/link.txt --verbose=1
        Using built-in specs.
        COLLECT_GCC=/opt/rh/devtoolset-11/root/usr/bin/cc
        COLLECT_LTO_WRAPPER=/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/lto-wrapper
        Target: x86_64-redhat-linux
        Configured with: ../configure --enable-bootstrap --enable-languages=c,c++,fortran,lto --prefix=/opt/rh/devtoolset-11/root/usr --mandir=/opt/rh/devtoolset-11/root/usr/share/man --infodir=/opt/rh/devtoolset-11/root/usr/share/info --with-bugurl=http://bugzilla.redhat.com/bugzilla --enable-shared --enable-threads=posix --enable-checking=release --enable-multilib --with-system-zlib --enable-__cxa_atexit --disable-libunwind-exceptions --enable-gnu-unique-object --enable-linker-build-id --with-gcc-major-version-only --with-linker-hash-style=gnu --with-default-libstdcxx-abi=gcc4-compatible --enable-plugin --enable-initfini-array --with-isl=/builddir/build/BUILD/gcc-11.2.1-20220127/obj-x86_64-redhat-linux/isl-install --enable-gnu-indirect-function --with-tune=generic --with-arch_32=x86-64 --build=x86_64-redhat-linux
        Thread model: posix
        Supported LTO compression algorithms: zlib
        gcc version 11.2.1 20220127 (Red Hat 11.2.1-9) (GCC) 
        COMPILER_PATH=/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/:/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/:/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/:/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/:/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/
        LIBRARY_PATH=/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/:/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../lib64/:/lib/../lib64/:/usr/lib/../lib64/:/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_427d8' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_427d8.'
         /opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/collect2 -plugin /opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/liblto_plugin.so -plugin-opt=/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/lto-wrapper -plugin-opt=-fresolution=/tmp/ccI0LFuu.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --build-id --no-add-needed --eh-frame-hdr --hash-style=gnu -m elf_x86_64 -dynamic-linker /lib64/ld-linux-x86-64.so.2 -o cmTC_427d8 /lib/../lib64/crt1.o /lib/../lib64/crti.o /opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/crtbegin.o -L/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11 -L/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../lib64 -L/lib/../lib64 -L/usr/lib/../lib64 -L/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../.. -v CMakeFiles/cmTC_427d8.dir/CMakeCCompilerABI.c.o -lgcc --push-state --as-needed -lgcc_s --pop-state -lc -lgcc --push-state --as-needed -lgcc_s --pop-state /opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/crtend.o /lib/../lib64/crtn.o
        collect2 version 11.2.1 20220127 (Red Hat 11.2.1-9)
        /opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/ld -plugin /opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/liblto_plugin.so -plugin-opt=/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/lto-wrapper -plugin-opt=-fresolution=/tmp/ccI0LFuu.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --build-id --no-add-needed --eh-frame-hdr --hash-style=gnu -m elf_x86_64 -dynamic-linker /lib64/ld-linux-x86-64.so.2 -o cmTC_427d8 /lib/../lib64/crt1.o /lib/../lib64/crti.o /opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/crtbegin.o -L/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11 -L/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../lib64 -L/lib/../lib64 -L/usr/lib/../lib64 -L/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../.. -v CMakeFiles/cmTC_427d8.dir/CMakeCCompilerABI.c.o -lgcc --push-state --as-needed -lgcc_s --pop-state -lc -lgcc --push-state --as-needed -lgcc_s --pop-state /opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/crtend.o /lib/../lib64/crtn.o
        GNU ld version 2.36.1-1.el7.2
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_427d8' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_427d8.'
        /opt/rh/devtoolset-11/root/usr/bin/cc  -v -Wl,-v CMakeFiles/cmTC_427d8.dir/CMakeCCompilerABI.c.o -o cmTC_427d8
        gmake[1]: Leaving directory `/trunk/hj/hybrid_kit_shm-enflame-0311/src/sideband/bridge/build/CMakeFiles/CMakeScratch/TryCompile-jZwEdp'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:217 (message)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/include]
          add: [/usr/local/include]
          add: [/opt/rh/devtoolset-11/root/usr/include]
          add: [/usr/include]
        end of search list found
        collapse include dir [/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/include] ==> [/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/include]
        collapse include dir [/usr/local/include] ==> [/usr/local/include]
        collapse include dir [/opt/rh/devtoolset-11/root/usr/include] ==> [/opt/rh/devtoolset-11/root/usr/include]
        collapse include dir [/usr/include] ==> [/usr/include]
        implicit include dirs: [/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/include;/usr/local/include;/opt/rh/devtoolset-11/root/usr/include;/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: '/trunk/hj/hybrid_kit_shm-enflame-0311/src/sideband/bridge/build/CMakeFiles/CMakeScratch/TryCompile-jZwEdp']
        ignore line: []
        ignore line: [Run Build Command(s): /home/<USER>/cmake/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_427d8/fast]
        ignore line: [/usr/bin/gmake  -f CMakeFiles/cmTC_427d8.dir/build.make CMakeFiles/cmTC_427d8.dir/build]
        ignore line: [gmake[1]: Entering directory `/trunk/hj/hybrid_kit_shm-enflame-0311/src/sideband/bridge/build/CMakeFiles/CMakeScratch/TryCompile-jZwEdp']
        ignore line: [Building C object CMakeFiles/cmTC_427d8.dir/CMakeCCompilerABI.c.o]
        ignore line: [/opt/rh/devtoolset-11/root/usr/bin/cc   -v -o CMakeFiles/cmTC_427d8.dir/CMakeCCompilerABI.c.o -c /home/<USER>/cmake/share/cmake-4.0/Modules/CMakeCCompilerABI.c]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/opt/rh/devtoolset-11/root/usr/bin/cc]
        ignore line: [Target: x86_64-redhat-linux]
        ignore line: [Configured with: ../configure --enable-bootstrap --enable-languages=c c++ fortran lto --prefix=/opt/rh/devtoolset-11/root/usr --mandir=/opt/rh/devtoolset-11/root/usr/share/man --infodir=/opt/rh/devtoolset-11/root/usr/share/info --with-bugurl=http://bugzilla.redhat.com/bugzilla --enable-shared --enable-threads=posix --enable-checking=release --enable-multilib --with-system-zlib --enable-__cxa_atexit --disable-libunwind-exceptions --enable-gnu-unique-object --enable-linker-build-id --with-gcc-major-version-only --with-linker-hash-style=gnu --with-default-libstdcxx-abi=gcc4-compatible --enable-plugin --enable-initfini-array --with-isl=/builddir/build/BUILD/gcc-11.2.1-20220127/obj-x86_64-redhat-linux/isl-install --enable-gnu-indirect-function --with-tune=generic --with-arch_32=x86-64 --build=x86_64-redhat-linux]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib]
        ignore line: [gcc version 11.2.1 20220127 (Red Hat 11.2.1-9) (GCC) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_427d8.dir/CMakeCCompilerABI.c.o' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_427d8.dir/']
        ignore line: [ /opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/cc1 -quiet -v /home/<USER>/cmake/share/cmake-4.0/Modules/CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles/cmTC_427d8.dir/ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -mtune=generic -march=x86-64 -version -o /tmp/ccQICqKr.s]
        ignore line: [GNU C17 (GCC) version 11.2.1 20220127 (Red Hat 11.2.1-9) (x86_64-redhat-linux)]
        ignore line: [	compiled by GNU C version 11.2.1 20220127 (Red Hat 11.2.1-9)  GMP version 6.0.0  MPFR version 3.1.1  MPC version 1.0.1  isl version isl-0.18-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring nonexistent directory "/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/include-fixed"]
        ignore line: [ignoring nonexistent directory "/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../x86_64-redhat-linux/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/include]
        ignore line: [ /usr/local/include]
        ignore line: [ /opt/rh/devtoolset-11/root/usr/include]
        ignore line: [ /usr/include]
        ignore line: [End of search list.]
        ignore line: [GNU C17 (GCC) version 11.2.1 20220127 (Red Hat 11.2.1-9) (x86_64-redhat-linux)]
        ignore line: [	compiled by GNU C version 11.2.1 20220127 (Red Hat 11.2.1-9)  GMP version 6.0.0  MPFR version 3.1.1  MPC version 1.0.1  isl version isl-0.18-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [Compiler executable checksum: fcd9572df254446d5004a010508b613e]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_427d8.dir/CMakeCCompilerABI.c.o' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_427d8.dir/']
        ignore line: [ /opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/as -v --64 -o CMakeFiles/cmTC_427d8.dir/CMakeCCompilerABI.c.o /tmp/ccQICqKr.s]
        ignore line: [GNU assembler version 2.36.1 (x86_64-redhat-linux) using BFD version version 2.36.1-1.el7.2]
        ignore line: [COMPILER_PATH=/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/:/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/:/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/:/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/:/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/]
        ignore line: [LIBRARY_PATH=/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/:/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../lib64/:/lib/../lib64/:/usr/lib/../lib64/:/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../:/lib/:/usr/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_427d8.dir/CMakeCCompilerABI.c.o' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_427d8.dir/CMakeCCompilerABI.c.']
        ignore line: [Linking C executable cmTC_427d8]
        ignore line: [/home/<USER>/cmake/bin/cmake -E cmake_link_script CMakeFiles/cmTC_427d8.dir/link.txt --verbose=1]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/opt/rh/devtoolset-11/root/usr/bin/cc]
        ignore line: [COLLECT_LTO_WRAPPER=/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/lto-wrapper]
        ignore line: [Target: x86_64-redhat-linux]
        ignore line: [Configured with: ../configure --enable-bootstrap --enable-languages=c c++ fortran lto --prefix=/opt/rh/devtoolset-11/root/usr --mandir=/opt/rh/devtoolset-11/root/usr/share/man --infodir=/opt/rh/devtoolset-11/root/usr/share/info --with-bugurl=http://bugzilla.redhat.com/bugzilla --enable-shared --enable-threads=posix --enable-checking=release --enable-multilib --with-system-zlib --enable-__cxa_atexit --disable-libunwind-exceptions --enable-gnu-unique-object --enable-linker-build-id --with-gcc-major-version-only --with-linker-hash-style=gnu --with-default-libstdcxx-abi=gcc4-compatible --enable-plugin --enable-initfini-array --with-isl=/builddir/build/BUILD/gcc-11.2.1-20220127/obj-x86_64-redhat-linux/isl-install --enable-gnu-indirect-function --with-tune=generic --with-arch_32=x86-64 --build=x86_64-redhat-linux]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib]
        ignore line: [gcc version 11.2.1 20220127 (Red Hat 11.2.1-9) (GCC) ]
        ignore line: [COMPILER_PATH=/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/:/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/:/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/:/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/:/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/]
        ignore line: [LIBRARY_PATH=/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/:/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../lib64/:/lib/../lib64/:/usr/lib/../lib64/:/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../:/lib/:/usr/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_427d8' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_427d8.']
        link line: [ /opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/collect2 -plugin /opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/liblto_plugin.so -plugin-opt=/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/lto-wrapper -plugin-opt=-fresolution=/tmp/ccI0LFuu.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --build-id --no-add-needed --eh-frame-hdr --hash-style=gnu -m elf_x86_64 -dynamic-linker /lib64/ld-linux-x86-64.so.2 -o cmTC_427d8 /lib/../lib64/crt1.o /lib/../lib64/crti.o /opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/crtbegin.o -L/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11 -L/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../lib64 -L/lib/../lib64 -L/usr/lib/../lib64 -L/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../.. -v CMakeFiles/cmTC_427d8.dir/CMakeCCompilerABI.c.o -lgcc --push-state --as-needed -lgcc_s --pop-state -lc -lgcc --push-state --as-needed -lgcc_s --pop-state /opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/crtend.o /lib/../lib64/crtn.o]
          arg [/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/collect2] ==> ignore
          arg [-plugin] ==> ignore
          arg [/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/liblto_plugin.so] ==> ignore
          arg [-plugin-opt=/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/lto-wrapper] ==> ignore
          arg [-plugin-opt=-fresolution=/tmp/ccI0LFuu.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [--build-id] ==> ignore
          arg [--no-add-needed] ==> ignore
          arg [--eh-frame-hdr] ==> ignore
          arg [--hash-style=gnu] ==> ignore
          arg [-m] ==> ignore
          arg [elf_x86_64] ==> ignore
          arg [-dynamic-linker] ==> ignore
          arg [/lib64/ld-linux-x86-64.so.2] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_427d8] ==> ignore
          arg [/lib/../lib64/crt1.o] ==> obj [/lib/../lib64/crt1.o]
          arg [/lib/../lib64/crti.o] ==> obj [/lib/../lib64/crti.o]
          arg [/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/crtbegin.o] ==> obj [/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/crtbegin.o]
          arg [-L/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11] ==> dir [/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11]
          arg [-L/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../lib64] ==> dir [/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../lib64]
          arg [-L/lib/../lib64] ==> dir [/lib/../lib64]
          arg [-L/usr/lib/../lib64] ==> dir [/usr/lib/../lib64]
          arg [-L/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../..] ==> dir [/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../..]
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_427d8.dir/CMakeCCompilerABI.c.o] ==> ignore
          arg [-lgcc] ==> lib [gcc]
          arg [--push-state] ==> ignore
          arg [--as-needed] ==> ignore
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [--pop-state] ==> ignore
          arg [-lc] ==> lib [c]
          arg [-lgcc] ==> lib [gcc]
          arg [--push-state] ==> ignore
          arg [--as-needed] ==> ignore
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [--pop-state] ==> ignore
          arg [/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/crtend.o] ==> obj [/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/crtend.o]
          arg [/lib/../lib64/crtn.o] ==> obj [/lib/../lib64/crtn.o]
        ignore line: [collect2 version 11.2.1 20220127 (Red Hat 11.2.1-9)]
        ignore line: [/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/ld -plugin /opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/liblto_plugin.so -plugin-opt=/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/lto-wrapper -plugin-opt=-fresolution=/tmp/ccI0LFuu.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --build-id --no-add-needed --eh-frame-hdr --hash-style=gnu -m elf_x86_64 -dynamic-linker /lib64/ld-linux-x86-64.so.2 -o cmTC_427d8 /lib/../lib64/crt1.o /lib/../lib64/crti.o /opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/crtbegin.o -L/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11 -L/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../lib64 -L/lib/../lib64 -L/usr/lib/../lib64 -L/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../.. -v CMakeFiles/cmTC_427d8.dir/CMakeCCompilerABI.c.o -lgcc --push-state --as-needed -lgcc_s --pop-state -lc -lgcc --push-state --as-needed -lgcc_s --pop-state /opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/crtend.o /lib/../lib64/crtn.o]
        linker tool for 'C': /opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/ld
        collapse obj [/lib/../lib64/crt1.o] ==> [/lib64/crt1.o]
        collapse obj [/lib/../lib64/crti.o] ==> [/lib64/crti.o]
        collapse obj [/lib/../lib64/crtn.o] ==> [/lib64/crtn.o]
        collapse library dir [/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11] ==> [/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11]
        collapse library dir [/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../lib64] ==> [/opt/rh/devtoolset-11/root/usr/lib64]
        collapse library dir [/lib/../lib64] ==> [/lib64]
        collapse library dir [/usr/lib/../lib64] ==> [/usr/lib64]
        collapse library dir [/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../..] ==> [/opt/rh/devtoolset-11/root/usr/lib]
        implicit libs: [gcc;gcc_s;c;gcc;gcc_s]
        implicit objs: [/lib64/crt1.o;/lib64/crti.o;/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/crtbegin.o;/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/crtend.o;/lib64/crtn.o]
        implicit dirs: [/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11;/opt/rh/devtoolset-11/root/usr/lib64;/lib64;/usr/lib64;/opt/rh/devtoolset-11/root/usr/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt"
    message: |
      Running the C compiler's linker: "/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/ld" "-v"
      GNU ld version 2.36.1-1.el7.2
  -
    kind: "try_compile-v1"
    backtrace:
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "/trunk/hj/hybrid_kit_shm-enflame-0311/src/sideband/bridge/build/CMakeFiles/CMakeScratch/TryCompile-mZhVII"
      binary: "/trunk/hj/hybrid_kit_shm-enflame-0311/src/sideband/bridge/build/CMakeFiles/CMakeScratch/TryCompile-mZhVII"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/trunk/hj/hybrid_kit_shm-enflame-0311/src/sideband/bridge/build/CMakeFiles/CMakeScratch/TryCompile-mZhVII'
        
        Run Build Command(s): /home/<USER>/cmake/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_19b48/fast
        /usr/bin/gmake  -f CMakeFiles/cmTC_19b48.dir/build.make CMakeFiles/cmTC_19b48.dir/build
        gmake[1]: Entering directory `/trunk/hj/hybrid_kit_shm-enflame-0311/src/sideband/bridge/build/CMakeFiles/CMakeScratch/TryCompile-mZhVII'
        Building CXX object CMakeFiles/cmTC_19b48.dir/CMakeCXXCompilerABI.cpp.o
        /opt/rh/devtoolset-11/root/usr/bin/c++   -v -o CMakeFiles/cmTC_19b48.dir/CMakeCXXCompilerABI.cpp.o -c /home/<USER>/cmake/share/cmake-4.0/Modules/CMakeCXXCompilerABI.cpp
        Using built-in specs.
        COLLECT_GCC=/opt/rh/devtoolset-11/root/usr/bin/c++
        Target: x86_64-redhat-linux
        Configured with: ../configure --enable-bootstrap --enable-languages=c,c++,fortran,lto --prefix=/opt/rh/devtoolset-11/root/usr --mandir=/opt/rh/devtoolset-11/root/usr/share/man --infodir=/opt/rh/devtoolset-11/root/usr/share/info --with-bugurl=http://bugzilla.redhat.com/bugzilla --enable-shared --enable-threads=posix --enable-checking=release --enable-multilib --with-system-zlib --enable-__cxa_atexit --disable-libunwind-exceptions --enable-gnu-unique-object --enable-linker-build-id --with-gcc-major-version-only --with-linker-hash-style=gnu --with-default-libstdcxx-abi=gcc4-compatible --enable-plugin --enable-initfini-array --with-isl=/builddir/build/BUILD/gcc-11.2.1-20220127/obj-x86_64-redhat-linux/isl-install --enable-gnu-indirect-function --with-tune=generic --with-arch_32=x86-64 --build=x86_64-redhat-linux
        Thread model: posix
        Supported LTO compression algorithms: zlib
        gcc version 11.2.1 20220127 (Red Hat 11.2.1-9) (GCC) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_19b48.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_19b48.dir/'
         /opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/cc1plus -quiet -v -D_GNU_SOURCE /home/<USER>/cmake/share/cmake-4.0/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles/cmTC_19b48.dir/ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -mtune=generic -march=x86-64 -version -o /tmp/ccJlLEsA.s
        GNU C++17 (GCC) version 11.2.1 20220127 (Red Hat 11.2.1-9) (x86_64-redhat-linux)
        	compiled by GNU C version 11.2.1 20220127 (Red Hat 11.2.1-9), GMP version 6.0.0, MPFR version 3.1.1, MPC version 1.0.1, isl version isl-0.18-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring nonexistent directory "/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/include-fixed"
        ignoring nonexistent directory "/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../x86_64-redhat-linux/include"
        #include "..." search starts here:
        #include <...> search starts here:
         /opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../include/c++/11
         /opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../include/c++/11/x86_64-redhat-linux
         /opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../include/c++/11/backward
         /opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/include
         /usr/local/include
         /opt/rh/devtoolset-11/root/usr/include
         /usr/include
        End of search list.
        GNU C++17 (GCC) version 11.2.1 20220127 (Red Hat 11.2.1-9) (x86_64-redhat-linux)
        	compiled by GNU C version 11.2.1 20220127 (Red Hat 11.2.1-9), GMP version 6.0.0, MPFR version 3.1.1, MPC version 1.0.1, isl version isl-0.18-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        Compiler executable checksum: 4d912b14bce339cc8a41b2b0d103bc14
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_19b48.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_19b48.dir/'
         /opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/as -v --64 -o CMakeFiles/cmTC_19b48.dir/CMakeCXXCompilerABI.cpp.o /tmp/ccJlLEsA.s
        GNU assembler version 2.36.1 (x86_64-redhat-linux) using BFD version version 2.36.1-1.el7.2
        COMPILER_PATH=/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/:/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/:/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/:/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/:/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/
        LIBRARY_PATH=/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/:/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../lib64/:/lib/../lib64/:/usr/lib/../lib64/:/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_19b48.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_19b48.dir/CMakeCXXCompilerABI.cpp.'
        Linking CXX executable cmTC_19b48
        /home/<USER>/cmake/bin/cmake -E cmake_link_script CMakeFiles/cmTC_19b48.dir/link.txt --verbose=1
        Using built-in specs.
        COLLECT_GCC=/opt/rh/devtoolset-11/root/usr/bin/c++
        COLLECT_LTO_WRAPPER=/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/lto-wrapper
        Target: x86_64-redhat-linux
        Configured with: ../configure --enable-bootstrap --enable-languages=c,c++,fortran,lto --prefix=/opt/rh/devtoolset-11/root/usr --mandir=/opt/rh/devtoolset-11/root/usr/share/man --infodir=/opt/rh/devtoolset-11/root/usr/share/info --with-bugurl=http://bugzilla.redhat.com/bugzilla --enable-shared --enable-threads=posix --enable-checking=release --enable-multilib --with-system-zlib --enable-__cxa_atexit --disable-libunwind-exceptions --enable-gnu-unique-object --enable-linker-build-id --with-gcc-major-version-only --with-linker-hash-style=gnu --with-default-libstdcxx-abi=gcc4-compatible --enable-plugin --enable-initfini-array --with-isl=/builddir/build/BUILD/gcc-11.2.1-20220127/obj-x86_64-redhat-linux/isl-install --enable-gnu-indirect-function --with-tune=generic --with-arch_32=x86-64 --build=x86_64-redhat-linux
        Thread model: posix
        Supported LTO compression algorithms: zlib
        gcc version 11.2.1 20220127 (Red Hat 11.2.1-9) (GCC) 
        COMPILER_PATH=/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/:/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/:/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/:/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/:/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/
        LIBRARY_PATH=/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/:/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../lib64/:/lib/../lib64/:/usr/lib/../lib64/:/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_19b48' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_19b48.'
         /opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/collect2 -plugin /opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/liblto_plugin.so -plugin-opt=/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/lto-wrapper -plugin-opt=-fresolution=/tmp/cc971zav.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --no-add-needed --eh-frame-hdr --hash-style=gnu -m elf_x86_64 -dynamic-linker /lib64/ld-linux-x86-64.so.2 -o cmTC_19b48 /lib/../lib64/crt1.o /lib/../lib64/crti.o /opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/crtbegin.o -L/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11 -L/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../lib64 -L/lib/../lib64 -L/usr/lib/../lib64 -L/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../.. -v CMakeFiles/cmTC_19b48.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/crtend.o /lib/../lib64/crtn.o
        collect2 version 11.2.1 20220127 (Red Hat 11.2.1-9)
        /opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/ld -plugin /opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/liblto_plugin.so -plugin-opt=/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/lto-wrapper -plugin-opt=-fresolution=/tmp/cc971zav.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --no-add-needed --eh-frame-hdr --hash-style=gnu -m elf_x86_64 -dynamic-linker /lib64/ld-linux-x86-64.so.2 -o cmTC_19b48 /lib/../lib64/crt1.o /lib/../lib64/crti.o /opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/crtbegin.o -L/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11 -L/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../lib64 -L/lib/../lib64 -L/usr/lib/../lib64 -L/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../.. -v CMakeFiles/cmTC_19b48.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/crtend.o /lib/../lib64/crtn.o
        GNU ld version 2.36.1-1.el7.2
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_19b48' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_19b48.'
        /opt/rh/devtoolset-11/root/usr/bin/c++  -v -Wl,-v CMakeFiles/cmTC_19b48.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_19b48
        gmake[1]: Leaving directory `/trunk/hj/hybrid_kit_shm-enflame-0311/src/sideband/bridge/build/CMakeFiles/CMakeScratch/TryCompile-mZhVII'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:217 (message)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../include/c++/11]
          add: [/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../include/c++/11/x86_64-redhat-linux]
          add: [/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../include/c++/11/backward]
          add: [/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/include]
          add: [/usr/local/include]
          add: [/opt/rh/devtoolset-11/root/usr/include]
          add: [/usr/include]
        end of search list found
        collapse include dir [/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../include/c++/11] ==> [/opt/rh/devtoolset-11/root/usr/include/c++/11]
        collapse include dir [/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../include/c++/11/x86_64-redhat-linux] ==> [/opt/rh/devtoolset-11/root/usr/include/c++/11/x86_64-redhat-linux]
        collapse include dir [/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../include/c++/11/backward] ==> [/opt/rh/devtoolset-11/root/usr/include/c++/11/backward]
        collapse include dir [/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/include] ==> [/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/include]
        collapse include dir [/usr/local/include] ==> [/usr/local/include]
        collapse include dir [/opt/rh/devtoolset-11/root/usr/include] ==> [/opt/rh/devtoolset-11/root/usr/include]
        collapse include dir [/usr/include] ==> [/usr/include]
        implicit include dirs: [/opt/rh/devtoolset-11/root/usr/include/c++/11;/opt/rh/devtoolset-11/root/usr/include/c++/11/x86_64-redhat-linux;/opt/rh/devtoolset-11/root/usr/include/c++/11/backward;/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/include;/usr/local/include;/opt/rh/devtoolset-11/root/usr/include;/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: '/trunk/hj/hybrid_kit_shm-enflame-0311/src/sideband/bridge/build/CMakeFiles/CMakeScratch/TryCompile-mZhVII']
        ignore line: []
        ignore line: [Run Build Command(s): /home/<USER>/cmake/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_19b48/fast]
        ignore line: [/usr/bin/gmake  -f CMakeFiles/cmTC_19b48.dir/build.make CMakeFiles/cmTC_19b48.dir/build]
        ignore line: [gmake[1]: Entering directory `/trunk/hj/hybrid_kit_shm-enflame-0311/src/sideband/bridge/build/CMakeFiles/CMakeScratch/TryCompile-mZhVII']
        ignore line: [Building CXX object CMakeFiles/cmTC_19b48.dir/CMakeCXXCompilerABI.cpp.o]
        ignore line: [/opt/rh/devtoolset-11/root/usr/bin/c++   -v -o CMakeFiles/cmTC_19b48.dir/CMakeCXXCompilerABI.cpp.o -c /home/<USER>/cmake/share/cmake-4.0/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/opt/rh/devtoolset-11/root/usr/bin/c++]
        ignore line: [Target: x86_64-redhat-linux]
        ignore line: [Configured with: ../configure --enable-bootstrap --enable-languages=c c++ fortran lto --prefix=/opt/rh/devtoolset-11/root/usr --mandir=/opt/rh/devtoolset-11/root/usr/share/man --infodir=/opt/rh/devtoolset-11/root/usr/share/info --with-bugurl=http://bugzilla.redhat.com/bugzilla --enable-shared --enable-threads=posix --enable-checking=release --enable-multilib --with-system-zlib --enable-__cxa_atexit --disable-libunwind-exceptions --enable-gnu-unique-object --enable-linker-build-id --with-gcc-major-version-only --with-linker-hash-style=gnu --with-default-libstdcxx-abi=gcc4-compatible --enable-plugin --enable-initfini-array --with-isl=/builddir/build/BUILD/gcc-11.2.1-20220127/obj-x86_64-redhat-linux/isl-install --enable-gnu-indirect-function --with-tune=generic --with-arch_32=x86-64 --build=x86_64-redhat-linux]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib]
        ignore line: [gcc version 11.2.1 20220127 (Red Hat 11.2.1-9) (GCC) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_19b48.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_19b48.dir/']
        ignore line: [ /opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/cc1plus -quiet -v -D_GNU_SOURCE /home/<USER>/cmake/share/cmake-4.0/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles/cmTC_19b48.dir/ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -mtune=generic -march=x86-64 -version -o /tmp/ccJlLEsA.s]
        ignore line: [GNU C++17 (GCC) version 11.2.1 20220127 (Red Hat 11.2.1-9) (x86_64-redhat-linux)]
        ignore line: [	compiled by GNU C version 11.2.1 20220127 (Red Hat 11.2.1-9)  GMP version 6.0.0  MPFR version 3.1.1  MPC version 1.0.1  isl version isl-0.18-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring nonexistent directory "/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/include-fixed"]
        ignore line: [ignoring nonexistent directory "/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../x86_64-redhat-linux/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../include/c++/11]
        ignore line: [ /opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../include/c++/11/x86_64-redhat-linux]
        ignore line: [ /opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../include/c++/11/backward]
        ignore line: [ /opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/include]
        ignore line: [ /usr/local/include]
        ignore line: [ /opt/rh/devtoolset-11/root/usr/include]
        ignore line: [ /usr/include]
        ignore line: [End of search list.]
        ignore line: [GNU C++17 (GCC) version 11.2.1 20220127 (Red Hat 11.2.1-9) (x86_64-redhat-linux)]
        ignore line: [	compiled by GNU C version 11.2.1 20220127 (Red Hat 11.2.1-9)  GMP version 6.0.0  MPFR version 3.1.1  MPC version 1.0.1  isl version isl-0.18-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [Compiler executable checksum: 4d912b14bce339cc8a41b2b0d103bc14]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_19b48.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_19b48.dir/']
        ignore line: [ /opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/as -v --64 -o CMakeFiles/cmTC_19b48.dir/CMakeCXXCompilerABI.cpp.o /tmp/ccJlLEsA.s]
        ignore line: [GNU assembler version 2.36.1 (x86_64-redhat-linux) using BFD version version 2.36.1-1.el7.2]
        ignore line: [COMPILER_PATH=/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/:/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/:/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/:/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/:/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/]
        ignore line: [LIBRARY_PATH=/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/:/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../lib64/:/lib/../lib64/:/usr/lib/../lib64/:/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../:/lib/:/usr/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_19b48.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_19b48.dir/CMakeCXXCompilerABI.cpp.']
        ignore line: [Linking CXX executable cmTC_19b48]
        ignore line: [/home/<USER>/cmake/bin/cmake -E cmake_link_script CMakeFiles/cmTC_19b48.dir/link.txt --verbose=1]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/opt/rh/devtoolset-11/root/usr/bin/c++]
        ignore line: [COLLECT_LTO_WRAPPER=/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/lto-wrapper]
        ignore line: [Target: x86_64-redhat-linux]
        ignore line: [Configured with: ../configure --enable-bootstrap --enable-languages=c c++ fortran lto --prefix=/opt/rh/devtoolset-11/root/usr --mandir=/opt/rh/devtoolset-11/root/usr/share/man --infodir=/opt/rh/devtoolset-11/root/usr/share/info --with-bugurl=http://bugzilla.redhat.com/bugzilla --enable-shared --enable-threads=posix --enable-checking=release --enable-multilib --with-system-zlib --enable-__cxa_atexit --disable-libunwind-exceptions --enable-gnu-unique-object --enable-linker-build-id --with-gcc-major-version-only --with-linker-hash-style=gnu --with-default-libstdcxx-abi=gcc4-compatible --enable-plugin --enable-initfini-array --with-isl=/builddir/build/BUILD/gcc-11.2.1-20220127/obj-x86_64-redhat-linux/isl-install --enable-gnu-indirect-function --with-tune=generic --with-arch_32=x86-64 --build=x86_64-redhat-linux]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib]
        ignore line: [gcc version 11.2.1 20220127 (Red Hat 11.2.1-9) (GCC) ]
        ignore line: [COMPILER_PATH=/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/:/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/:/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/:/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/:/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/]
        ignore line: [LIBRARY_PATH=/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/:/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../lib64/:/lib/../lib64/:/usr/lib/../lib64/:/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../:/lib/:/usr/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_19b48' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_19b48.']
        link line: [ /opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/collect2 -plugin /opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/liblto_plugin.so -plugin-opt=/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/lto-wrapper -plugin-opt=-fresolution=/tmp/cc971zav.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --no-add-needed --eh-frame-hdr --hash-style=gnu -m elf_x86_64 -dynamic-linker /lib64/ld-linux-x86-64.so.2 -o cmTC_19b48 /lib/../lib64/crt1.o /lib/../lib64/crti.o /opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/crtbegin.o -L/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11 -L/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../lib64 -L/lib/../lib64 -L/usr/lib/../lib64 -L/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../.. -v CMakeFiles/cmTC_19b48.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/crtend.o /lib/../lib64/crtn.o]
          arg [/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/collect2] ==> ignore
          arg [-plugin] ==> ignore
          arg [/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/liblto_plugin.so] ==> ignore
          arg [-plugin-opt=/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/lto-wrapper] ==> ignore
          arg [-plugin-opt=-fresolution=/tmp/cc971zav.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [--build-id] ==> ignore
          arg [--no-add-needed] ==> ignore
          arg [--eh-frame-hdr] ==> ignore
          arg [--hash-style=gnu] ==> ignore
          arg [-m] ==> ignore
          arg [elf_x86_64] ==> ignore
          arg [-dynamic-linker] ==> ignore
          arg [/lib64/ld-linux-x86-64.so.2] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_19b48] ==> ignore
          arg [/lib/../lib64/crt1.o] ==> obj [/lib/../lib64/crt1.o]
          arg [/lib/../lib64/crti.o] ==> obj [/lib/../lib64/crti.o]
          arg [/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/crtbegin.o] ==> obj [/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/crtbegin.o]
          arg [-L/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11] ==> dir [/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11]
          arg [-L/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../lib64] ==> dir [/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../lib64]
          arg [-L/lib/../lib64] ==> dir [/lib/../lib64]
          arg [-L/usr/lib/../lib64] ==> dir [/usr/lib/../lib64]
          arg [-L/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../..] ==> dir [/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../..]
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_19b48.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
          arg [-lstdc++] ==> lib [stdc++]
          arg [-lm] ==> lib [m]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [-lc] ==> lib [c]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/crtend.o] ==> obj [/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/crtend.o]
          arg [/lib/../lib64/crtn.o] ==> obj [/lib/../lib64/crtn.o]
        ignore line: [collect2 version 11.2.1 20220127 (Red Hat 11.2.1-9)]
        ignore line: [/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/ld -plugin /opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/liblto_plugin.so -plugin-opt=/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/lto-wrapper -plugin-opt=-fresolution=/tmp/cc971zav.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --no-add-needed --eh-frame-hdr --hash-style=gnu -m elf_x86_64 -dynamic-linker /lib64/ld-linux-x86-64.so.2 -o cmTC_19b48 /lib/../lib64/crt1.o /lib/../lib64/crti.o /opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/crtbegin.o -L/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11 -L/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../lib64 -L/lib/../lib64 -L/usr/lib/../lib64 -L/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../.. -v CMakeFiles/cmTC_19b48.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/crtend.o /lib/../lib64/crtn.o]
        linker tool for 'CXX': /opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/ld
        collapse obj [/lib/../lib64/crt1.o] ==> [/lib64/crt1.o]
        collapse obj [/lib/../lib64/crti.o] ==> [/lib64/crti.o]
        collapse obj [/lib/../lib64/crtn.o] ==> [/lib64/crtn.o]
        collapse library dir [/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11] ==> [/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11]
        collapse library dir [/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../../../lib64] ==> [/opt/rh/devtoolset-11/root/usr/lib64]
        collapse library dir [/lib/../lib64] ==> [/lib64]
        collapse library dir [/usr/lib/../lib64] ==> [/usr/lib64]
        collapse library dir [/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/../../..] ==> [/opt/rh/devtoolset-11/root/usr/lib]
        implicit libs: [stdc++;m;gcc_s;gcc;c;gcc_s;gcc]
        implicit objs: [/lib64/crt1.o;/lib64/crti.o;/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/crtbegin.o;/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/crtend.o;/lib64/crtn.o]
        implicit dirs: [/opt/rh/devtoolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11;/opt/rh/devtoolset-11/root/usr/lib64;/lib64;/usr/lib64;/opt/rh/devtoolset-11/root/usr/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "/home/<USER>/cmake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt"
    message: |
      Running the CXX compiler's linker: "/opt/rh/devtoolset-11/root/usr/libexec/gcc/x86_64-redhat-linux/11/ld" "-v"
      GNU ld version 2.36.1-1.el7.2
...
