#ifndef SSLN_HYBRID_SIDEBAND_SIG_BRIDGE_H
#define SSLN_HYBRID_SIDEBAND_SIG_BRIDGE_H

#include <systemc>
#include <vector>
#include <atomic>
#include <cstdint>
#include <thread>
#include <readerwriterqueue.h>
#include "xtor_sig_proxy.h"
#include "sig_payload.h"
#include "async_event.h"
#include "dispatcher/include/dispatcher.h"
#include "dispatcher/include/i_endpoint.h"
namespace ssln{
namespace hybrid{

class SidebandSigBridge: public sc_core::sc_module, public IEndpoint {

public:
    SC_HAS_PROCESS(SidebandSigBridge);
    SidebandSigBridge(sc_core::sc_module_name nm, Dispatcher* dispatcher, uint32_t comp_id, uint32_t dest_id, const std::string& bfm_hdl_path, uint32_t n_sig_in, uint32_t n_sig_out);
    ~SidebandSigBridge();
    void HandleRequest(void* req);

private:
    // IEndpoint interface
    bool handle_data(const void* data, size_t size) override;
    uint32_t get_component_id() const override;

    void HandleOutRequest(SigPayload* sig_req);

    void ScProcessReq();

    uint32_t n_sig_in;
    uint32_t n_sig_out;

    Dispatcher* dispatcher_;
    uint32_t comp_id_;
    uint32_t dest_id_;
    std::shared_ptr<umi_sig::xtor_sig_proxy> sig_proxy;

    moodycamel::ReaderWriterQueue<std::vector<char>>* recv_req_Q;
    
    async_event recv_req_event;

    std::atomic<uint64_t> id_count{0};
};



} // namespace hybride
} // namespace ssln





#endif