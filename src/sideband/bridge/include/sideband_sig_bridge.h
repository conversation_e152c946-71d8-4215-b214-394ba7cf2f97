#ifndef SSLN_HYBRID_SIDEBAND_SIG_BRIDGE_H_
#define SSLN_HYBRID_SIDEBAND_SIG_BRIDGE_H_

#include <atomic>
#include <cstdint>
#include <memory>
#include <string>
#include <vector>

#include <systemc>
#include <readerwriterqueue.h>

#include "async_event.h"
#include "dispatcher/include/dispatcher.h"
#include "dispatcher/include/i_endpoint.h"
#include "sig_payload.h"
#include "xtor_sig_proxy.h"

namespace ssln {
namespace hybrid {

/**
 * @brief Bridge between sideband signals and TLM proxy using dispatcher
 *
 * This class handles the conversion between hardware sideband signals and
 * TLM-based communication through the dispatcher system. It manages signal
 * transactions and provides the interface between the hardware simulation
 * and the software TLM world.
 */
class SidebandSigBridge : public sc_core::sc_module, public IEndpoint {
 public:
  SC_HAS_PROCESS(SidebandSigBridge);

  /**
   * @brief Construct a new SidebandSigBridge
   *
   * @param nm SystemC module name
   * @param dispatcher Pointer to the dispatcher for communication
   * @param comp_id Component ID for this bridge
   * @param dest_id Destination component ID for communication
   * @param bfm_hdl_path Path to the BFM HDL module
   * @param n_sig_in Number of input signals
   * @param n_sig_out Number of output signals
   */
  SidebandSigBridge(sc_core::sc_module_name nm,
                    Dispatcher* dispatcher,
                    uint32_t comp_id,
                    uint32_t dest_id,
                    const std::string& bfm_hdl_path,
                    uint32_t n_sig_in,
                    uint32_t n_sig_out);

  ~SidebandSigBridge();

  // Disabled copy/move operations
  SidebandSigBridge(const SidebandSigBridge&) = delete;
  SidebandSigBridge& operator=(const SidebandSigBridge&) = delete;
  SidebandSigBridge(SidebandSigBridge&&) = delete;
  SidebandSigBridge& operator=(SidebandSigBridge&&) = delete;

  /**
   * @brief Handle incoming signal request from hardware
   * @param req Pointer to the signal transaction request
   */
  void HandleRequest(void* req);

 private:
  // IEndpoint interface implementation
  bool handle_data(const void* data, size_t size) override;
  uint32_t get_component_id() const override;

  /**
   * @brief Handle outgoing signal request to hardware
   * @param sig_req Pointer to the signal payload request
   */
  void HandleOutRequest(SigPayload* sig_req);

  /**
   * @brief SystemC thread for processing signal requests
   */
  void ScProcessReq();

  // Signal configuration
  uint32_t n_sig_in_;
  uint32_t n_sig_out_;

  // Dispatcher communication
  Dispatcher* dispatcher_;
  uint32_t comp_id_;
  uint32_t dest_id_;

  // Signal proxy for hardware interaction
  std::shared_ptr<umi_sig::xtor_sig_proxy> sig_proxy_;

  // Request queue and synchronization
  moodycamel::ReaderWriterQueue<std::vector<char>>* recv_req_Q_;
  async_event recv_req_event_;

  // Transaction ID counter
  std::atomic<uint64_t> id_count_{0};
};


}  // namespace hybrid
}  // namespace ssln

#endif  // SSLN_HYBRID_SIDEBAND_SIG_BRIDGE_H_

#endif