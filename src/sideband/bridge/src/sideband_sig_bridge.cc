#include "sideband_sig_bridge.h"

#include <cstring>
#include <memory>

#include "ssln/sslogger.h"
#include "dispatcher/generated/payload_generated.h"

namespace ssln {
namespace hybrid {

SidebandSigBridge::SidebandSigBridge(sc_core::sc_module_name nm,
                                     Dispatcher* dispatcher,
                                     uint32_t comp_id,
                                     uint32_t dest_id,
                                     const std::string& bfm_hdl_path,
                                     uint32_t n_sig_in,
                                     uint32_t n_sig_out)
    : sc_core::sc_module(nm),
      n_sig_in_(n_sig_in),
      n_sig_out_(n_sig_out),
      dispatcher_(dispatcher),
      comp_id_(comp_id),
      dest_id_(dest_id) {

  // Create the signal proxy
  sig_proxy_ = std::make_shared<umi_sig::xtor_sig_proxy>("xtor_sig_proxy", bfm_hdl_path);
  sig_proxy_->setHandler("AFTER_RECV_TRANS", this, &SidebandSigBridge::HandleRequest);

  // Initialize the request queue
  recv_req_Q_ = new moodycamel::ReaderWriterQueue<std::vector<char>>();

  // Start the SystemC thread for processing requests
  SC_THREAD(ScProcessReq);

  // Register this component with the dispatcher
  dispatcher_->RegisterEndpoint(this);
}

SidebandSigBridge::~SidebandSigBridge() {
  delete recv_req_Q_;
}

void SidebandSigBridge::HandleRequest(void* req) {
  auto* req_item = reinterpret_cast<umi_sig::xtor_sig_transaction_item*>(req);

  // Create FlatBuffers builder
  flatbuffers::FlatBufferBuilder builder(1024);

  // Prepare signal data
  uint32_t signal_width = req_item->get_signal_width();
  uint32_t signal_width_bytes = (signal_width + 7) / 8;

  std::vector<uint8_t> signal_data(signal_width_bytes);
  std::memcpy(signal_data.data(),
              reinterpret_cast<uint8_t*>(req_item->signals),
              signal_width_bytes);

  auto fb_signal_data = builder.CreateVector(signal_data);

  // Create the payload
  auto payload = ssln::hybrid::dispatch::CreatePayload(
      builder,
      dest_id_,                                           // destination_id
      ssln::hybrid::dispatch::PayloadType_SIDEBAND,      // payload_type
      id_count_.fetch_add(1, std::memory_order_relaxed), // id
      0,                                                  // command (unused for sideband)
      0,                                                  // address (unused for sideband)
      0,                                                  // response (unused for sideband)
      0,                                                  // streaming_width (unused for sideband)
      ssln::hybrid::dispatch::SidebandType_HW2TLM_REQ,   // sideband_type
      signal_width,                                       // signal_width
      fb_signal_data,                                     // variable_data
      0,                                                  // data_length (unused for sideband)
      0,                                                  // byte_enable_length (unused for sideband)
      0,                                                  // axuser_length (unused for sideband)
      0);                                                 // xuser_length (unused for sideband)

  builder.Finish(payload);

  // Send the request through dispatcher
  uint8_t* buffer = builder.GetBufferPointer();
  size_t size = builder.GetSize();

  SSLN_LOG_INFO(file_logger, "[{}], sending sideband request size: {}, signal_width: {}",
                this->name(), size, signal_width);

  if (dispatcher_->Send(dest_id_, buffer, size)) {
    SSLN_LOG_INFO(file_logger, "[{}], sideband request sent successfully", this->name());
  } else {
    SSLN_LOG_ERROR(file_logger, "[{}], failed to send sideband request", this->name());
  }
}

// IEndpoint interface implementation
bool SidebandSigBridge::HandleData(const void* data, size_t size) {
  SSLN_LOG_INFO(file_logger, "[{}], HandleData received data size: {}", this->name(), size);

  // Parse the FlatBuffers payload
  auto* fb_payload = flatbuffers::GetRoot<ssln::hybrid::dispatch::Payload>(data);

  // Verify this is a sideband payload
  if (fb_payload->payload_type() != ssln::hybrid::dispatch::PayloadType_SIDEBAND) {
    SSLN_LOG_ERROR(file_logger, "[{}], Received non-sideband payload type: {}",
                   this->name(), static_cast<int>(fb_payload->payload_type()));
    return false;
  }

  // Copy the data into a vector to be owned by the queue
  std::vector<char> data_vec(static_cast<const char*>(data),
                             static_cast<const char*>(data) + size);

  if (recv_req_Q_->try_enqueue(std::move(data_vec))) {
    recv_req_event_.notify(sc_core::SC_ZERO_TIME);
    return true;
  } else {
    SSLN_LOG_ERROR(file_logger, "[{}], Request queue full, data loss!!!", this->name());
    return false;
  }
}

uint32_t SidebandSigBridge::GetComponentId() const {
  return comp_id_;
}

void SidebandSigBridge::HandleOutRequest(const ssln::hybrid::dispatch::Payload* fb_payload) {
  auto req_item = std::make_unique<umi_sig::xtor_sig_transaction_item>();

  uint32_t signal_width = fb_payload->signal_width();
  uint32_t signal_width_uint = (signal_width + 31) / 32;
  auto signal_val = std::make_unique<uint32_t[]>(signal_width_uint);

  // Extract signal data from FlatBuffers
  auto* signal_data = fb_payload->variable_data();
  if (signal_data && signal_data->size() > 0) {
    std::memcpy(reinterpret_cast<uint8_t*>(signal_val.get()),
                signal_data->data(),
                (signal_width + 7) / 8);
  }

  req_item->set_signal_width(signal_width);
  req_item->signals = signal_val.get();

  sig_proxy_->nb_send_transaction(*req_item);

  // Send reply back through dispatcher using FlatBuffers
  flatbuffers::FlatBufferBuilder builder(256);

  auto reply_payload = ssln::hybrid::dispatch::CreatePayload(
      builder,
      dest_id_,                                           // destination_id
      ssln::hybrid::dispatch::PayloadType_SIDEBAND,      // payload_type
      fb_payload->id(),                                   // id (same as request)
      0,                                                  // command (unused for sideband)
      0,                                                  // address (unused for sideband)
      0,                                                  // response (unused for sideband)
      0,                                                  // streaming_width (unused for sideband)
      ssln::hybrid::dispatch::SidebandType_TLM2HW_REPLY, // sideband_type
      signal_width,                                       // signal_width
      0,                                                  // variable_data (empty for reply)
      0,                                                  // data_length (unused for sideband)
      0,                                                  // byte_enable_length (unused for sideband)
      0,                                                  // axuser_length (unused for sideband)
      0);                                                 // xuser_length (unused for sideband)

  builder.Finish(reply_payload);

  SSLN_LOG_INFO(file_logger, "[{}], sending sideband reply size: {}", this->name(), builder.GetSize());

  if (dispatcher_->Send(dest_id_, builder.GetBufferPointer(), builder.GetSize())) {
    SSLN_LOG_INFO(file_logger, "[{}], sideband reply sent successfully", this->name());
  } else {
    SSLN_LOG_ERROR(file_logger, "[{}], failed to send sideband reply", this->name());
  }
}

void SidebandSigBridge::ScProcessReq() {
  sig_proxy_->xtor_start_run();
  sig_proxy_->wait_reset_deassert();

  while (true) {
    if (recv_req_Q_->peek() == nullptr) {
      SSLN_LOG_INFO(file_logger, "[{}], waiting for recv_req_event", this->name());
      wait(recv_req_event_);
      SSLN_LOG_INFO(file_logger, "[{}], recv_req_event received", this->name());
    }

    std::vector<char> data_vec;
    if (recv_req_Q_->try_dequeue(data_vec)) {
      // Parse FlatBuffers payload
      auto* fb_payload = flatbuffers::GetRoot<ssln::hybrid::dispatch::Payload>(data_vec.data());

      // Verify this is a sideband request
      if (fb_payload->payload_type() == ssln::hybrid::dispatch::PayloadType_SIDEBAND &&
          fb_payload->sideband_type() == ssln::hybrid::dispatch::SidebandType_TLM2HW_REQ) {
        HandleOutRequest(fb_payload);
      } else {
        SSLN_LOG_ERROR(file_logger, "[{}], Received invalid sideband payload type", this->name());
      }
    }
  }
}

}  // namespace hybrid
}  // namespace ssln