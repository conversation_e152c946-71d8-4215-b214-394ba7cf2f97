#include "sideband_sig_bridge.h"
#include <cstring>
#include "ssln/sslogger.h"

using namespace ssln::hybrid;
using namespace umi_sig;
using namespace std;

SidebandSigBridge::SidebandSigBridge(sc_core::sc_module_name nm, const string& channel_name, shared_ptr<umi_sig::xtor_sig_proxy> sig_proxy_, uint32_t n_sig_in_, uint32_t n_sig_out_): 
    sc_core::sc_module(nm)
    , n_sig_in(n_sig_in_)
    , n_sig_out(n_sig_out_){
    ipc::channel::clear_storage((channel_name + "_hw2tlm").c_str());
    ipc::channel::clear_storage((channel_name + "_tlm2hw").c_str());
    send_channel = new ipc::channel((channel_name + "_hw2tlm").c_str(), ipc::sender);
    recv_channel = new ipc::channel((channel_name + "_tlm2hw").c_str(), ipc::receiver);

    this->sig_proxy = sig_proxy_;
    sig_proxy->setHandler("AFTER_RECV_TRANS",this, &SidebandSigBridge::HandleRequest);

    recv_req_Q = new moodycamel::ReaderWriterQueue<ipc::buffer*>();
    SC_THREAD(ScProcessReq);
    process_thread = make_unique<thread>(&SidebandSigBridge::ProcessMsg, this);
    
}

SidebandSigBridge::SidebandSigBridge(sc_core::sc_module_name nm, const std::string& channel_name, const std::string& bfm_hdl_path): sc_core::sc_module(nm){
    ipc::channel::clear_storage((channel_name + "_hw2tlm").c_str());
    ipc::channel::clear_storage((channel_name + "_tlm2hw").c_str());
    send_channel = new ipc::channel((channel_name + "_hw2tlm").c_str(), ipc::sender);
    recv_channel = new ipc::channel((channel_name + "_tlm2hw").c_str(), ipc::receiver);

    sig_proxy = std::make_shared<xtor_sig_proxy>("xtor_sig_proxy", bfm_hdl_path); 
    sig_proxy->setHandler("AFTER_RECV_TRANS",this, &SidebandSigBridge::HandleRequest);

    recv_req_Q = new moodycamel::ReaderWriterQueue<ipc::buffer*>();
    SC_THREAD(ScProcessReq);
    process_thread = make_unique<thread>(&SidebandSigBridge::ProcessMsg, this);


}


SidebandSigBridge::~SidebandSigBridge(){
    running_ = false;
    if(process_thread && process_thread->joinable()){
        process_thread->join();
    }
    send_channel->disconnect();
    recv_channel->disconnect();
    delete send_channel;
    delete recv_channel;

    delete recv_req_Q;
}

void SidebandSigBridge::HandleRequest(void* req){
    xtor_sig_transaction_item* req_item = reinterpret_cast<xtor_sig_transaction_item*>(req);
    
    uint32_t msg_size = sizeof(SigPayload);
    uint32_t signal_width_bytes = (req_item->get_signal_width() + 7) / 8;
    msg_size += signal_width_bytes;

    uint8_t* req_payload_ptr = new uint8_t [msg_size];
    SigPayload* req_payload = reinterpret_cast<SigPayload*>(req_payload_ptr);
    req_payload->id = id_count.fetch_add(1, memory_order_relaxed);
    req_payload->type = SigType::hw2tlm_req;
    req_payload->signal_width = req_item->get_signal_width();
    req_payload->data = req_payload_ptr + sizeof(SigPayload);

    // copy value
    memcpy(req_payload->data, reinterpret_cast<uint8_t*>(req_item->signals), signal_width_bytes);

    SSLN_LOG_INFO(file_logger, "[{}], send request size: {}", this->name(), msg_size);
    SSLN_LOG_DEBUG(file_logger, "[{}], {}", this->name(), *req_payload);
    if(send_channel->wait_for_recv(1)){
        send_channel->send(req_payload, msg_size);
    }

    // wait reply
    SSLN_LOG_INFO(file_logger, "[{}], wait recv_rep_event start", this->name());
    wait(recv_rep_event);
    SSLN_LOG_INFO(file_logger, "[{}], wait recv_rep_event end", this->name());

    delete [] req_payload_ptr;
}

void SidebandSigBridge::ScProcessReq(){
    
    sig_proxy->xtor_start_run();
    sig_proxy->wait_reset_deassert();

    while(true){
        if(recv_req_Q->peek() == nullptr){
            SSLN_LOG_INFO(file_logger, "[{}], wait recv_req_event start", this->name());
            wait(recv_req_event);
            SSLN_LOG_INFO(file_logger, "[{}], wait recv_req_event end", this->name());
        }
        ipc::buffer* data;
        
        recv_req_Q->try_dequeue(data);
        SigPayload* req_payload = reinterpret_cast<SigPayload*>(data->data());

        xtor_sig_transaction_item* req_item = new xtor_sig_transaction_item();
        uint32_t signal_width_uint = (req_payload->signal_width + 31) / 32;
        uint32_t* signal_val = new uint32_t [signal_width_uint];
        memcpy(reinterpret_cast<uint8_t*>(signal_val), req_payload->data, (req_payload->signal_width + 7) / 8);
        req_item->set_signal_width(req_payload->signal_width);
        req_item->signals = signal_val;

        sig_proxy->nb_send_transaction(*req_item);

        // send reply
        uint32_t sig_reply_size = sizeof(SigPayload);
        SigPayload* reply_payload = new SigPayload();
        reply_payload->id = req_payload->id;
        reply_payload->type = SigType::tlm2hw_reply;
        reply_payload->signal_width = req_payload->signal_width;
        reply_payload->data = nullptr;

        SSLN_LOG_INFO(file_logger, "[{}], send reply size: {}", this->name(), sig_reply_size);
        if(send_channel->wait_for_recv(1)){
            send_channel->send(reply_payload, sig_reply_size);
        }

        delete [] signal_val;
        delete req_item;
        delete data;
    }
}

void SidebandSigBridge::ProcessMsg(){
    while(running_){
        ipc::buffer* data = new ipc::buffer();
        (*data) = recv_channel->recv(1000);
        if (data->empty()){
            delete data;
            continue;
        }
        SSLN_LOG_INFO(file_logger, "[{}], recv msg size: {}", this->name(), data->size());
        SigPayload* sig_payload = reinterpret_cast<SigPayload*>(data->data());
        sig_payload->data = reinterpret_cast<uint8_t*>(sig_payload) + sizeof(SigPayload);
        if(sig_payload->type == SigType::tlm2hw_req){
            recv_req_Q->try_enqueue(data);
            recv_req_event.notify(sc_core::SC_ZERO_TIME);
            SSLN_LOG_INFO(file_logger, "[{}], recv_req_event notify", this->name());
        }
        else if(sig_payload->type == SigType::hw2tlm_reply){
            recv_rep_event.notify(sc_core::SC_ZERO_TIME);
            SSLN_LOG_INFO(file_logger, "[{}], recv_rep_event notify", this->name());
            delete data;
        }
    }
}

