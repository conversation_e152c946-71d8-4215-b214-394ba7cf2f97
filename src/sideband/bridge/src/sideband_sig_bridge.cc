#include "sideband_sig_bridge.h"

#include <cstring>
#include <memory>

#include "ssln/sslogger.h"

namespace ssln {
namespace hybrid {

SidebandSigBridge::SidebandSigBridge(sc_core::sc_module_name nm,
                                     Dispatcher* dispatcher,
                                     uint32_t comp_id,
                                     uint32_t dest_id,
                                     const std::string& bfm_hdl_path,
                                     uint32_t n_sig_in,
                                     uint32_t n_sig_out)
    : sc_core::sc_module(nm),
      n_sig_in_(n_sig_in),
      n_sig_out_(n_sig_out),
      dispatcher_(dispatcher),
      comp_id_(comp_id),
      dest_id_(dest_id) {

  // Create the signal proxy
  sig_proxy_ = std::make_shared<umi_sig::xtor_sig_proxy>("xtor_sig_proxy", bfm_hdl_path);
  sig_proxy_->setHandler("AFTER_RECV_TRANS", this, &SidebandSigBridge::HandleRequest);

  // Initialize the request queue
  recv_req_Q_ = new moodycamel::ReaderWriterQueue<std::vector<char>>();

  // Start the SystemC thread for processing requests
  SC_THREAD(ScProcessReq);

  // Register this component with the dispatcher
  dispatcher_->RegisterEndpoint(this);
}

SidebandSigBridge::~SidebandSigBridge() {
  delete recv_req_Q_;
}

void SidebandSigBridge::HandleRequest(void* req) {
  auto* req_item = reinterpret_cast<umi_sig::xtor_sig_transaction_item*>(req);

  uint32_t msg_size = sizeof(SigPayload);
  uint32_t signal_width_bytes = (req_item->get_signal_width() + 7) / 8;
  msg_size += signal_width_bytes;

  auto req_payload_ptr = std::make_unique<uint8_t[]>(msg_size);
  auto* req_payload = reinterpret_cast<SigPayload*>(req_payload_ptr.get());
  req_payload->id = id_count_.fetch_add(1, std::memory_order_relaxed);
  req_payload->type = SigType::hw2tlm_req;
  req_payload->signal_width = req_item->get_signal_width();
  req_payload->data = req_payload_ptr.get() + sizeof(SigPayload);

  // Copy signal value
  std::memcpy(req_payload->data,
              reinterpret_cast<uint8_t*>(req_item->signals),
              signal_width_bytes);

  SSLN_LOG_INFO(file_logger, "[{}], sending request size: {}", this->name(), msg_size);
  SSLN_LOG_DEBUG(file_logger, "[{}], {}", this->name(), *req_payload);

  // Send the request through dispatcher
  if (dispatcher_->Send(dest_id_, req_payload, msg_size)) {
    SSLN_LOG_INFO(file_logger, "[{}], request sent successfully", this->name());
  } else {
    SSLN_LOG_ERROR(file_logger, "[{}], failed to send request", this->name());
  }
}

// IEndpoint interface implementation
bool SidebandSigBridge::HandleData(const void* data, size_t size) {
  SSLN_LOG_INFO(file_logger, "[{}], handle_data received data size: {}", this->name(), size);

  // Copy the data into a vector to be owned by the queue
  std::vector<char> data_vec(static_cast<const char*>(data),
                             static_cast<const char*>(data) + size);

  if (recv_req_Q_->try_enqueue(std::move(data_vec))) {
    recv_req_event_.notify(sc_core::SC_ZERO_TIME);
    return true;
  } else {
    SSLN_LOG_ERROR(file_logger, "[{}], Request queue full, data loss!!!", this->name());
    return false;
  }
}

uint32_t SidebandSigBridge::GetComponentId() const {
  return comp_id_;
}

void SidebandSigBridge::HandleOutRequest(SigPayload* sig_req) {
  auto req_item = std::make_unique<umi_sig::xtor_sig_transaction_item>();
  uint32_t signal_width_uint = (sig_req->signal_width + 31) / 32;
  auto signal_val = std::make_unique<uint32_t[]>(signal_width_uint);

  std::memcpy(reinterpret_cast<uint8_t*>(signal_val.get()),
              sig_req->data,
              (sig_req->signal_width + 7) / 8);

  req_item->set_signal_width(sig_req->signal_width);
  req_item->signals = signal_val.get();

  sig_proxy_->nb_send_transaction(*req_item);

  // Send reply back through dispatcher
  SigPayload reply_payload;
  reply_payload.id = sig_req->id;
  reply_payload.type = SigType::tlm2hw_reply;
  reply_payload.signal_width = sig_req->signal_width;
  reply_payload.data = nullptr;

  SSLN_LOG_INFO(file_logger, "[{}], sending reply size: {}", this->name(), sizeof(SigPayload));

  if (dispatcher_->Send(dest_id_, &reply_payload, sizeof(SigPayload))) {
    SSLN_LOG_INFO(file_logger, "[{}], reply sent successfully", this->name());
  } else {
    SSLN_LOG_ERROR(file_logger, "[{}], failed to send reply", this->name());
  }
}

void SidebandSigBridge::ScProcessReq() {
  sig_proxy_->xtor_start_run();
  sig_proxy_->wait_reset_deassert();

  while (true) {
    if (recv_req_Q_->peek() == nullptr) {
      SSLN_LOG_INFO(file_logger, "[{}], waiting for recv_req_event", this->name());
      wait(recv_req_event_);
      SSLN_LOG_INFO(file_logger, "[{}], recv_req_event received", this->name());
    }

    std::vector<char> data_vec;
    if (recv_req_Q_->try_dequeue(data_vec)) {
      auto* req_payload = reinterpret_cast<SigPayload*>(data_vec.data());
      req_payload->data = reinterpret_cast<uint8_t*>(req_payload) + sizeof(SigPayload);

      HandleOutRequest(req_payload);
    }
  }
}

}  // namespace hybrid
}  // namespace ssln