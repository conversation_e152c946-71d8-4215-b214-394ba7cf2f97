#include "sideband_sig_bridge.h"
#include <gtest/gtest.h>
#include <systemc>
#include <tlm>
#include <thread>

using namespace ssln::hybrid;
using namespace umi_sig;
using namespace std;
using namespace sc_core;

const int sleep_time = 200;

class ScStopModule: public sc_core::sc_module{
public:
    SC_HAS_PROCESS(ScStopModule);
    ScStopModule(sc_core::sc_module_name name): sc_core::sc_module(name){
        stop_event = new async_event("stop_event");
        SC_THREAD(StopThread);
    }

    void StopThread(){
        while(true){
            wait(*stop_event);
            sc_core::sc_stop();
        }
    }
    ~ScStopModule(){
        delete stop_event;
    }
    async_event* stop_event;
};

class SendXtorSigModule: public sc_module{
public:
    SC_HAS_PROCESS(SendXtorSigModule);
    SendXtorSigModule(sc_module_name nm, SidebandSigBridge* bridge_): sc_module(nm){
        bridge = bridge_;
        SC_THREAD(send_xtor_thread);
    }
    ~SendXtorSigModule(){

    }

    void send_xtor_thread(){
        while(true){
            wait(send_xtor_sig_event);
            block = true;
            for(auto &i: send_xtor_list){
                bridge->HandleRequest(i);
            }
            block = false;
        }
    }


    SidebandSigBridge* bridge;
    vector<xtor_sig_transaction_item*> send_xtor_list;
    async_event send_xtor_sig_event;
    bool block;
};

class SidebandSigBridgeTest: public testing::Test{
public:
    void SetUp() override{
        string channel_name = "sideband_bridge_test";
        sig_proxy = make_shared<xtor_sig_proxy>();
        sig_bridge = new SidebandSigBridge("sig_bridge", channel_name, sig_proxy, n_sig_in, n_sig_out);
        
        send_channel = new ipc::channel((channel_name + "_tlm2hw").c_str(), ipc::sender);
        recv_channel = new ipc::channel((channel_name + "_hw2tlm").c_str(), ipc::receiver);

        
        send_xtor_sig = new SendXtorSigModule("send_xtor_sig", sig_bridge);
        sc_stop_module = new ScStopModule("stopmodule");

        sim_thread = new thread(
            [](){
                sc_core::sc_start();
            }
        );
    }

    void TearDown() override{
        sc_stop_module->stop_event->notify();
        if(sim_thread->joinable()){
            sim_thread->join();
        }
        send_channel->disconnect();
        recv_channel->disconnect();
        delete send_channel;
        delete recv_channel;
        delete sim_thread;
        delete send_xtor_sig;
        delete sig_bridge;
        delete sc_stop_module;
        sc_core::sc_curr_simcontext = new sc_core::sc_simcontext();
        sc_core::sc_default_global_context = nullptr;
    }

    thread* sim_thread;
    SidebandSigBridge* sig_bridge;
    SendXtorSigModule* send_xtor_sig;
    shared_ptr<xtor_sig_proxy> sig_proxy;
    ScStopModule* sc_stop_module;

    ipc::channel* send_channel;
    ipc::channel* recv_channel;

    uint32_t n_sig_in{7};
    uint32_t n_sig_out{8};
};


TEST_F(SidebandSigBridgeTest, TLM2HW){
    int pos_index = 3;
    uint32_t sig_payload_size = sizeof(SigPayload) + (n_sig_out + 7) / 8;
    uint8_t* sig_payload_ptr = new uint8_t [sig_payload_size];
    SigPayload* sig_payload = reinterpret_cast<SigPayload*>(sig_payload_ptr);
    sig_payload->id = 0;
    sig_payload->type = SigType::tlm2hw_req;
    sig_payload->signal_width = 8;
    sig_payload->data = sig_payload_ptr + sizeof(SigPayload);
    
    uint32_t sig_count_bytes = (n_sig_out + 7) / 8;
    memset(sig_payload->data, 0, sig_count_bytes);
    for(int i = 0; i < n_sig_out; i++){
        if(i == pos_index){
            sig_payload->data[i / 8] |= (1 << (i % 8));
        }
    }

    send_channel->send(sig_payload, sig_payload_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    //check
    EXPECT_EQ(sig_proxy->nb_send_list.size(), 1);
    xtor_sig_transaction_item& item = sig_proxy->nb_send_list[0];
    EXPECT_EQ(item.get_signal_width(), 8);
    EXPECT_EQ(*item.signals, (1 << pos_index));


    auto recv_data = recv_channel->recv();
    EXPECT_EQ(recv_data.size(), sizeof(SigPayload));
    SigPayload* recv_payload = reinterpret_cast<SigPayload*>(recv_data.data());

    //check
    EXPECT_EQ(recv_payload->id, 0);
    EXPECT_EQ(recv_payload->type, SigType::tlm2hw_reply);
    EXPECT_EQ(recv_payload->signal_width, n_sig_out);

    delete [] sig_payload_ptr;
}



TEST_F(SidebandSigBridgeTest, HW2TLM){
    int pos_index = 3;
    xtor_sig_transaction_item* item = new xtor_sig_transaction_item();
    item->set_signal_width(n_sig_in);
    item->signals = new uint32_t;
    (*item->signals) = (1 << pos_index);
    send_xtor_sig->send_xtor_list.push_back(item);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    send_xtor_sig->send_xtor_sig_event.notify();
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    auto recv_data = recv_channel->recv();
    EXPECT_EQ(recv_data.size(), sizeof(SigPayload) + (n_sig_in + 7) / 8);

    SigPayload* recv_payload = reinterpret_cast<SigPayload*>(recv_data.data());

    EXPECT_EQ(recv_payload->id, 0);
    EXPECT_EQ(recv_payload->type, SigType::hw2tlm_req);
    EXPECT_EQ(recv_payload->signal_width, n_sig_in);
    
    for(int i = 0; i < n_sig_in; i++){
        uint32_t val = (recv_payload->data[i / 8] >> (i % 8)) & 1;
        EXPECT_EQ(val, i == pos_index ? 1 : 0);
    }

    EXPECT_EQ(send_xtor_sig->block, true);


    // response
    uint32_t sig_payload_size = sizeof(SigPayload);
    uint8_t* sig_payload_ptr = new uint8_t [sig_payload_size];
    SigPayload* sig_payload = reinterpret_cast<SigPayload*>(sig_payload_ptr);
    sig_payload->id = 0;
    sig_payload->type = SigType::hw2tlm_reply;
    sig_payload->signal_width = n_sig_in;
    sig_payload->data = nullptr;

    send_channel->send(sig_payload, sig_payload_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(send_xtor_sig->block, false);

}


int sc_main(int argc, char* argv[]) {
    return 0;
}