#include "sideband_sig_bridge.h"

#include <memory>
#include <string>
#include <thread>
#include <unordered_map>
#include <vector>

#include <gtest/gtest.h>
#include <systemc>
#include <tlm>

#include "async_event.h"
#include "dispatcher/include/dispatcher.h"
#include "xtor_sig_proxy.h"

namespace ssln {
namespace hybrid {

constexpr int kSleepTime = 200;

class ScStopModule : public sc_core::sc_module {
 public:
  SC_HAS_PROCESS(ScStopModule);
  explicit ScStopModule(sc_core::sc_module_name name)
      : sc_core::sc_module(name) {
    stop_event_ = std::make_unique<async_event>("stop_event");
    SC_THREAD(StopThread);
  }

  void StopThread() {
    while (true) {
      wait(*stop_event_);
      sc_core::sc_stop();
    }
  }

  ~ScStopModule() = default;

  std::unique_ptr<async_event> stop_event_;
};

class SendXtorSigModule : public sc_core::sc_module {
 public:
  SC_HAS_PROCESS(SendXtorSigModule);
  SendXtorSigModule(sc_core::sc_module_name nm, SidebandSigBridge* bridge)
      : sc_core::sc_module(nm), bridge_(bridge) {
    SC_THREAD(SendXtorThread);
  }

  ~SendXtorSigModule() = default;

  void SendXtorThread() {
    while (true) {
      wait(send_xtor_sig_event_);
      block_ = true;
      for (auto* item : send_xtor_list_) {
        bridge_->HandleRequest(item);
      }
      block_ = false;
    }
  }

  SidebandSigBridge* bridge_;
  std::vector<umi_sig::xtor_sig_transaction_item*> send_xtor_list_;
  async_event send_xtor_sig_event_;
  bool block_ = false;
};

// Mock dispatcher for testing
class MockDispatcher : public Dispatcher {
 public:
  bool RegisterEndpoint(IEndpoint* endpoint) override {
    endpoints_[endpoint->GetComponentId()] = endpoint;
    return true;
  }

  bool Send(uint32_t destination_id, const void* buffer, size_t size) override {
    auto it = endpoints_.find(destination_id);
    if (it != endpoints_.end()) {
      return it->second->HandleData(buffer, size);
    }
    return false;
  }

  void Start() override {}
  void Stop() override {}

 private:
  std::unordered_map<uint32_t, IEndpoint*> endpoints_;
};

class SidebandSigBridgeTest : public ::testing::Test {
 public:
  void SetUp() override {
    const std::string bfm_hdl_path = "test_bfm_path";
    constexpr uint32_t kCompId = 1;
    constexpr uint32_t kDestId = 2;

    mock_dispatcher_ = std::make_unique<MockDispatcher>();
    sig_bridge_ = std::make_unique<SidebandSigBridge>(
        "sig_bridge", mock_dispatcher_.get(), kCompId, kDestId,
        bfm_hdl_path, n_sig_in_, n_sig_out_);

    send_xtor_sig_ = std::make_unique<SendXtorSigModule>("send_xtor_sig", sig_bridge_.get());
    sc_stop_module_ = std::make_unique<ScStopModule>("stopmodule");

    sim_thread_ = std::make_unique<std::thread>([]() {
      sc_core::sc_start();
    });
  }

  void TearDown() override {
    sc_stop_module_->stop_event_->notify();
    if (sim_thread_->joinable()) {
      sim_thread_->join();
    }

    // Reset SystemC simulation context
    sc_core::sc_curr_simcontext = new sc_core::sc_simcontext();
    sc_core::sc_default_global_context = nullptr;
  }

 private:
  std::unique_ptr<std::thread> sim_thread_;
  std::unique_ptr<SidebandSigBridge> sig_bridge_;
  std::unique_ptr<SendXtorSigModule> send_xtor_sig_;
  std::unique_ptr<ScStopModule> sc_stop_module_;
  std::unique_ptr<MockDispatcher> mock_dispatcher_;

  uint32_t n_sig_in_{7};
  uint32_t n_sig_out_{8};
};


TEST_F(SidebandSigBridgeTest, TLM2HW) {
  constexpr int kPosIndex = 3;
  constexpr uint32_t kSignalWidth = 8;

  uint32_t sig_payload_size = sizeof(SigPayload) + (kSignalWidth + 7) / 8;
  auto sig_payload_ptr = std::make_unique<uint8_t[]>(sig_payload_size);
  auto* sig_payload = reinterpret_cast<SigPayload*>(sig_payload_ptr.get());

  sig_payload->id = 0;
  sig_payload->type = SigType::tlm2hw_req;
  sig_payload->signal_width = kSignalWidth;
  sig_payload->data = sig_payload_ptr.get() + sizeof(SigPayload);

  uint32_t sig_count_bytes = (kSignalWidth + 7) / 8;
  std::memset(sig_payload->data, 0, sig_count_bytes);

  // Set the bit at pos_index
  sig_payload->data[kPosIndex / 8] |= (1 << (kPosIndex % 8));

  // Send through dispatcher (this would normally be handled by the proxy)
  EXPECT_TRUE(sig_bridge_->HandleData(sig_payload, sig_payload_size));

  std::this_thread::sleep_for(std::chrono::milliseconds(kSleepTime));

  // Note: In a real test, we would need to verify the signal proxy received the transaction
  // This would require either mocking the signal proxy or checking its internal state
}



TEST_F(SidebandSigBridgeTest, HW2TLM) {
  constexpr int kPosIndex = 3;

  auto item = std::make_unique<umi_sig::xtor_sig_transaction_item>();
  item->set_signal_width(n_sig_in_);
  auto signal_val = std::make_unique<uint32_t>(1 << kPosIndex);
  item->signals = signal_val.get();

  send_xtor_sig_->send_xtor_list_.push_back(item.get());
  std::this_thread::sleep_for(std::chrono::milliseconds(kSleepTime));
  send_xtor_sig_->send_xtor_sig_event_.notify();
  std::this_thread::sleep_for(std::chrono::milliseconds(kSleepTime));

  // In the new dispatcher-based architecture, the signal would be sent through
  // the dispatcher to the destination component. For testing, we would need to
  // verify that the dispatcher's send method was called with the correct data.

  EXPECT_EQ(send_xtor_sig_->block_, true);

  // Clean up
  send_xtor_sig_->send_xtor_list_.clear();
}

}  // namespace hybrid
}  // namespace ssln

int sc_main(int argc, char* argv[]) {
  return 0;
}