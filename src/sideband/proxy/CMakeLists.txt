add_library(sideband_sig_tlm_proxy
    src/sideband_sig_tlm_proxy.cc
)

target_include_directories(sideband_sig_tlm_proxy
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}/include
        $ENV{UMICOM_HOME}/include
        $ENV{UMICOM_HOME}/include/systemc
)

target_link_libraries(sideband_sig_tlm_proxy
    PUBLIC
        common
        dispatcher
        axi_common
        sslogger
)

#target_compile_definitions(sideband_sig_tlm_proxy
#    PUBLIC
#        SC_CPLUSPLUS=201402L
#)

#target_compile_features(sideband_sig_tlm_proxy
#    PUBLIC
#       cxx_std_17
#) 

install(TARGETS sideband_sig_tlm_proxy
    LIBRARY DESTINATION ${CMAKE_INSTALL_PREFIX}/${LIB_VERSION}
    ARCHIVE DESTINATION ${CMAKE_INSTALL_PREFIX}/${LIB_VERSION}
)
add_subdirectory(tests EXCLUDE_FROM_ALL)