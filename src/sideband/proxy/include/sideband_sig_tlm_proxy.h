#ifndef SSLN_HYBRID_SIDEBAND_SIG_TLM_PROXY_H
#define SSLN_HYBRID_SIDEBAND_SIG_TLM_PROXY_H

#include <systemc>
#include <vector>
#include <queue>
#include <thread>
#include <readerwriterqueue.h>
#include <atomic>
#include "sig_payload.h"
#include "async_event.h"
#include "dispatcher/include/dispatcher.h"
#include "dispatcher/include/i_endpoint.h"

namespace ssln{
namespace hybrid{

class SidebandSigTlmProxy: public sc_core::sc_module, public IEndpoint {

public:
    SC_HAS_PROCESS(SidebandSigTlmProxy);
    SidebandSigTlmProxy(sc_core::sc_module_name nm, Dispatcher* dispatcher, uint32_t comp_id, uint32_t dest_id, uint32_t n_sig_in, uint32_t n_sig_out, bool sig_in_default=false);
    ~SidebandSigTlmProxy();

    std::vector<sc_core::sc_out<bool>* > sig_in_proxy;
    std::vector<sc_core::sc_in<bool>* > sig_out_proxy;

private:

    // IEndpoint interface
    bool handle_data(const void* data, size_t size) override;
    uint32_t get_component_id() const override;

    void ScProcessReq();
    void ScSensitiveOut();
    void ScSendReq();

    uint32_t n_sig_in;
    uint32_t n_sig_out;

    Dispatcher* dispatcher_;
    uint32_t comp_id_;
    uint32_t dest_id_;

    moodycamel::ReaderWriterQueue<std::vector<char>>* recv_req_Q;
    std::queue<SigPayload*> tlm2hw_req_Q;

    async_event recv_rep_event;
    async_event recv_req_event;
    sc_core::sc_event sensitive_out;

    std::atomic<bool> running_{true};
    std::atomic<uint64_t> id_count{0};


};


} // namespace hybrid
} // namespace ssln


#endif