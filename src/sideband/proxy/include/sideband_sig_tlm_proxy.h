#ifndef SSLN_HYBRID_SIDEBAND_SIG_TLM_PROXY_H_
#define SSLN_HYBRID_SIDEBAND_SIG_TLM_PROXY_H_

#include <atomic>
#include <cstdint>
#include <queue>
#include <thread>
#include <vector>

#include <systemc>
#include <readerwriterqueue.h>

#include "async_event.h"
#include "dispatcher/include/dispatcher.h"
#include "dispatcher/include/i_endpoint.h"
#include "sig_payload.h"

namespace ssln {
namespace hybrid {

/**
 * @brief TLM proxy for sideband signals using dispatcher
 *
 * This class provides a TLM interface for sideband signals, allowing
 * SystemC modules to interact with sideband signals through the dispatcher
 * communication system.
 */
class SidebandSigTlmProxy : public sc_core::sc_module, public IEndpoint {
 public:
  SC_HAS_PROCESS(SidebandSigTlmProxy);

  /**
   * @brief Construct a new SidebandSigTlmProxy
   *
   * @param nm SystemC module name
   * @param dispatcher Pointer to the dispatcher for communication
   * @param comp_id Component ID for this proxy
   * @param dest_id Destination component ID for communication
   * @param n_sig_in Number of input signals
   * @param n_sig_out Number of output signals
   * @param sig_in_default Default value for input signals
   */
  SidebandSigTlmProxy(sc_core::sc_module_name nm,
                      Dispatcher* dispatcher,
                      uint32_t comp_id,
                      uint32_t dest_id,
                      uint32_t n_sig_in,
                      uint32_t n_sig_out,
                      bool sig_in_default = false);

  ~SidebandSigTlmProxy();

  std::vector<sc_core::sc_out<bool>*> sig_in_proxy;
  std::vector<sc_core::sc_in<bool>*> sig_out_proxy;

 private:
  // IEndpoint interface implementation
  bool HandleData(const void* data, size_t size) override;
  uint32_t GetComponentId() const override;

  /**
   * @brief SystemC thread for processing requests
   */
  void ScProcessReq();

  /**
   * @brief SystemC thread for handling output signal sensitivity
   */
  void ScSensitiveOut();

  /**
   * @brief SystemC thread for sending requests
   */
  void ScSendReq();

  // Signal configuration
  uint32_t n_sig_in_;
  uint32_t n_sig_out_;

  // Dispatcher communication
  Dispatcher* dispatcher_;
  uint32_t comp_id_;
  uint32_t dest_id_;

  // Request queues and synchronization
  moodycamel::ReaderWriterQueue<std::vector<char>>* recv_req_Q_;
  std::queue<SigPayload*> tlm2hw_req_Q_;

  async_event recv_rep_event_;
  async_event recv_req_event_;
  sc_core::sc_event sensitive_out_;

  // State management
  std::atomic<bool> running_{true};
  std::atomic<uint64_t> id_count_{0};
};

}  // namespace hybrid
}  // namespace ssln

#endif  // SSLN_HYBRID_SIDEBAND_SIG_TLM_PROXY_H_