#ifndef _UV_SIDEBAND_PROXY_WRAPPER_H_
#define _UV_SIDEBAND_PROXY_WRAPPER_H_

#include "sideband_sig_tlm_proxy.h"

/**
 * @brief uv_sideband_sig_proxy_wrapper ,
 * @uv_class_name uv_sideband_sig_proxy_wrapper
 * @uv_model_name uv_sideband_sig_proxy_wrapper
 * @uv_array_port sig_in_proxy_wrapper N_SIG_IN
 * @uv_array_port sig_out_proxy_wrapper N_SIG_OUT
 * @uv_lib_path_third ${HYBRID_ROOT}/lib/Linux64_GCC-10.3.0_Release/,${HYBRID_ROOT}/src/thirdparty/install/lib/
 * @uv_lib_third ipc,sslogger
 * @uv_include_path_third ${HYBRID_ROOT}/lib/sideband/proxy/include,${HYBRID_ROOT}/lib/common/include,${HYBRID_ROOT}/lib/sslogger/include,${HYBRID_ROOT}/src/thirdparty/install/include
 */
template<const char* CHANNEL_NAME="sideband_channel", uint32_t N_SIG_IN=1, uint32_t N_SIG_OUT=0, bool DEFAULT_IN=false>
class uv_sideband_sig_proxy_wrapper: protected ssln::hybrid::SidebandSigTlmProxy{
public:
    uv_sideband_sig_proxy_wrapper(sc_core::sc_module_name name): SidebandSigTlmProxy(name, CHANNEL_NAME, N_SIG_IN, N_SIG_OUT, DEFAULT_IN){
        for(auto &r: sig_in_proxy){
            sig_in_proxy_wrapper.push_back(r);
        }
        for(auto &r: sig_out_proxy){
            sig_out_proxy_wrapper.push_back(r);
        }
    }

public:
    std::vector<sc_out<bool>* > sig_in_proxy_wrapper;
    std::vector<sc_in<bool>* > sig_out_proxy_wrapper;
};


#endif