#include "sideband_sig_tlm_proxy.h"
#include <cstring>
#include "ssln/sslogger.h"
using namespace ssln::hybrid;
using namespace sc_core;
using namespace std;

SidebandSigTlmProxy::SidebandSigTlmProxy(sc_core::sc_module_name nm, const string& channel_name, uint32_t n_sig_in_, uint32_t n_sig_out_, bool sig_in_default) :
    sc_module(nm)
    , n_sig_in(n_sig_in_)
    , n_sig_out(n_sig_out_){
    

    // ipc::channel::clear_storage((channel_name + "_tlm2hw").c_str());
    send_channel = new ipc::channel((channel_name + "_tlm2hw").c_str(), ipc::sender);
    recv_channel = new ipc::channel((channel_name + "_hw2tlm").c_str(), ipc::receiver);

    recv_req_Q = new moodycamel::ReaderWriterQueue<ipc::buffer*>();

    for(int i = 0; i < n_sig_in; i++){
        sig_in_proxy.push_back(new sc_out<bool>(("sig_in_proxy_" + to_string(i)).c_str()));
    }
    for(auto& i: sig_in_proxy){
        i->initialize(sig_in_default);
    }

    for(int i = 0; i < n_sig_out; i++){
        sig_out_proxy.push_back(new sc_in<bool>(("sig_out_proxy_" + to_string(i)).c_str()));
    }

    SC_THREAD(ScProcessReq);
    SC_THREAD(ScSensitiveOut);
    SC_THREAD(ScSendReq);
    process_thread = make_unique<thread>(&SidebandSigTlmProxy::ProcessMsg, this);

}

SidebandSigTlmProxy::~SidebandSigTlmProxy(){
    running_ = false;
    if(process_thread && process_thread->joinable()){
        process_thread->join();
    }
    send_channel->disconnect();
    recv_channel->disconnect();
    delete send_channel;
    delete recv_channel;
    delete recv_req_Q;

}

void SidebandSigTlmProxy::ScProcessReq(){
    while(true){
        if(recv_req_Q->peek() == nullptr){
            SSLN_LOG_INFO(file_logger, "[{}], wait recv_req_event start", this->name());
            wait(recv_req_event);
            SSLN_LOG_INFO(file_logger, "[{}], wait recv_req_event end", this->name());
            continue;
        }
        ipc::buffer* data;
        
        recv_req_Q->try_dequeue(data);
        SigPayload* req_payload = reinterpret_cast<SigPayload*>(data->data());
        SSLN_LOG_DEBUG(file_logger, "[{}], {}", this->name(), *req_payload);
        if(req_payload->type == SigType::hw2tlm_req){
            uint32_t signal_width = req_payload->signal_width;
            std::cout << "signal_width: " << signal_width << std::endl;
            
            for(int i = 0; i < min(n_sig_in, signal_width); i++){
                uint32_t index = i / 8;
                uint32_t offset = i % 8;
                if((req_payload->data[index] >> offset) & 1){
                    sig_in_proxy[i]->write(true);
                }
                else{
                    sig_in_proxy[i]->write(false);
                }
            }
        }

        wait(SC_ZERO_TIME);

        // send reply
        uint32_t sig_reply_size = sizeof(SigPayload);
        SigPayload* reply_payload = new SigPayload();
        reply_payload->id = req_payload->id;
        reply_payload->type = SigType::hw2tlm_reply;
        reply_payload->signal_width = req_payload->signal_width;
        reply_payload->data = nullptr;
        SSLN_LOG_INFO(file_logger, "[{}], send reply size: {}", this->name(), sig_reply_size);
        SSLN_LOG_DEBUG(file_logger, "[{}], {}", this->name(), *reply_payload);
        std::cout << "send channel send" << std::endl;
        send_channel->send(reply_payload, sig_reply_size);
        
        delete data;
        delete reply_payload;
    }
}

void SidebandSigTlmProxy::ScSensitiveOut(){
    if(sig_out_proxy.size() == 0){
        return;
    }
    while(true){
        sc_event_or_list event_list;
        for(int i = 0; i < sig_out_proxy.size(); i++){
            event_list |= sig_out_proxy[i]->value_changed_event();
        }
        wait(event_list);
        uint32_t req_size = sizeof(SigPayload);
        req_size += (n_sig_out + 7 ) / 8;
        uint8_t* tlm2hw_req_ptr = new uint8_t [req_size];
        memset(tlm2hw_req_ptr, 0, req_size);
        SigPayload* tlm2hw_req_payload = reinterpret_cast<SigPayload*>(tlm2hw_req_ptr);
        tlm2hw_req_payload->id = id_count.fetch_add(1, memory_order_relaxed);
        tlm2hw_req_payload->type = SigType::tlm2hw_req;
        tlm2hw_req_payload->signal_width = n_sig_out;
        tlm2hw_req_payload->data = tlm2hw_req_ptr + sizeof(SigPayload);
        uint8_t* data_ptr = tlm2hw_req_payload->data;
        for(int i = 0; i < n_sig_out; i++){
            uint32_t index = i / 8;
            uint32_t offset = i % 8;
            if(sig_out_proxy[i]->read()){
                data_ptr[index] |= data_ptr[index] | (1 << offset);
            }
        }
        SSLN_LOG_DEBUG(file_logger, "[{}], push Q: {}", this->name(), *tlm2hw_req_payload);
        tlm2hw_req_Q.push(tlm2hw_req_payload);
        sensitive_out.notify();
        SSLN_LOG_INFO(file_logger, "[{}], sensitive_out notify", this->name());
    }
}

void SidebandSigTlmProxy::ScSendReq(){
    while(true){
        if(tlm2hw_req_Q.empty()){
            wait(sensitive_out);
        }
        SigPayload* req_payload = tlm2hw_req_Q.front();
        tlm2hw_req_Q.pop();
        uint32_t msg_size = sizeof(SigPayload) + (req_payload->signal_width + 7) / 8;
        SSLN_LOG_INFO(file_logger, "[{}], send request size: {}", this->name(), msg_size);
        send_channel->send(req_payload, msg_size);
        
        SSLN_LOG_INFO(file_logger, "[{}], wait recv_rep_event start", this->name());
        wait(recv_rep_event);
        SSLN_LOG_INFO(file_logger, "[{}], wait recv_rep_event end", this->name());
    }
}


void SidebandSigTlmProxy::ProcessMsg(){
    while(running_){
        ipc::buffer* data = new ipc::buffer();
        (*data) = recv_channel->recv(1000);
        if (data->empty()){
            delete data;
            continue;
        }
        SSLN_LOG_INFO(file_logger, "[{}], recv msg size: {}", this->name(), data->size());
        SigPayload* sig_payload = reinterpret_cast<SigPayload*>(data->data());
        sig_payload->data = reinterpret_cast<uint8_t*>(sig_payload) + sizeof(SigPayload);
        if(sig_payload->type == SigType::hw2tlm_req){
            SSLN_LOG_INFO(file_logger, "[{}], recv_req_event notify1", this->name());
            std::cout << "dt1" << std::endl;
            recv_req_Q->try_enqueue(data);
            SSLN_LOG_INFO(file_logger, "[{}], recv_req_event notify2", this->name());
            recv_req_event.notify(sc_core::SC_ZERO_TIME);
            SSLN_LOG_INFO(file_logger, "[{}], recv_req_event notify3", this->name());
        }
        else if(sig_payload->type == SigType::tlm2hw_reply){
            SSLN_LOG_INFO(file_logger, "[{}], recv_rep_event notify1", this->name());
            recv_rep_event.notify(sc_core::SC_ZERO_TIME);
            SSLN_LOG_INFO(file_logger, "[{}], recv_rep_event notify2", this->name());
            delete data;
        }
    }
}

