
add_executable(sideband_sig_tlm_proxy_test
    src/sideband_sig_tlm_proxy_test.cc
)

target_link_directories(sideband_sig_tlm_proxy_test
    PRIVATE
        $ENV{UMICOM_HOME}/lib
        ${PROJECT_ROOT}/thirdparty/fake_install/lib
)

target_link_libraries(sideband_sig_tlm_proxy_test
    PRIVATE
        GTest::gtest_main
        fake_xtor
        sideband_sig_tlm_proxy
        systemc
)

include(GoogleTest)
gtest_discover_tests(sideband_sig_tlm_proxy_test)

add_dependencies(build_utests sideband_sig_tlm_proxy_test)