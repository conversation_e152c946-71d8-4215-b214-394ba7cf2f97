#include "sideband_sig_tlm_proxy.h"
#include <gtest/gtest.h>
#include <systemc>
#include <tlm>
#include <thread>

using namespace ssln::hybrid;
using namespace std;
using namespace sc_core;

const int sleep_time = 200;

class ScStopModule: public sc_core::sc_module{
public:
    SC_HAS_PROCESS(ScStopModule);
    ScStopModule(sc_core::sc_module_name name): sc_core::sc_module(name){
        stop_event = new async_event("stop_event");
        SC_THREAD(StopThread);
    }

    void StopThread(){
        while(true){
            wait(*stop_event);
            sc_core::sc_stop();
        }
    }
    ~ScStopModule(){
        delete stop_event;
    }
    async_event* stop_event;
};

class FakeDevice: public sc_module{
public:
    SC_HAS_PROCESS(FakeDevice);
    FakeDevice(sc_core::sc_module_name name, int n_in_, int n_out_):sc_module(name),n_out(n_out_),n_in(n_in_){
        for(int i = 0; i < n_out; i++){
            irq_out.push_back(new sc_out<bool>(("irq_out_" + to_string(i)).c_str()));
        }
        for(int i = 0; i < n_in; i++){
            irq_in.push_back(new sc_in<bool>(("irq_in_" + to_string(i)).c_str()));
        }

        SC_THREAD(send_seq_out_thread);
        SC_THREAD(check_seq_in_thread);
    }

    void send_seq_out_thread(){
        while(true){
            wait(send_seq_out_event);
            for(int i = 0; i < n_out; i++){
                irq_out[i]->write(seq_out[i]);
            }
            wait(SC_ZERO_TIME);
        }
    
    }

    void check_seq_in_thread(){
        while(true){
            sc_event_or_list or_list;
            for(int i = 0; i < n_in; i++){
                or_list |= irq_in[i]->value_changed_event();
            }
            wait(or_list);
            for(int i = 0; i < n_in; i++){
                seq_in.push_back(irq_in[i]->read());
            }
        }
    }


    vector<bool> seq_out;
    vector<bool> seq_in;
    async_event send_seq_out_event;
    int n_out;
    int n_in;
    vector<sc_out<bool>* > irq_out;
    vector<sc_in<bool>* > irq_in;
};


class SidebandSigTlmProxyTest: public testing::Test{
public:
    void SetUp() override{
        string channel_name = "sideband_tlm_proxy_test";
        ipc::channel::clear_storage((channel_name + "_hw2tlm").c_str());
        ipc::channel::clear_storage((channel_name + "_tlm2hw").c_str());
        send_channel = new ipc::channel((channel_name + "_hw2tlm").c_str(), ipc::sender);
        recv_channel = new ipc::channel((channel_name + "_tlm2hw").c_str(), ipc::receiver);

        sig_tlm_proxy = new SidebandSigTlmProxy("sig_tlm_proxy", channel_name, n_sig_in, n_sig_out);
        fake_device = new FakeDevice("fake_device", n_sig_in, n_sig_out);
        sc_stop_module = new ScStopModule("stopmodule");

        // connect
        for(int i = 0; i < n_sig_out; i++){
            sc_signal<bool>* sig = new sc_signal<bool>(("signal_out_" + to_string(i)).c_str());
            fake_device->irq_out[i]->bind(*sig);
            sig_tlm_proxy->sig_out_proxy[i]->bind(*sig);
            v_signal.push_back(sig);
        }
        for(int i = 0; i < n_sig_in; i++){
            sc_signal<bool>* sig = new sc_signal<bool>(("signal_in_" + to_string(i)).c_str());
            fake_device->irq_in[i]->bind(*sig);
            sig_tlm_proxy->sig_in_proxy[i]->bind(*sig);
            v_signal.push_back(sig);
        }

        sim_thread = new thread(
            [](){
                sc_core::sc_start();
            }
        );
    }
    void TearDown() override{
        sc_stop_module->stop_event->notify();
        if(sim_thread->joinable()){
            sim_thread->join();
        }
        delete sim_thread;
        send_channel->disconnect();
        recv_channel->disconnect();
        delete send_channel;
        delete recv_channel;
        delete sig_tlm_proxy;
        delete sc_stop_module;
        delete fake_device;
        for(auto i: v_signal){
            delete i;
        }
        sc_core::sc_curr_simcontext = new sc_core::sc_simcontext();
        sc_core::sc_default_global_context = nullptr;
    }


    thread* sim_thread;
    SidebandSigTlmProxy* sig_tlm_proxy;
    FakeDevice* fake_device;
    ScStopModule* sc_stop_module;
    vector<sc_signal<bool>*> v_signal;

    uint32_t n_sig_in{7};
    uint32_t n_sig_out{8};

    ipc::channel* send_channel;
    ipc::channel* recv_channel;

};


TEST_F(SidebandSigTlmProxyTest, TLM2HW){
    int pos_index = 3;
    for(int i = 0; i < n_sig_out; i++){
        if(i == pos_index){
            fake_device->seq_out.push_back(true);
        }
        else{
            fake_device->seq_out.push_back(false);
        }
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    fake_device->send_seq_out_event.notify();
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    auto recv_data = recv_channel->recv();
    EXPECT_EQ(recv_data.size(), sizeof(SigPayload) + (n_sig_out + 7) / 8);

    SigPayload* recv_payload = reinterpret_cast<SigPayload*>(recv_data.data());
    EXPECT_EQ(recv_payload->id, 0);
    EXPECT_EQ(recv_payload->type, SigType::tlm2hw_req);
    EXPECT_EQ(recv_payload->signal_width, n_sig_out);
    for(int i = 0; i < n_sig_out; i++){
        uint32_t val = (recv_payload->data[i / 8] >> (i % 8)) & 1;
        EXPECT_EQ(val, i == pos_index ? 0x1 : 0x0);
    }

    // resp
    uint32_t sig_payload_size = sizeof(SigPayload);
    uint8_t* sig_payload_ptr = new uint8_t [sig_payload_size];
    SigPayload* sig_payload = reinterpret_cast<SigPayload*>(sig_payload_ptr);
    sig_payload->id = 0;
    sig_payload->type = SigType::tlm2hw_reply;
    sig_payload->signal_width = n_sig_out;
    sig_payload->data = nullptr;

    send_channel->send(sig_payload, sig_payload_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    delete [] sig_payload_ptr;
}


TEST_F(SidebandSigTlmProxyTest, HW2TLM){
    int pos_index = 3;
    uint32_t sig_payload_size = sizeof(SigPayload) + (n_sig_in + 7) / 8;
    uint8_t* sig_payload_ptr = new uint8_t [sig_payload_size];
    SigPayload* sig_payload = reinterpret_cast<SigPayload*>(sig_payload_ptr);
    sig_payload->id = 0;
    sig_payload->type = SigType::hw2tlm_req;
    sig_payload->signal_width = n_sig_in;
    sig_payload->data = sig_payload_ptr + sizeof(SigPayload);

    uint32_t sig_count_bytes = (n_sig_in + 7) / 8;
    memset(sig_payload->data, 0, sig_count_bytes);

    for(int i = 0; i < n_sig_out; i++){
        if(i == pos_index){
            sig_payload->data[i / 8] |= (1 << (i % 8));
        }
    }

    send_channel->send(sig_payload, sig_payload_size);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    auto recv_data = recv_channel->recv();
    EXPECT_EQ(recv_data.size(), sizeof(SigPayload));
    SigPayload* recv_payload = reinterpret_cast<SigPayload*>(recv_data.data());

    EXPECT_EQ(recv_payload->id, 0);
    EXPECT_EQ(recv_payload->type, SigType::hw2tlm_reply);
    EXPECT_EQ(recv_payload->signal_width, n_sig_in);


    EXPECT_EQ(fake_device->seq_in.size(), n_sig_in);
    for(int i = 0; i < n_sig_in; i++){
        bool high = false;
        if(i == pos_index) high = true;
        EXPECT_EQ(fake_device->seq_in[i], high);
    }

    delete [] sig_payload_ptr;
}


int sc_main(int argc, char* argv[]) {
    return 0;
}