#include "sideband_sig_bridge.h"
#include "sideband_sig_tlm_proxy.h"
#include <gtest/gtest.h>
#include <systemc>
#include <tlm>
#include <thread>


using namespace ssln::hybrid;
using namespace umi_sig;
using namespace std;
using namespace sc_core;

const int sleep_time = 200;

class ScStopModule: public sc_core::sc_module{
public:
    SC_HAS_PROCESS(ScStopModule);
    ScStopModule(sc_core::sc_module_name name): sc_core::sc_module(name){
        stop_event = new async_event("stop_event");
        SC_THREAD(StopThread);
    }

    void StopThread(){
        while(true){
            wait(*stop_event);
            sc_core::sc_stop();
        }
    }
    ~ScStopModule(){
        delete stop_event;
    }
    async_event* stop_event;
};

class FakeDevice: public sc_module{
public:
    SC_HAS_PROCESS(FakeDevice);
    FakeDevice(sc_core::sc_module_name name, int n_in_, int n_out_):sc_module(name),n_out(n_out_),n_in(n_in_){
        for(int i = 0; i < n_out; i++){
            irq_out.push_back(new sc_out<bool>(("irq_out_" + to_string(i)).c_str()));
        }
        for(int i = 0; i < n_in; i++){
            irq_in.push_back(new sc_in<bool>(("irq_in_" + to_string(i)).c_str()));
        }

        SC_THREAD(send_seq_out_thread);
        SC_THREAD(check_seq_in_thread);
    }

    void send_seq_out_thread(){
        while(true){
            wait(send_seq_out_event);
            for(int i = 0; i < n_out; i++){
                irq_out[i]->write(seq_out[i]);
            }
            wait(SC_ZERO_TIME);
        }
    
    }

    void check_seq_in_thread(){
        while(true){
            sc_event_or_list or_list;
            for(int i = 0; i < n_in; i++){
                or_list |= irq_in[i]->value_changed_event();
            }
            wait(or_list);
            for(int i = 0; i < n_in; i++){
                seq_in.push_back(irq_in[i]->read());
            }
        }
    }


    vector<bool> seq_out;
    vector<bool> seq_in;
    async_event send_seq_out_event;
    int n_out;
    int n_in;
    vector<sc_out<bool>* > irq_out;
    vector<sc_in<bool>* > irq_in;
};

class SendXtorSigModule: public sc_module{
public:
    SC_HAS_PROCESS(SendXtorSigModule);
    SendXtorSigModule(sc_module_name nm, SidebandSigBridge* bridge_): sc_module(nm){
        bridge = bridge_;
        SC_THREAD(send_xtor_thread);
    }
    ~SendXtorSigModule(){

    }

    void send_xtor_thread(){
        while(true){
            wait(send_xtor_sig_event);
            block = true;
            for(auto &i: send_xtor_list){
                bridge->HandleRequest(i);
            }
            block = false;
        }
    }


    SidebandSigBridge* bridge;
    vector<xtor_sig_transaction_item*> send_xtor_list;
    async_event send_xtor_sig_event;
    bool block;
};


class SidebandSigTest: public testing::Test{
public:
    void SetUp() override{
        
        string channel_name = "sideband_test";
        sig_proxy = make_shared<xtor_sig_proxy>();
        sig_bridge = new SidebandSigBridge("sig_bridge", channel_name, sig_proxy, n_sig_in, n_sig_out);
        send_xtor_sig = new SendXtorSigModule("send_xtor_sig", sig_bridge);
        sig_tlm_proxy = new SidebandSigTlmProxy("sig_tlm_proxy", channel_name, n_sig_in, n_sig_out);
        fake_device = new FakeDevice("fake_device", n_sig_in, n_sig_out);
        sc_stop_module = new ScStopModule("stopmodule");
        
        // connect
        for(int i = 0; i < n_sig_out; i++){
            sc_signal<bool>* sig = new sc_signal<bool>(("signal_out_" + to_string(i)).c_str());
            fake_device->irq_out[i]->bind(*sig);
            sig_tlm_proxy->sig_out_proxy[i]->bind(*sig);
            v_signal.push_back(sig);
        }
        for(int i = 0; i < n_sig_in; i++){
            sc_signal<bool>* sig = new sc_signal<bool>(("signal_in_" + to_string(i)).c_str());
            fake_device->irq_in[i]->bind(*sig);
            sig_tlm_proxy->sig_in_proxy[i]->bind(*sig);
            v_signal.push_back(sig);
        }
        sim_thread = new thread(
            [](){
                sc_core::sc_start();
            }
        );
    }
    void TearDown() override{
        sc_stop_module->stop_event->notify();
        if(sim_thread->joinable()){
            sim_thread->join();
        }
        delete sim_thread;
        delete send_xtor_sig;
        delete sig_bridge;
        delete sig_tlm_proxy;
        delete sc_stop_module;
        delete fake_device;
        for(auto i: v_signal){
            delete i;
        }
        sc_core::sc_curr_simcontext = new sc_core::sc_simcontext();
        sc_core::sc_default_global_context = nullptr;
    }

    thread* sim_thread;
    SidebandSigBridge* sig_bridge;
    SendXtorSigModule* send_xtor_sig;
    SidebandSigTlmProxy* sig_tlm_proxy;
    shared_ptr<xtor_sig_proxy> sig_proxy;
    FakeDevice* fake_device;
    ScStopModule* sc_stop_module;
    vector<sc_signal<bool>*> v_signal;

    uint32_t n_sig_in{7};
    uint32_t n_sig_out{8};
};

TEST_F(SidebandSigTest, TLM2HW){
    int pos_index = 3;
    for(int i = 0; i < n_sig_out; i++){
        if(i == pos_index){
            fake_device->seq_out.push_back(true);
        }
        else{
            fake_device->seq_out.push_back(false);
        }
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    fake_device->send_seq_out_event.notify();
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    // check
    EXPECT_EQ(sig_proxy->nb_send_list.size(), 1);
    xtor_sig_transaction_item& item = sig_proxy->nb_send_list[0];
    EXPECT_EQ(item.get_signal_width(), 8);
    EXPECT_EQ(*item.signals, (1 << pos_index));

    fake_device->seq_out.clear();
    sig_proxy->nb_send_list.clear();

    for(int i = 0; i < n_sig_out; i++){
        fake_device->seq_out.push_back(false);
    }
    fake_device->send_seq_out_event.notify();
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    // check
    EXPECT_EQ(sig_proxy->nb_send_list.size(), 1);
    xtor_sig_transaction_item& item2 = sig_proxy->nb_send_list[0];
    EXPECT_EQ(item2.get_signal_width(), 8);
    EXPECT_EQ(*item2.signals, 0x0);

}

TEST_F(SidebandSigTest, TLM2HW_MULTI){
    vector<int> pos_index{0, 1, 2, 3, 4, 5, 6, 7};
    for(int i = 0; i < n_sig_out; i++){
        bool high = false;
        for(auto j : pos_index){
            if(i == j){
                high = true;
                break;
            }
        }
        fake_device->seq_out.push_back(high);
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    fake_device->send_seq_out_event.notify();
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    // check
    EXPECT_EQ(sig_proxy->nb_send_list.size(), 1);
    xtor_sig_transaction_item& item = sig_proxy->nb_send_list[0];
    EXPECT_EQ(item.get_signal_width(), 8);
    uint32_t target_val = 0;
    for(auto i: pos_index){
        target_val |= (1 << i);
    }
    EXPECT_EQ(*item.signals, target_val);

    fake_device->seq_out.clear();
    sig_proxy->nb_send_list.clear();

    for(int i = 0; i < n_sig_out; i++){
        fake_device->seq_out.push_back(false);
    }
    fake_device->send_seq_out_event.notify();
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    // check
    EXPECT_EQ(sig_proxy->nb_send_list.size(), 1);
    xtor_sig_transaction_item& item2 = sig_proxy->nb_send_list[0];
    EXPECT_EQ(item2.get_signal_width(), 8);
    EXPECT_EQ(*item2.signals, 0x0);

}

TEST_F(SidebandSigTest, HW2TLM){
    int pos_index = 3;
    xtor_sig_transaction_item* item = new xtor_sig_transaction_item();
    item->set_signal_width(n_sig_in);
    item->signals = new uint32_t;
    (*item->signals) = (1 << pos_index);

    send_xtor_sig->send_xtor_list.push_back(item);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    send_xtor_sig->send_xtor_sig_event.notify();
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    //check
    EXPECT_EQ(send_xtor_sig->block, false);
    EXPECT_EQ(fake_device->seq_in.size(), n_sig_in);
    for(int i = 0; i < n_sig_in; i++){
        bool high = false;
        if(i == pos_index) high = true;
        EXPECT_EQ(fake_device->seq_in[i], high);
        // cout << int(fake_device->seq_in[i]) << " ";
    }
    // cout << endl;

    fake_device->seq_in.clear();
    send_xtor_sig->send_xtor_list.clear();
    xtor_sig_transaction_item* item2 = new xtor_sig_transaction_item();
    item2->set_signal_width(n_sig_in);
    item2->signals = new uint32_t;
    (*item2->signals) = 0x0;
    send_xtor_sig->send_xtor_list.push_back(item2);
    send_xtor_sig->send_xtor_sig_event.notify();
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(send_xtor_sig->block, false);
    EXPECT_EQ(fake_device->seq_in.size(), n_sig_in);
    for(int i = 0; i < n_sig_in; i++){
        EXPECT_EQ(fake_device->seq_in[i], false);
    }


    delete item2->signals;
    delete item2;
    delete item->signals;
    delete item;
}

TEST_F(SidebandSigTest, HW2TLM_MULTI){
    vector<int> pos_index{0, 1, 2, 3, 4, 5, 6};
    xtor_sig_transaction_item* item = new xtor_sig_transaction_item();
    item->set_signal_width(n_sig_in);
    item->signals = new uint32_t;
    for(int i = 0; i < n_sig_in; i++){
        int high = 0;
        for(auto j: pos_index){
            if(i == j){
                high = (1 << i);
                break;
            }
        }
        (*item->signals) |= high;
    }

    send_xtor_sig->send_xtor_list.push_back(item);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    send_xtor_sig->send_xtor_sig_event.notify();
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));

    //check
    EXPECT_EQ(send_xtor_sig->block, false);
    EXPECT_EQ(fake_device->seq_in.size(), n_sig_in);
    for(int i = 0; i < n_sig_in; i++){
        bool high = false;
        for(auto j: pos_index){
            if(j == i){
                high = true;
                break;
            }
        }
        EXPECT_EQ(fake_device->seq_in[i], high);
        // cout << int(fake_device->seq_in[i]) << " ";
    }
    // cout << endl;

    fake_device->seq_in.clear();
    send_xtor_sig->send_xtor_list.clear();
    xtor_sig_transaction_item* item2 = new xtor_sig_transaction_item();
    item2->set_signal_width(n_sig_in);
    item2->signals = new uint32_t;
    (*item2->signals) = 0x0;
    send_xtor_sig->send_xtor_list.push_back(item2);
    send_xtor_sig->send_xtor_sig_event.notify();
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
    EXPECT_EQ(send_xtor_sig->block, false);
    EXPECT_EQ(fake_device->seq_in.size(), n_sig_in);
    for(int i = 0; i < n_sig_in; i++){
        EXPECT_EQ(fake_device->seq_in[i], false);
    }


    delete item2->signals;
    delete item2;
    delete item->signals;
    delete item;
}






int sc_main(int argc, char* argv[]) {
    return 0;
}