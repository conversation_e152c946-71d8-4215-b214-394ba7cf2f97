# Find dependencies
find_package(quill REQUIRED)

# Source files
set(SSLOGGER_SOURCES
    src/sslogger.cc
)

# Create library
add_library(sslogger ${SSLOGGER_SOURCES})

# Set include directories
target_include_directories(sslogger
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}/include
)

# Link dependencies
target_link_libraries(sslogger
    PUBLIC
        quill::quill
)

# Add compile definitions
target_compile_definitions(sslogger 
    PUBLIC
        QUILL_COMPILE_ACTIVE_LOG_LEVEL=QUILL_COMPILE_ACTIVE_LOG_LEVEL_TRACE_L3
)

# Installation options
option(BUILD_EXAMPLES "Build example programs" OFF)

# Install headers
install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
    DESTINATION ${CMAKE_INSTALL_PREFIX}
    FILES_MATCHING PATTERN "*.h"
    PATTERN "examples" EXCLUDE
)

# Install library and export targets
install(TARGETS sslogger
    EXPORT ssloggerTargets
    LIBRARY DESTINATION ${CMAKE_INSTALL_PREFIX}/${LIB_VERSION}
    ARCHIVE DESTINATION ${CMAKE_INSTALL_PREFIX}/${LIB_VERSION}
    RUNTIME DESTINATION bin
    INCLUDES DESTINATION include
)

#install(EXPORT ssloggerTargets
#    FILE ssloggerTargets.cmake
#    NAMESPACE sslogger::
#    DESTINATION lib/cmake/sslogger
#)

# Create config files
#include(CMakePackageConfigHelpers)
#write_basic_package_version_file(
#    "${CMAKE_CURRENT_BINARY_DIR}/ssloggerConfigVersion.cmake"
#    VERSION ${PROJECT_VERSION}
#    COMPATIBILITY SameMajorVersion
#)

#configure_package_config_file(
#    "${CMAKE_CURRENT_SOURCE_DIR}/cmake/ssloggerConfig.cmake.in"
#    "${CMAKE_CURRENT_BINARY_DIR}/ssloggerConfig.cmake"
#    INSTALL_DESTINATION lib/cmake/sslogger
#)

# Install config files
#install(FILES
#    "${CMAKE_CURRENT_BINARY_DIR}/ssloggerConfig.cmake"
#    "${CMAKE_CURRENT_BINARY_DIR}/ssloggerConfigVersion.cmake"
#    DESTINATION lib/cmake/sslogger
#)

# Add examples
if(BUILD_EXAMPLES)
    add_subdirectory(examples)
endif()

# Enable testing
enable_testing()
#add_subdirectory(tests)
