#ifndef SSLN_SSLOGGER_STD_H_
#define SSLN_SSLOGGER_STD_H_

// Include standard type support from Quill
#include "quill/std/Array.h"
#include "quill/std/Chrono.h"
#include "quill/std/Deque.h"
#include "quill/std/List.h"
#include "quill/std/Map.h"
#include "quill/std/Optional.h"
#include "quill/std/Pair.h"
#include "quill/std/Set.h"
#include "quill/std/String.h"
#include "quill/std/StringView.h"
#include "quill/std/Tuple.h"
#include "quill/std/Variant.h"
#include "quill/std/Vector.h"

#if defined(_WIN32)
#include "quill/std/WideString.h"
#endif

#endif  // SSLN_SSLOGGER_STD_H_ 