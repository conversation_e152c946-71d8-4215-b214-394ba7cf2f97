CURRENT_DIR=$(pwd)

module load cmake/3.19.6
module unload gcc/9.3.0
module load gcc/10.3.0


cd $CURRENT_DIR
mkdir -p ./install



# compile and install cpp-ipc
cd $CURRENT_DIR
cd cpp-ipc && mkdir -p build && cd build
cmake -DCMAKE_INSTALL_PREFIX=../../install -DCMAKE_CXX_STANDARD=17 -DLIBIPC_BUILD_SHARED_LIBS=ON ..
make -j8 && make install



#compile and install googletest for UT
cd $CURRENT_DIR
cd googletest && mkdir -p build && cd build
cmake -DCMAKE_INSTALL_PREFIX=../../install -DCMAKE_CXX_STANDARD=17 -DBUILD_SHARED_LIBS=ON ..
make -j8 && make install



cd $CURRENT_DIR
mkdir -p ./fake_install && mkdir -p ./fake_install/lib
cd axi_xtor
g++ -fPIC -Wall -Wextra -O2 -std=c++17 -shared -I./include src/*.cc -o libfake_xtor.so
cp libfake_xtor.so ../fake_install/lib && cp ./include ../fake_install -r

cd $CURRENT_DIR
cd quill && mkdir -p build && cd build
cmake -DCMAKE_INSTALL_PREFIX=../../install -DCMAKE_CXX_STANDARD=17 -DBUILD_SHARED_LIBS=ON ..
make && make install

cd $CURRENT_DIR