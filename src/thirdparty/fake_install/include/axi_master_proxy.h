#ifndef AXI_MASTER_PROXY_H_H_
#define AXI_MASTER_PROXY_H_H_

#include "axi_transaction_item.h"
#include <functional>
#include <unordered_map>
#include <string>


namespace umi_axi {

class axi_master_proxy {
public:
    virtual ~axi_master_proxy() = default;

    void b_send_transaction(axi_transaction_item& trans);

    axi_master_proxy(int data_width_, spec_version_t spec_ver_, int axlen_width_=8):data_bus_width(data_width_), axi_spec_type(spec_ver_), len_width(axlen_width_) {
    }

    axi_master_proxy(std::string nm,std::string bfm_hdl_path){

    }

    template<typename T>
    void setHandler(const std::string& event, T* instance, void (T::*handler)(void*)) {
        handlers_[event] = [instance, handler](void* data) {
            (instance->*handler)(data);
        };
    }

    void triggerEvent(const std::string& event, void* data) {
        auto it = handlers_.find(event);
        if (it != handlers_.end()) {
            it->second(data);
        }
    }
    int get_data_width() const {
        return data_bus_width;
    }
    spec_version_t get_spec_version() const {
        return axi_spec_type;
    }
    int get_axlen_width() const {
        return len_width;
    }
    int get_axsize_width() const {
        return axsize_width;
    }

    void xtor_start_run() {
    }
    void wait_reset_deassert(){
        
    }

public:
    int data_bus_width;
    spec_version_t axi_spec_type;
    int len_width;
    int axsize_width;

    uint32_t req_user_width{128};
    uint32_t resp_w_user_width{16};
    uint32_t data_w_user_width{128};
    uint32_t data_r_user_width{128};

    axi_transaction_item wr_trans, rd_trans;
    std::unordered_map<std::string, std::function<void(void*)>> handlers_;
    std::vector<axi_transaction_item> b_send_lists;
};

} // umi_axi

#endif /* end of include guard: AXI_MASTER_PROXY_H_H_ */
