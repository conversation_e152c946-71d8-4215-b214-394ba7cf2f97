#ifndef AXI_MASTER_PROXY_H_H_
#define AXI_MASTER_PROXY_H_H_

#include "axi_transaction_item.h"
#include <functional>
#include <unordered_map>
#include <string>
#include <atomic>


namespace umi_axi {

class axi_slave_proxy {
public:
    virtual ~axi_slave_proxy() = default;

    virtual void b_send_transaction(axi_transaction_item& item) {
        
    }
    void wait_reset_deassert(){
        
    }
    axi_slave_proxy(int data_width_, spec_version_t spec_ver_, int axlen_width_=8)
        : data_width(data_width_), spec_ver(spec_ver_), axlen_width(axlen_width_) {}

    axi_slave_proxy(std::string name, std::string bfm_hdl_path, bool flag, uint32_t base_addr, uint32_t size) {

    }

    template<typename T>
    void setHandler(const std::string& event, T* instance, void (T::*handler)(void*)) {
        setHandlerImpl(event, [instance, handler](void* data) {
            (instance->*handler)(data);
        });
    }

    virtual void triggerEvent(const std::string& event, void* data) {
        auto it = handlers_.find(event);
        if (it != handlers_.end()) {
            it->second(data);
        }
    }

    int get_data_width() const {
        return data_width;
    }

    spec_version_t get_spec_version() const {
        return spec_ver;
    }

    int get_axlen_width() const {
        return axlen_width;
    }

    void register_after_recv_trans_cb(void (*const func)(void *));
    virtual void send_response(axi_transaction_item& trans);

    void xtor_start_run();

    std::vector<axi_transaction_item> response_lists_;
    std::atomic<int> resp_cnt_{0};

protected:
    virtual void setHandlerImpl(const std::string& event, std::function<void(void*)> handler) {
        handlers_[event] = std::move(handler);
    }

private:
    int data_width;
    spec_version_t spec_ver;
    int axlen_width;

    axi_transaction_item wr_trans, rd_trans;
    mutable std::unordered_map<std::string, std::function<void(void*)>> handlers_;
};

} // umi_axi

#endif /* end of include guard: AXI_MASTER_PROXY_H_H_ */
