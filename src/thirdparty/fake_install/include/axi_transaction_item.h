#ifndef AXI_TRANSACTION_ITEM_H
#define AXI_TRANSACTION_ITEM_H

#include <cstdint>
#include <functional>
#include <vector>
#include <iostream>
#include <iomanip>

using namespace std;

namespace umi_axi {


enum lock_type_t {
    NORMAL = 0,
    EXCLUSIVE=1,
    LOCKED=2,
    RESERVED_LOCK=3
};
enum domain_t {
  NON_SHAREABLE = 0,
  INNER = 1,
  OUTER = 2,
  SYSTEM = 3
};
enum barrier_t {
  NORMAL_RESPECTING,
  MEMORY_BARRIER,
  NORMAL_IGNORING,
  SYNC_BARRIER
};
enum spec_version_t { AMBA3 = 3, AMBA4 = 4, AMBA5 = 5 };
enum cmd_type_t { WRITE = 0, READ = 1 };
enum burst_type_t { FIXED = 0, INCR = 1, WRAP = 2 };
enum response_type_t { OKAY = 0, EXOKAY = 1, SLVERR = 2, DECERR = 3 };
enum size_type_t {
  BYTE_1 = 0,
  BYTE_2 = 1,
  BYTE_4 = 2,
  BYTE_8 = 3,
  BYTE_16 = 4,
  BYTE_32 = 5,
  BYTE_64 = 6,
  BYTE_128 = 7
};
enum bus_width_t {BIT_8=8,BIT_16=16,BIT_32=32,BIT_64=64,BIT_128=128,BIT_256=256,BIT_512=512,BIT_1024=1024};

enum channel_delay_t { aw_delay, w_delay, b_delay, ar_delay, r_delay };
enum event_type_t {
  reset_deassert_ev = 0,
  timer_expired_ev = 1,
  wr_resp_ev = 2,
  rd_resp_ev = 3
};

const char* enumToStr(burst_type_t c); 

class axi_transaction_item  {
public:
    struct {
		uint32_t msg_id;
		uint8_t  pkt_tail; // means the last pkt generated from One "msg-id" gp
	} commu_tag;

    //aw || ar channel
    cmd_type_t cmd; //read or write
    uint64_t addr;
    uint32_t id;
    burst_type_t burst;
    size_type_t size;
    uint8_t len;
    lock_type_t lock;
    uint8_t prot;
    uint8_t cache;
    uint8_t qos;
    uint8_t region;
    vector<uint8_t>					req_user;
    domain_t domain;
    uint32_t snoop;
    barrier_t bar;

    //data channel
    vector<unsigned char> data;
    vector<unsigned char> be; //bit enable
    vector<uint8_t> data_user;
    vector<uint8_t> last;

    //resp field
    vector<response_type_t> resp;//bresp and rresp
    vector<uint8_t> resp_user;

    //width field
    uint32_t addr_width; //V2
    uint32_t len_width; //V2
    uint32_t lock_width; //V2
    bus_width_t data_width;
    uint32_t id_width;
    uint32_t                        req_user_width;
    uint32_t                        resp_w_user_width;
    uint32_t                        data_w_user_width;
    uint32_t                        data_r_user_width;

    //delay field
    bool enable_delay;
    uint32_t delay_width;
    uint32_t cmd_valid_delay;//awvlid delay,arvalid delay
    vector<uint32_t> data_valid_delay;//wvalid delay
    spec_version_t spec_ver;
    bool is_finished;
    bool wr_sop;
    bool ar_sop;

    axi_transaction_item() {
      reset();
    }
    ~axi_transaction_item() =default;

    axi_transaction_item(const axi_transaction_item& trans) {
      axi_transaction_item::do_copy(trans);
    }

    axi_transaction_item& operator = (const axi_transaction_item& trans) {
      if(this != &trans) {
        axi_transaction_item::do_copy(trans);
      }
      return *this;
    }

    void do_copy(const axi_transaction_item& trans);
    virtual void reset();
    virtual void process_req_field(bool pack_t=true);
    virtual void process_data_field(bool pack_t=true);
    virtual void process_resp_field(bool pack_t=true);
    void set_width_field(bool en_delay, uint32_t dly_width, uint32_t dt_width, uint32_t idx_width, uint32_t usr_width, uint32_t ver, uint32_t a_width=64, uint32_t l_width=8, uint32_t lk_width=2);

    void set_delay(bool dly_en  = false, uint32_t width=8);
    void set_data_width(uint32_t width);
    void set_addr_width(uint32_t width);
    void set_id_width(uint32_t width);
    void set_len_width(uint32_t width);
    void set_lock_width(uint32_t width);
    void set_ver_type(uint32_t ver);
    void set_user_width(uint32_t width);

    void field_check();


    void print() const {
        std::cout << "AXI Transaction Item:" << std::endl;
        std::cout << "  Data: [";
        for (size_t i = 0; i < data.size(); ++i) {
            std::cout << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(data[i]);
            if (i < data.size() - 1) {
                std::cout << ", ";
            }
        }
        std::cout << "]" << std::endl;

        std::cout << "  Byte Enable: [";
        for (size_t i = 0; i < be.size(); ++i) {
            std::cout << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(be[i]);
            if (i < be.size() - 1) {
                std::cout << ", ";
            }
        }
        std::cout << "]" << std::endl;

        std::cout << "  Size: " << static_cast<int>(size) << std::endl;
        std::cout << "  Length: " << len << std::endl;
        std::cout << "  Address: 0x" << std::hex << addr << std::endl;
        std::cout << "  ID: " << id << std::endl;
        std::cout << "  Command: " << (cmd == cmd_type_t::READ ? "READ" : "WRITE") << std::endl;
        std::cout << "  Burst: " << (burst == burst_type_t::INCR ? "INCR" : "UNKNOWN") << std::endl;
    }

    // axi_transaction_item(cmd_type_t cmd, uint64_t addr, id_t id, burst_type_t burst, size_type_t size, uint8_t len, lock_type_t lock, uint8_t prot, uint8_t cache, uint8_t qos, uint8_t region, uint32_t req_user, domain_t domain, uint32_t snoop, barrier_t bar, const std::vector<uint8_t>& data, const std::vector<uint8_t>& be, const std::vector<uint8_t>& addr_user, const std::vector<uint32_t>& resp_user, uint32_t addr_width, uint32_t len_width, uint32_t lock_width, bus_width_t data_width, uint32_t id_width, uint32_t user_width, bool enable_delay, uint32_t delay_width, uint32_t cmd_valid_delay, spec_version_t spec_ver, bool is_finished, bool wr_sop, bool ar_sop)
    //     : cmd(cmd), addr(addr), id(id), burst(burst), size(size), len(len), lock(lock), prot(prot), cache(cache), qos(qos), region(region), req_user(req_user), domain(domain), snoop(snoop), bar(bar), data(data), be(be),  resp_user(resp_user), addr_width(addr_width), len_width(len_width), lock_width(lock_width), data_width(data_width), id_width(id_width), user_width(user_width), enable_delay(enable_delay), delay_width(delay_width), cmd_valid_delay(cmd_valid_delay),spec_ver(spec_ver), is_finished(is_finished), wr_sop(wr_sop), ar_sop(ar_sop) {}
};

}

#endif // AXI_TRANSACTION_ITEM_H
