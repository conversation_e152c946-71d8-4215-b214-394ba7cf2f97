#ifndef XTOR_SIG_PROXY_H
#define XTOR_SIG_PROXY_H

#include "xtor_sig_transaction_item.h"
#include <vector>
#include <functional>
#include <string>
#include <unordered_map>
namespace umi_sig {


class xtor_sig_proxy{
public:
    xtor_sig_proxy(){

    }
    xtor_sig_proxy(std::string nm,std::string bfm_hdl_path){

    }
    ~xtor_sig_proxy(){

    }

    void xtor_start_run(){

    }

    void nb_send_transaction(xtor_sig_transaction_item& trans){
        nb_send_list.push_back(trans);
    }

    template<typename T>
    void setHandler(const std::string& event, T* instance, void (T::*handler)(void*)) {
        setHandlerImpl(event, [instance, handler](void* data) {
            (instance->*handler)(data);
        });
    }

    virtual void setHandlerImpl(const std::string& event, std::function<void(void*)> handler) {
        handlers_[event] = std::move(handler);
    }
    void wait_reset_deassert(){
        
    }

    std::vector<xtor_sig_transaction_item> nb_send_list;
    mutable std::unordered_map<std::string, std::function<void(void*)>> handlers_;
};


}


#endif