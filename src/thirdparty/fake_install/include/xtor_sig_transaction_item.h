#ifndef XTOR_SIG_TRANSACTION_ITEM_H
#define XTOR_SIG_TRANSACTION_ITEM_H


#include <iostream>
#include <vector>
#include <string>

namespace umi_sig{

class xtor_sig_transaction_item{
public:
    uint32_t* signals; // c2rtl or capture rtl2c new values
    uint32_t* old_signals; // rtl2c old values

    xtor_sig_transaction_item(){

    }
    xtor_sig_transaction_item(const xtor_sig_transaction_item& trans){
        signal_width = trans.get_signal_width();
        uint32_t signal_width_uint = (signal_width + 31)/32;
        signals = new uint32_t [signal_width_uint];
        for(int i = 0; i < signal_width_uint; i++){
            signals[i] = trans.signals[i];
        }
    }

    ~xtor_sig_transaction_item(){
        // delete [] signals;
    }

    void set_signal_width(uint32_t val){
        signal_width = val;
    }

    uint32_t get_signal_width(){
        return signal_width;
    }

    uint32_t get_signal_width() const{
        return signal_width;
    }


private:
    uint32_t signal_width;
    bool rtl2c;


};



}


#endif