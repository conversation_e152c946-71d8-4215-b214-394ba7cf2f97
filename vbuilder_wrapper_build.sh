#!/bin/bash
current_path=$(pwd)


mkdir -p $current_path/vbuilder_wrapper/source/axi_master
mkdir -p $current_path/vbuilder_wrapper/source/axi_slave
mkdir -p $current_path/vbuilder_wrapper/source/sideband

cp -f -r $current_path/src/axi_master/proxy/include $current_path/vbuilder_wrapper/source/axi_master
cp -f -r $current_path/src/axi_master/proxy/src $current_path/vbuilder_wrapper/source/axi_master

cp -f -r $current_path/src/axi_slave/proxy/include $current_path/vbuilder_wrapper/source/axi_slave 
cp -f -r $current_path/src/axi_slave/proxy/src $current_path/vbuilder_wrapper/source/axi_slave

cp -f -r $current_path/src/sideband/proxy/include $current_path/vbuilder_wrapper/source/sideband 
cp -f -r $current_path/src/sideband/proxy/src $current_path/vbuilder_wrapper/source/sideband


uv_scons user_yaml --project=module_import --src_path=$current_path/vbuilder_wrapper/source/axi_master --workspace_home=$current_path/vbuilder_wrapper/build/axi_master --dst_dir=$current_path/vbuilder_wrapper/install/axi_master --gen_libs --systemc=2.3.3.uv --gcc=9.3.0 --sc_cplusplus=201103L --release

uv_scons user_yaml --project=module_import --src_path=$current_path/vbuilder_wrapper/source/axi_slave --workspace_home=$current_path/vbuilder_wrapper/build/axi_slave --dst_dir=$current_path/vbuilder_wrapper/install/axi_slave --gen_libs --systemc=2.3.3.uv --gcc=9.3.0 --sc_cplusplus=201103L --release

uv_scons user_yaml --project=module_import --src_path=$current_path/vbuilder_wrapper/source/sideband --workspace_home=$current_path/vbuilder_wrapper/build/sideband --dst_dir=$current_path/vbuilder_wrapper/install/sideband --gen_libs --systemc=2.3.3.uv --gcc=9.3.0 --sc_cplusplus=201103L --release

